import numpy as np
from typing import List, Dict
from TradingSystem import Bar

class TA:
    # Cache for expensive calculations
    _cache: Dict[str, float] = {}

    @staticmethod
    def _get_column_data(data: List[Bar], column: str) -> np.ndarray:
        """Extract column data as numpy array for vectorized operations."""
        if column == "Open":
            return np.array([bar.Open for bar in data])
        elif column == "High":
            return np.array([bar.High for bar in data])
        elif column == "Low":
            return np.array([bar.Low for bar in data])
        elif column == "Close":
            return np.array([bar.Close for bar in data])
        elif column == "Volume":
            return np.array([bar.Volume for bar in data])
        else:
            raise ValueError(f"Invalid column name: {column}")

    @staticmethod
    def _cache_key(func_name: str, bar: int, column: str, window: int, data_len: int) -> str:
        """Generate cache key for memoization."""
        return f"{func_name}_{bar}_{column}_{window}_{data_len}"

    @staticmethod
    def clear_cache():
        """Clear the calculation cache."""
        TA._cache.clear()

    @staticmethod
    def SMA(bar: int, data: List[Bar], column: str, window: int) -> float:
        """Optimized Simple Moving Average with caching."""
        if window <= 0 or window > len(data) or bar < window - 1:
            raise ValueError("Invalid window size or insufficient data")

        # Check cache first
        cache_key = TA._cache_key("SMA", bar, column, window, len(data))
        if cache_key in TA._cache:
            return TA._cache[cache_key]

        # Vectorized calculation
        column_data = TA._get_column_data(data, column)
        result = float(np.mean(column_data[bar - window + 1:bar + 1]))

        # Cache result
        TA._cache[cache_key] = result
        return result

    @staticmethod
    def RSI(bar: int, data: List[Bar], column: str, window: int) -> float:
        """Optimized RSI calculation with vectorized operations."""
        if window <= 0 or window > len(data) or bar < window:
            raise ValueError("Invalid window size or insufficient data")

        # Check cache first
        cache_key = TA._cache_key("RSI", bar, column, window, len(data))
        if cache_key in TA._cache:
            return TA._cache[cache_key]

        # Vectorized calculation
        column_data = TA._get_column_data(data, column)

        # Calculate price differences
        start_idx = bar - window
        end_idx = bar + 1
        prices = column_data[start_idx:end_idx]
        differences = np.diff(prices)

        # Separate gains and losses
        gains = np.where(differences > 0, differences, 0)
        losses = np.where(differences < 0, -differences, 0)

        # Calculate average gain and loss
        avg_gain = np.mean(gains)
        avg_loss = np.mean(losses)

        # Calculate RSI
        if avg_loss == 0:
            result = 100.0
        else:
            rs = avg_gain / avg_loss
            result = 100.0 - (100.0 / (1.0 + rs))

        # Cache result
        TA._cache[cache_key] = result
        return result

    @staticmethod
    def ADX(bar: int, data: List[Bar], window: int) -> float:
        if window <= 0 or window > len(data):
            raise ValueError("Invalid window size")

        # Calculate +DM, -DM, and True Range (TR) for each bar
        plus_dm = np.zeros(len(data))
        minus_dm = np.zeros(len(data))
        true_range = np.zeros(len(data))

        for i in range(1, bar + 1):
            up_move = data[i].High - data[i - 1].High
            down_move = data[i - 1].Low - data[i].Low

            plus_dm[i] = up_move if up_move > down_move and up_move > 0 else 0.0
            minus_dm[i] = down_move if down_move > up_move and down_move > 0 else 0.0

            true_range[i] = max(
                data[i].High - data[i].Low,
                abs(data[i].High - data[i - 1].Close),
                abs(data[i].Low - data[i - 1].Close)
            )

        # Smooth +DM, -DM, and TR using the specified window
        smoothed_plus_dm = np.zeros(len(data))
        smoothed_minus_dm = np.zeros(len(data))
        smoothed_tr = np.zeros(len(data))

        for i in range(window, bar + 1):
            smoothed_plus_dm[i] = smoothed_plus_dm[i - 1] - (smoothed_plus_dm[i - 1] / window) + plus_dm[i]
            smoothed_minus_dm[i] = smoothed_minus_dm[i - 1] - (smoothed_minus_dm[i - 1] / window) + minus_dm[i]
            smoothed_tr[i] = smoothed_tr[i - 1] - (smoothed_tr[i - 1] / window) + true_range[i]

        # Calculate +DI and -DI
        plus_di = np.zeros(len(data))
        minus_di = np.zeros(len(data))

        for i in range(window, bar + 1):
            plus_di[i] = (smoothed_plus_dm[i] / smoothed_tr[i]) * 100
            minus_di[i] = (smoothed_minus_dm[i] / smoothed_tr[i]) * 100

        # Calculate Directional Index (DX)
        dx = np.zeros(len(data))

        for i in range(window, bar + 1):
            di_diff = abs(plus_di[i] - minus_di[i])
            di_sum = plus_di[i] + minus_di[i]
            dx[i] = (di_diff / di_sum) * 100 if di_sum != 0 else 0.0

        # Calculate ADX as the smoothed average of DX
        adx = np.mean(dx[window:bar + 1])

        return adx

    @staticmethod
    def ATR(bar: int, data: List[Bar], window: int) -> float:
        """Optimized Average True Range with vectorized operations."""
        if window <= 0 or window > len(data) or bar < window:
            raise ValueError("Invalid window size or insufficient data")

        # Check cache first
        cache_key = TA._cache_key("ATR", bar, "OHLC", window, len(data))
        if cache_key in TA._cache:
            return TA._cache[cache_key]

        # Vectorized calculation of True Range
        high_data = TA._get_column_data(data, "High")
        low_data = TA._get_column_data(data, "Low")
        close_data = TA._get_column_data(data, "Close")

        # Calculate True Range components vectorized
        end_idx = bar + 1
        start_idx = max(1, bar - window + 1)

        hl = high_data[start_idx:end_idx] - low_data[start_idx:end_idx]
        hc = np.abs(high_data[start_idx:end_idx] - close_data[start_idx-1:end_idx-1])
        lc = np.abs(low_data[start_idx:end_idx] - close_data[start_idx-1:end_idx-1])

        # True Range is the maximum of the three components
        true_range = np.maximum(hl, np.maximum(hc, lc))

        # Calculate ATR as simple moving average of True Range
        result = float(np.mean(true_range))

        # Cache result
        TA._cache[cache_key] = result
        return result

    @staticmethod
    def RollingMax(bar: int, data: List[Bar], column: str, window: int) -> float:
        """Optimized Rolling Maximum with vectorized operations."""
        if window <= 0 or window > len(data) or bar < window - 1:
            raise ValueError("Invalid window size or insufficient data")

        # Check cache first
        cache_key = TA._cache_key("RollingMax", bar, column, window, len(data))
        if cache_key in TA._cache:
            return TA._cache[cache_key]

        # Vectorized calculation
        column_data = TA._get_column_data(data, column)
        result = float(np.max(column_data[bar - window + 1:bar + 1]))

        # Cache result
        TA._cache[cache_key] = result
        return result

    @staticmethod
    def RollingMin(bar: int, data: List[Bar], column: str, window: int) -> float:
        """Optimized Rolling Minimum with vectorized operations."""
        if window <= 0 or window > len(data) or bar < window - 1:
            raise ValueError("Invalid window size or insufficient data")

        # Check cache first
        cache_key = TA._cache_key("RollingMin", bar, column, window, len(data))
        if cache_key in TA._cache:
            return TA._cache[cache_key]

        # Vectorized calculation
        column_data = TA._get_column_data(data, column)
        result = float(np.min(column_data[bar - window + 1:bar + 1]))

        # Cache result
        TA._cache[cache_key] = result
        return result