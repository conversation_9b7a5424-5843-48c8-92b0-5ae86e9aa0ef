"""
Constants and configuration for PyTS GUI
"""

# Application constants
APP_TITLE = "PyTS - Professional Trading System"
APP_VERSION = "2.0.0"
DEFAULT_GEOMETRY = "1400x900"

# Theme colors
DARK_THEME = {
    'bg': '#2b2b2b',
    'fg': '#ffffff',
    'select_bg': '#404040',
    'select_fg': '#ffffff',
    'field_bg': '#3c3c3c',
    'button_bg': '#404040',
    'button_fg': '#ffffff',
    'entry_bg': '#3c3c3c',
    'entry_fg': '#ffffff'
}

# Bar size options
BAR_SIZE_OPTIONS = [
    "1 Minute",
    "5 Minutes", 
    "15 Minutes",
    "30 Minutes",
    "1 Hour",
    "4 Hours",
    "Daily",
    "Weekly",
    "Monthly"
]

# Pandas frequency mapping
FREQUENCY_MAP = {
    "1 Minute": "1T",
    "5 Minutes": "5T",
    "15 Minutes": "15T", 
    "30 Minutes": "30T",
    "1 Hour": "1H",
    "4 Hours": "4H",
    "Daily": "1D",
    "Weekly": "1W",
    "Monthly": "1M"
}

# Timeframe options
TIMEFRAME_OPTIONS = [
    "Custom",
    "Last 30 Days",
    "Last 90 Days",
    "Last 3 Months",
    "Last 6 Months",
    "Last Year",
    "Last 2 Years",
    "Last 3 Years",
    "Last 5 Years",
    "Last 10 Years",
    "All Data"
]

# CSV column mappings
CSV_COLUMN_MAPPINGS = {
    'date_columns': ['Date', 'DateTime', 'Timestamp', 'Time'],
    'required_columns': ['Date', 'Open', 'High', 'Low', 'Close', 'Volume'],
    'optional_columns': ['Adj Close', 'OpenInt', 'OpenInterest']
}

# Performance metrics configuration
METRICS_CONFIG = {
    'currency_metrics': ['total_profit', 'gross_profit', 'gross_loss', 'avg_win', 'avg_loss', 'max_drawdown'],
    'percentage_metrics': ['win_rate', 'loss_rate', 'avg_profit_pct', 'avg_win_pct', 'avg_loss_pct'],
    'number_metrics': ['total_trades', 'wins', 'losses', 'max_consecutive_wins', 'max_consecutive_losses'],
    'decimal_metrics': ['profit_factor', 'recovery_factor', 'payoff_ratio', 'sharpe_ratio', 'calmar_ratio', 'sortino_ratio', 'ddr_ratio', 'avg_bars_held']
}

# Optimization settings
OPTIMIZATION_CONFIG = {
    'bayesian_iterations': 50,  # Number of Bayesian optimization iterations
    'random_search_iterations': 30,  # Fallback random search iterations
    'progress_update_interval': 5,
    'default_metric': 'total_profit'
}

# Optimization metric options
OPTIMIZATION_METRICS = [
    ("Total Profit", "total_profit"),
    ("Profit Factor", "profit_factor"),
    ("Sharpe Ratio", "sharpe_ratio"),
    ("Calmar Ratio", "calmar_ratio"),
    ("Sortino Ratio", "sortino_ratio"),
    ("DDR Ratio", "ddr_ratio"),
    ("Recovery Factor", "recovery_factor"),
    ("Win Rate", "win_rate"),
    ("Payoff Ratio", "payoff_ratio")
]

# File extensions
SUPPORTED_FILE_TYPES = [
    ("CSV files", "*.csv"),
    ("Text files", "*.txt"),
    ("All files", "*.*")
]

# Strategy file template
STRATEGY_TEMPLATE = '''"""
New Trading Strategy Template
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
from Tools import Tools
from TradingSystem import TradingSystem, Bar, Trade, MatchedTrade
from TA import TA

class Params:
    training_months = 3
    testing_months = 1

    param_ranges = {
        "param1": (1, 10),
        "param2": (5, 20),
    }

    current_params = {
        "param1": 5,
        "param2": 10,
    }

    bPlot = False

class MyTradingSystem(TradingSystem):
    def __init__(self, bars, resampled_bars=None):
        super().__init__(bars, resampled_bars)

    def process_bar(self, bar):
        # Implement your trading logic here
        pass
'''

# Error messages
ERROR_MESSAGES = {
    'no_data': "No data loaded. Please load data first.",
    'no_strategy': "No strategy selected. Please select a strategy first.",
    'invalid_csv': "Invalid CSV format. Please ensure your file has the required columns: Date, Open, High, Low, Close, Volume",
    'optimization_failed': "Optimization failed. Please check your strategy parameters.",
    'backtest_failed': "Backtest failed. Please check your data and strategy.",
    'file_not_found': "File not found. Please check the file path.",
    'invalid_date_range': "Invalid date range. Please check your start and end dates."
}

# Success messages
SUCCESS_MESSAGES = {
    'data_loaded': "Data loaded successfully",
    'strategy_loaded': "Strategy loaded successfully", 
    'backtest_complete': "Backtest completed successfully",
    'optimization_complete': "Optimization completed successfully",
    'file_saved': "File saved successfully"
}

# Default values
DEFAULTS = {
    'train_period': 3,
    'test_period': 1,
    'period_unit': 'months',
    'bar_size': 'Daily',
    'timeframe': 'Custom',
    'walk_forward': False,
    'use_full_range': False
}
