"""
Theme management for PyTS GUI
Handles dark theme application and widget styling
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any
import logging

from .constants import DARK_THEME

logger = logging.getLogger(__name__)

class ThemeManager:
    """Manages application theming and widget styling"""
    
    def __init__(self, root: tk.Tk):
        self.root = root
        self.current_theme = DARK_THEME
        self.styled_widgets = []
        
    def setup_dark_theme(self) -> None:
        """Configure dark theme for the application"""
        try:
            # Configure root window
            self.root.configure(bg=self.current_theme['bg'])

            # Configure ttk styles
            style = ttk.Style()

            # Try to use a theme that supports better customization
            try:
                style.theme_use('clam')  # clam theme is more customizable
            except:
                pass  # Use default theme if clam not available

            # Configure Notebook (tabs)
            style.configure('TNotebook',
                          background=self.current_theme['bg'],
                          borderwidth=0)
            style.configure('TNotebook.Tab',
                          background=self.current_theme['button_bg'],
                          foreground=self.current_theme['button_fg'],
                          padding=[20, 8],
                          borderwidth=0)
            style.map('TNotebook.Tab',
                     background=[('selected', self.current_theme['select_bg']),
                               ('active', self.current_theme['button_bg'])])
                               
            # Configure Frame
            style.configure('TFrame', background=self.current_theme['bg'])
            style.configure('TLabelFrame', 
                          background=self.current_theme['bg'],
                          foreground=self.current_theme['fg'],
                          borderwidth=1)
                          
            # Configure Labels
            style.configure('TLabel',
                          background=self.current_theme['bg'],
                          foreground=self.current_theme['fg'])
                          
            # Configure Buttons
            style.configure('TButton',
                          background=self.current_theme['button_bg'],
                          foreground=self.current_theme['button_fg'],
                          borderwidth=1,
                          focuscolor='none',
                          relief='flat')
            style.map('TButton',
                     background=[('active', self.current_theme['select_bg']),
                               ('pressed', self.current_theme['select_bg']),
                               ('!active', self.current_theme['button_bg'])],
                     foreground=[('active', self.current_theme['button_fg']),
                               ('pressed', self.current_theme['button_fg']),
                               ('!active', self.current_theme['button_fg'])])
                               
            # Configure Entry widgets
            style.configure('TEntry',
                          fieldbackground=self.current_theme['entry_bg'],
                          foreground=self.current_theme['entry_fg'],
                          borderwidth=1,
                          insertcolor=self.current_theme['entry_fg'])
                          
            # Configure Combobox
            style.configure('TCombobox',
                          fieldbackground=self.current_theme['entry_bg'],
                          foreground=self.current_theme['entry_fg'],
                          background=self.current_theme['button_bg'],
                          borderwidth=1)
            style.map('TCombobox',
                     fieldbackground=[('readonly', self.current_theme['entry_bg'])],
                     selectbackground=[('readonly', self.current_theme['select_bg'])])
                     
            # Configure Checkbutton
            style.configure('TCheckbutton',
                          background=self.current_theme['bg'],
                          foreground=self.current_theme['fg'],
                          focuscolor='none')
                          
            # Configure Progressbar
            style.configure('TProgressbar',
                          background=self.current_theme['select_bg'],
                          troughcolor=self.current_theme['field_bg'],
                          borderwidth=0)
                          
            # Configure Treeview
            style.configure('Treeview',
                          background=self.current_theme['field_bg'],
                          foreground=self.current_theme['fg'],
                          fieldbackground=self.current_theme['field_bg'],
                          borderwidth=1)
            style.configure('Treeview.Heading',
                          background=self.current_theme['button_bg'],
                          foreground=self.current_theme['button_fg'],
                          borderwidth=1)
            style.map('Treeview',
                     background=[('selected', self.current_theme['select_bg'])],
                     foreground=[('selected', self.current_theme['select_fg'])])
                     
            # Configure Scrollbar
            style.configure('Vertical.TScrollbar',
                          background=self.current_theme['button_bg'],
                          troughcolor=self.current_theme['field_bg'],
                          borderwidth=1,
                          arrowcolor=self.current_theme['fg'])
            style.configure('Horizontal.TScrollbar',
                          background=self.current_theme['button_bg'],
                          troughcolor=self.current_theme['field_bg'],
                          borderwidth=1,
                          arrowcolor=self.current_theme['fg'])
                          
            logger.info("Dark theme configured successfully")
            
        except Exception as e:
            logger.error(f"Error setting up dark theme: {str(e)}")
            
    def apply_dark_theme_to_widget(self, widget) -> None:
        """Apply dark theme to a specific widget"""
        try:
            widget_class = widget.winfo_class()
            
            if widget_class == 'Text':
                widget.configure(
                    bg=self.current_theme['field_bg'],
                    fg=self.current_theme['fg'],
                    insertbackground=self.current_theme['fg'],
                    selectbackground=self.current_theme['select_bg'],
                    selectforeground=self.current_theme['select_fg'],
                    borderwidth=1,
                    relief='solid'
                )
            elif widget_class == 'Listbox':
                widget.configure(
                    bg=self.current_theme['field_bg'],
                    fg=self.current_theme['fg'],
                    selectbackground=self.current_theme['select_bg'],
                    selectforeground=self.current_theme['select_fg'],
                    borderwidth=1,
                    relief='solid'
                )
            elif widget_class == 'Canvas':
                widget.configure(
                    bg=self.current_theme['field_bg'],
                    highlightthickness=0
                )
            elif widget_class == 'Frame':
                widget.configure(bg=self.current_theme['bg'])
            elif widget_class == 'Label':
                widget.configure(
                    bg=self.current_theme['bg'],
                    fg=self.current_theme['fg']
                )
            elif widget_class == 'Button':
                widget.configure(
                    bg=self.current_theme['button_bg'],
                    fg=self.current_theme['button_fg'],
                    activebackground=self.current_theme['select_bg'],
                    activeforeground=self.current_theme['select_fg'],
                    borderwidth=1,
                    relief='solid'
                )
            elif widget_class == 'Entry':
                widget.configure(
                    bg=self.current_theme['entry_bg'],
                    fg=self.current_theme['entry_fg'],
                    insertbackground=self.current_theme['entry_fg'],
                    selectbackground=self.current_theme['select_bg'],
                    selectforeground=self.current_theme['select_fg'],
                    borderwidth=1,
                    relief='solid'
                )
            elif widget_class == 'Scrollbar':
                widget.configure(
                    bg=self.current_theme['button_bg'],
                    troughcolor=self.current_theme['field_bg'],
                    activebackground=self.current_theme['select_bg']
                )
                
            # Track styled widgets
            if widget not in self.styled_widgets:
                self.styled_widgets.append(widget)
                
        except Exception as e:
            logger.warning(f"Could not apply theme to widget {widget}: {str(e)}")
            
    def apply_theme_to_children(self, parent) -> None:
        """Recursively apply theme to all child widgets"""
        try:
            self.apply_dark_theme_to_widget(parent)
            
            for child in parent.winfo_children():
                self.apply_theme_to_children(child)
                
        except Exception as e:
            logger.warning(f"Error applying theme to children: {str(e)}")
            
    def refresh_theme(self) -> None:
        """Refresh theme for all tracked widgets"""
        try:
            for widget in self.styled_widgets:
                if widget.winfo_exists():
                    self.apply_dark_theme_to_widget(widget)
                    
            logger.info("Theme refreshed for all widgets")
            
        except Exception as e:
            logger.error(f"Error refreshing theme: {str(e)}")
            
    def get_theme_colors(self) -> Dict[str, str]:
        """Get current theme color palette"""
        return self.current_theme.copy()
        
    def update_theme_color(self, color_key: str, color_value: str) -> None:
        """Update a specific theme color"""
        if color_key in self.current_theme:
            self.current_theme[color_key] = color_value
            self.refresh_theme()
            logger.info(f"Updated theme color {color_key} to {color_value}")
        else:
            logger.warning(f"Unknown theme color key: {color_key}")
            
    def reset_theme(self) -> None:
        """Reset theme to default dark theme"""
        self.current_theme = DARK_THEME.copy()
        self.setup_dark_theme()
        self.refresh_theme()
        logger.info("Theme reset to default")
        
    def create_themed_widget(self, widget_class, parent, **kwargs):
        """Create a widget with theme applied"""
        widget = widget_class(parent, **kwargs)
        self.apply_dark_theme_to_widget(widget)
        return widget

    def create_themed_button(self, parent, text="", command=None, **kwargs):
        """Create a properly themed button"""
        # Use tk.Button with manual theming for better control
        button = tk.Button(
            parent,
            text=text,
            command=command,
            bg=self.current_theme['button_bg'],
            fg=self.current_theme['button_fg'],
            activebackground=self.current_theme['select_bg'],
            activeforeground=self.current_theme['select_fg'],
            borderwidth=1,
            relief='raised',
            font=('Segoe UI', 9),
            **kwargs
        )
        return button
