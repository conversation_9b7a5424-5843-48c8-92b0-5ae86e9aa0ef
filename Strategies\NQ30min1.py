import pandas as pd
import numpy as np
from datetime import datetime, time
from Tools import Tools
from TradingSystem import TradingSystem, Bar, Trade, MatchedTrade
from TA import TA

class Params:
    training_months = 3
    testing_months = 1

    param_ranges = {
        "RollMaxPeriod": (2, 9),
        "RollMinPeriod": (2, 15),
        "LXlim": (10, 30),
        "LXlim_BigBar": (10, 30),
        "SXlim": (5, 30),
        "HighThreshold": (4, 20),
        "LowThreshold": (4, 20),
        "HL_Long": (10, 30),
        "HL_Short": (10, 30),
        "BigBar": (3, 30),
    }
    
    current_params = {
        "RollMaxPeriod": 9,
        "RollMinPeriod": 4,
        "LXlim": 12,
        "LXlim_BigBar": 12,
        "SXlim": 10,
        "HighThreshold": 4,
        "LowThreshold": 4,
        "HL_Long": 10,
        "HL_Short": 10,
	"BarBar": 10
    }
    
    LEstop930 = 999999;
    SEstop930 = 0.1;
    bBigBar = False

class MyTradingSystem(TradingSystem):
    def __init__(self, bars, resampled_bars=None):
        super().__init__(bars, resampled_bars)
        #self.Bars = Params.all_bars

    def process_bar(self, bar):
        high = TA.RollingMax(bar, self.Bars, "High", Params.current_params["RollMaxPeriod"])
        low = TA.RollingMin(bar, self.Bars, "Low", Params.current_params["RollMinPeriod"])
    
        if Tools.check_time(self.Bars[bar].Date.time(), "==", "09:30:00"):
            Params.LEstop930 = self.Bars[bar].High
            Params.SEstop930 = self.Bars[bar].Low
        # Exit
        if self.Active_Position() != "":
            if self.Active_Position() == "Long":
                if Tools.check_time(self.Bars[bar].Date.time(), ">=", "11:30:00"):
                    self.sell(bar + 1, 1, "stop", low, "LX")
                    if self.Active_Position() == "Long":
                        if bBigBar:
                    			    self.sell(bar + 1, 1, "limit", self.Bars[bar].Close+ 5*Params.current_params["LXlim"], "LXlim")
                        else:
                            self.sell(bar + 1, 1, "stop", self.Bars[bar].Low, "LX")
                            if self.Active_Position() == "Long":
                         			    self.sell(bar + 1, 1, "limit", self.Bars[bar].Close+ 5*Params.current_params["LXlim_BigBar"], "LXlim")               
                else:
                    self.sell(bar + 1, 1, "stop", Params.SEstop930, "LX930")

            elif self.Active_Position() == "Short":
                if Tools.check_time(self.Bars[bar].Date.time(), ">=", "11:30:00"):
                    self.buy(bar + 1, 1, "stop", high+120, "SX")
                    if self.Active_Position() == "Short":
                        self.buy(bar + 1, 1, "limit", self.Bars[bar].Close - 5*Params.current_params["SXlim"], "SXlim")
                else:
                    self.buy(bar + 1, 1, "stop", Params.LEstop930, "SX930")
        else:
            bBigBar = False
            # Entry 1 (Open range breakout)
            if Tools.check_time(self.Bars[bar].Date.time(), "==", "09:30:00"):
                if self.Bars[bar].Close > self.Bars[bar].Open:
                    self.buy(bar + 1, 1, "stop", self.Bars[bar].High, "LE_ORB")
                elif self.Bars[bar].Close < self.Bars[bar].Open:
                    self.sell(bar + 1, 1, "stop", self.Bars[bar].Low, "SE_ORB")
            else:
                # Entry 2 (Range liquidity)
                if Tools.check_time(self.Bars[bar].Date.time(), "<", "15:30:00"):
                    if (self.Bars[bar].Close-low < 5*Params.current_params["LowThreshold"]) and (high-low>Params.current_params["HL_Long"]):
                        self.buy(bar + 1, 1, "stop", self.Bar[bar].High, "LE_L")
                    elif (high-self.Bars[bar].Close > 5*Params.current_params["HighThreshold"]) and (high-low>Params.current_params["HL_Short"]):
                        self.sell(bar + 1, 1, "stop", self.Bar[bar].Low, "SE_H")
                    # Entry 3 (Big bar reversal long with special exit)
                    elif((TA.RollingMin(bar, self.Bars, "Low", 3)==sel.Bars[bar].Low) and (self.Bars[bar].High-self.Bars[bar].Low>Params.current_params["BigBar"])):
                        bBigBar = True
                        self.buy(bar + 1, 1, "stop", self.Bar[bar].High, "LE_BigBar")			







