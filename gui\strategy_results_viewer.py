"""
Strategy Results Viewer - GUI component for viewing and managing strategy run logs
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import pandas as pd
import logging

from .strategy_results_logger import StrategyResultsLogger

logger = logging.getLogger(__name__)

class StrategyResultsViewer:
    """GUI component for viewing strategy results history"""
    
    def __init__(self, parent_frame: ttk.Frame, theme_manager=None):
        """Initialize the results viewer"""
        self.parent_frame = parent_frame
        self.theme_manager = theme_manager
        self.logger = StrategyResultsLogger()
        
        # Control variables
        self.show_trades_var = tk.BooleanVar(value=True)
        self.selected_run_id = None
        self.results_data = {}  # Store results data separately
        
        self.create_widgets()
        self.refresh_results()
    
    def create_widgets(self):
        """Create the GUI widgets"""
        # Main container
        main_frame = ttk.Frame(self.parent_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control panel
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Refresh button
        ttk.Button(control_frame, text="Refresh", 
                  command=self.refresh_results).pack(side=tk.LEFT, padx=(0, 5))
        
        # Export button
        ttk.Button(control_frame, text="Export to Excel",
                  command=self.export_to_excel).pack(side=tk.LEFT, padx=(0, 5))

        # Export trades button
        ttk.Button(control_frame, text="Export Trades",
                  command=self.export_trades_to_csv).pack(side=tk.LEFT, padx=(0, 5))

        # Clear logs button
        ttk.Button(control_frame, text="Clear All Logs",
                  command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 10))
        
        # Show trades toggle
        ttk.Checkbutton(control_frame, text="Show Trade Details", 
                       variable=self.show_trades_var,
                       command=self.on_show_trades_changed).pack(side=tk.LEFT, padx=(10, 0))
        
        # Results table
        self.create_results_table(main_frame)
        
        # Details panel
        self.create_details_panel(main_frame)
    
    def create_results_table(self, parent):
        """Create the results table"""
        # Table frame
        table_frame = ttk.LabelFrame(parent, text="Strategy Run History")
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # Create Treeview with scrollbars
        tree_frame = ttk.Frame(table_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Define columns
        columns = ('Timestamp', 'Strategy', 'Market', 'Timeframe', 'Profit', 
                  'Trades', 'Win Rate', 'Profit Factor', 'Max DD', 'Sharpe')
        
        self.results_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=10)
        
        # Configure column headings and widths
        column_widths = {
            'Timestamp': 120,
            'Strategy': 100,
            'Market': 80,
            'Timeframe': 100,
            'Profit': 80,
            'Trades': 60,
            'Win Rate': 70,
            'Profit Factor': 80,
            'Max DD': 80,
            'Sharpe': 70
        }
        
        for col in columns:
            self.results_tree.heading(col, text=col)
            self.results_tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack scrollbars and tree
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Bind selection event
        self.results_tree.bind('<<TreeviewSelect>>', self.on_result_selected)
        
        # Apply theme if available
        if self.theme_manager:
            self.theme_manager.apply_dark_theme_to_widget(self.results_tree)
    
    def create_details_panel(self, parent):
        """Create the details panel"""
        # Details frame
        details_frame = ttk.LabelFrame(parent, text="Run Details")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for different detail views
        self.details_notebook = ttk.Notebook(details_frame)
        self.details_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Summary tab
        self.summary_frame = ttk.Frame(self.details_notebook)
        self.details_notebook.add(self.summary_frame, text="Summary")
        
        # Parameters tab
        self.params_frame = ttk.Frame(self.details_notebook)
        self.details_notebook.add(self.params_frame, text="Parameters")
        
        # Metrics tab
        self.metrics_frame = ttk.Frame(self.details_notebook)
        self.details_notebook.add(self.metrics_frame, text="Detailed Metrics")
        
        # Trades tab (conditionally shown)
        self.trades_frame = ttk.Frame(self.details_notebook)
        
        # Create text widgets for each tab
        self.create_text_widgets()
        
        # Apply theme if available
        if self.theme_manager:
            self.theme_manager.apply_dark_theme_to_widget(self.details_notebook)
    
    def create_text_widgets(self):
        """Create text widgets for detail display"""
        # Summary text
        self.summary_text = tk.Text(self.summary_frame, wrap=tk.WORD, height=10)
        summary_scroll = ttk.Scrollbar(self.summary_frame, orient=tk.VERTICAL, command=self.summary_text.yview)
        self.summary_text.configure(yscrollcommand=summary_scroll.set)
        self.summary_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        summary_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Parameters text
        self.params_text = tk.Text(self.params_frame, wrap=tk.WORD, height=10)
        params_scroll = ttk.Scrollbar(self.params_frame, orient=tk.VERTICAL, command=self.params_text.yview)
        self.params_text.configure(yscrollcommand=params_scroll.set)
        self.params_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        params_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Metrics text
        self.metrics_text = tk.Text(self.metrics_frame, wrap=tk.WORD, height=10)
        metrics_scroll = ttk.Scrollbar(self.metrics_frame, orient=tk.VERTICAL, command=self.metrics_text.yview)
        self.metrics_text.configure(yscrollcommand=metrics_scroll.set)
        self.metrics_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        metrics_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Trades text (initially not added to notebook)
        self.trades_text = tk.Text(self.trades_frame, wrap=tk.WORD, height=10)
        trades_scroll = ttk.Scrollbar(self.trades_frame, orient=tk.VERTICAL, command=self.trades_text.yview)
        self.trades_text.configure(yscrollcommand=trades_scroll.set)
        self.trades_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        trades_scroll.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # Apply theme if available
        if self.theme_manager:
            for text_widget in [self.summary_text, self.params_text, self.metrics_text, self.trades_text]:
                self.theme_manager.apply_dark_theme_to_widget(text_widget)
    
    def refresh_results(self):
        """Refresh the results table"""
        try:
            # Clear existing items and data
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)
            self.results_data.clear()

            # Get results from logger
            results = self.logger.get_strategy_history(limit=100)  # Show last 100 runs

            # Populate table
            for result in results:
                # Format timestamp
                timestamp = result.get('timestamp', '')
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        timestamp_str = dt.strftime('%Y-%m-%d %H:%M')
                    except:
                        timestamp_str = timestamp[:16]  # Fallback
                else:
                    timestamp_str = 'Unknown'
                
                # Format values
                profit = f"${result.get('total_profit', 0):.2f}"
                win_rate = f"{result.get('win_rate', 0):.1f}%"
                profit_factor = f"{result.get('profit_factor', 0):.2f}"
                max_dd = f"${result.get('max_drawdown', 0):.2f}"
                sharpe = f"{result.get('sharpe_ratio', 0):.2f}"
                
                # Insert row
                item_id = self.results_tree.insert('', 'end', values=(
                    timestamp_str,
                    result.get('strategy_name', 'Unknown'),
                    result.get('market_symbol', 'Unknown'),
                    result.get('timeframe_type', 'Unknown'),
                    profit,
                    result.get('total_trades', 0),
                    win_rate,
                    profit_factor,
                    max_dd,
                    sharpe
                ))
                
                # Store full result data separately
                self.results_data[item_id] = result
            
            logger.info(f"Refreshed results table with {len(results)} entries")
            
        except Exception as e:
            logger.error(f"Error refreshing results: {str(e)}")
            messagebox.showerror("Error", f"Failed to refresh results: {str(e)}")
    
    def on_result_selected(self, event):
        """Handle result selection"""
        try:
            selection = self.results_tree.selection()
            if not selection:
                return
            
            # Get selected item data
            item_id = selection[0]
            result = self.results_data.get(item_id)
            if result:
                self.selected_run_id = item_id
                self.display_result_details(result)
            
        except Exception as e:
            logger.error(f"Error handling result selection: {str(e)}")
    
    def display_result_details(self, result: Dict[str, Any]):
        """Display detailed information for selected result"""
        try:
            # Clear all text widgets
            for text_widget in [self.summary_text, self.params_text, self.metrics_text, self.trades_text]:
                text_widget.delete(1.0, tk.END)
            
            # Summary information
            summary_info = f"""Strategy Run Summary
{'='*50}

Strategy: {result.get('strategy_name', 'Unknown')}
Market: {result.get('market_symbol', 'Unknown')} ({result.get('data_file', 'Unknown')})
Timestamp: {result.get('timestamp', 'Unknown')}

Timeframe Information:
  Type: {result.get('timeframe_type', 'Unknown')}
  Start Date: {result.get('start_date', 'Unknown')}
  End Date: {result.get('end_date', 'Unknown')}
  Total Bars: {result.get('total_bars', 0):,}
  Bar Size: {result.get('bar_size', 'Unknown')}

Key Performance Metrics:
  Total Profit: ${result.get('total_profit', 0):.2f}
  Total Trades: {result.get('total_trades', 0)}
  Win Rate: {result.get('win_rate', 0):.1f}%
  Profit Factor: {result.get('profit_factor', 0):.2f}
  Max Drawdown: ${result.get('max_drawdown', 0):.2f}
  Sharpe Ratio: {result.get('sharpe_ratio', 0):.2f}
  Calmar Ratio: {result.get('calmar_ratio', 0):.2f}
  Sortino Ratio: {result.get('sortino_ratio', 0):.2f}
  DDR Ratio: {result.get('ddr_ratio', 0):.2f}

Optimization Settings:
  Metric: {result.get('optimization_metric', 'Unknown')}
  Walk Forward: {result.get('walk_forward', False)}
  Train Period: {result.get('train_period', 0)} {result.get('period_unit', '')}
  Test Period: {result.get('test_period', 0)} {result.get('period_unit', '')}
"""
            self.summary_text.insert(tk.END, summary_info)
            
            # Parameters information
            params = result.get('parameters', {})
            params_info = "Strategy Parameters\n" + "="*30 + "\n\n"
            for key, value in params.items():
                params_info += f"{key}: {value}\n"
            self.params_text.insert(tk.END, params_info)
            
            # Detailed metrics
            self.display_detailed_metrics(result)
            
            # Trade details if available and requested
            if self.show_trades_var.get() and result.get('trades'):
                self.display_trade_details(result.get('trades', []))
            
        except Exception as e:
            logger.error(f"Error displaying result details: {str(e)}")
    
    def display_detailed_metrics(self, result: Dict[str, Any]):
        """Display detailed metrics breakdown"""
        try:
            metrics_info = "Detailed Performance Metrics\n" + "="*40 + "\n\n"
            
            # All trades metrics
            all_metrics = result.get('all_trades_metrics', {})
            metrics_info += "ALL TRADES:\n" + "-"*15 + "\n"
            for key, value in all_metrics.items():
                if isinstance(value, (int, float)):
                    if 'rate' in key.lower() or 'ratio' in key.lower():
                        metrics_info += f"  {key}: {value:.2f}\n"
                    elif 'profit' in key.lower() or 'loss' in key.lower() or 'drawdown' in key.lower():
                        metrics_info += f"  {key}: ${value:.2f}\n"
                    else:
                        metrics_info += f"  {key}: {value}\n"
                else:
                    metrics_info += f"  {key}: {value}\n"
            
            # Long trades metrics
            long_metrics = result.get('long_trades_metrics', {})
            if long_metrics:
                metrics_info += "\nLONG TRADES:\n" + "-"*15 + "\n"
                for key, value in long_metrics.items():
                    if isinstance(value, (int, float)):
                        if 'rate' in key.lower() or 'ratio' in key.lower():
                            metrics_info += f"  {key}: {value:.2f}\n"
                        elif 'profit' in key.lower() or 'loss' in key.lower() or 'drawdown' in key.lower():
                            metrics_info += f"  {key}: ${value:.2f}\n"
                        else:
                            metrics_info += f"  {key}: {value}\n"
                    else:
                        metrics_info += f"  {key}: {value}\n"
            
            # Short trades metrics
            short_metrics = result.get('short_trades_metrics', {})
            if short_metrics:
                metrics_info += "\nSHORT TRADES:\n" + "-"*16 + "\n"
                for key, value in short_metrics.items():
                    if isinstance(value, (int, float)):
                        if 'rate' in key.lower() or 'ratio' in key.lower():
                            metrics_info += f"  {key}: {value:.2f}\n"
                        elif 'profit' in key.lower() or 'loss' in key.lower() or 'drawdown' in key.lower():
                            metrics_info += f"  {key}: ${value:.2f}\n"
                        else:
                            metrics_info += f"  {key}: {value}\n"
                    else:
                        metrics_info += f"  {key}: {value}\n"
            
            # Buy & Hold comparison
            bh_metrics = result.get('buy_hold_metrics', {})
            if bh_metrics:
                metrics_info += "\nBUY & HOLD COMPARISON:\n" + "-"*25 + "\n"
                for key, value in bh_metrics.items():
                    if isinstance(value, (int, float)):
                        if 'profit' in key.lower() or 'return' in key.lower():
                            metrics_info += f"  {key}: ${value:.2f}\n"
                        else:
                            metrics_info += f"  {key}: {value:.2f}\n"
                    else:
                        metrics_info += f"  {key}: {value}\n"
            
            self.metrics_text.insert(tk.END, metrics_info)
            
        except Exception as e:
            logger.error(f"Error displaying detailed metrics: {str(e)}")
    
    def display_trade_details(self, trades: List[Dict[str, Any]]):
        """Display individual trade details"""
        try:
            trades_info = f"Trade Details ({len(trades)} trades)\n" + "="*30 + "\n\n"
            
            for i, trade in enumerate(trades, 1):
                trades_info += f"Trade #{i}:\n"
                trades_info += f"  Entry Date: {trade.get('entry_date', 'Unknown')}\n"
                trades_info += f"  Exit Date: {trade.get('exit_date', 'Unknown')}\n"
                trades_info += f"  Type: {trade.get('trade_type', 'Unknown')}\n"
                trades_info += f"  Entry Price: ${trade.get('entry_price', 0):.2f}\n"
                trades_info += f"  Exit Price: ${trade.get('exit_price', 0):.2f}\n"
                trades_info += f"  Quantity: {trade.get('quantity', 0)}\n"
                trades_info += f"  Profit: ${trade.get('profit', 0):.2f}\n"
                trades_info += f"  Bars Held: {trade.get('bars_held', 0)}\n"
                trades_info += f"  Entry Reason: {trade.get('entry_reason', 'Unknown')}\n"
                trades_info += f"  Exit Reason: {trade.get('exit_reason', 'Unknown')}\n"
                trades_info += "\n"
            
            self.trades_text.delete(1.0, tk.END)
            self.trades_text.insert(tk.END, trades_info)
            
        except Exception as e:
            logger.error(f"Error displaying trade details: {str(e)}")
    
    def on_show_trades_changed(self):
        """Handle show trades toggle"""
        if self.show_trades_var.get():
            # Add trades tab
            self.details_notebook.add(self.trades_frame, text="Trades")
        else:
            # Remove trades tab
            try:
                self.details_notebook.forget(self.trades_frame)
            except:
                pass  # Tab might not be added yet
        
        # Refresh details if a result is selected
        if self.selected_run_id:
            selection = self.results_tree.selection()
            if selection:
                self.on_result_selected(None)
    
    def export_to_excel(self):
        """Export results to Excel file"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="Export Strategy Results"
            )
            
            if filename:
                exported_file = self.logger.export_results_to_excel(os.path.basename(filename))
                if exported_file:
                    messagebox.showinfo("Export Complete", f"Results exported to:\n{exported_file}")
                else:
                    messagebox.showerror("Export Failed", "Failed to export results")
        
        except Exception as e:
            logger.error(f"Error exporting to Excel: {str(e)}")
            messagebox.showerror("Export Error", f"Failed to export: {str(e)}")

    def export_trades_to_csv(self):
        """Export trades to CSV file"""
        try:
            # Check if a specific run is selected
            selected_run_id = None
            selection = self.results_tree.selection()
            if selection:
                item = selection[0]
                if item in self.results_data:
                    selected_run_id = self.results_data[item].get('run_id')

            # Ask user if they want to export all trades or just selected run
            if selected_run_id:
                choice = messagebox.askyesnocancel(
                    "Export Trades",
                    f"Export trades for selected run only?\n\n"
                    f"Yes: Export selected run only\n"
                    f"No: Export all trades\n"
                    f"Cancel: Cancel export"
                )
                if choice is None:  # Cancel
                    return
                elif choice:  # Yes - selected run only
                    export_run_id = selected_run_id
                else:  # No - all trades
                    export_run_id = None
            else:
                export_run_id = None

            # Get filename from user
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="Export Strategy Trades"
            )

            if filename:
                exported_file = self.logger.export_trades_to_csv(export_run_id, filename)
                if exported_file:
                    messagebox.showinfo("Export Complete", f"Trades exported to:\n{exported_file}")
                else:
                    messagebox.showerror("Export Failed", "Failed to export trades or no trades found")

        except Exception as e:
            logger.error(f"Error exporting trades to CSV: {str(e)}")
            messagebox.showerror("Export Error", f"Failed to export trades: {str(e)}")

    def clear_logs(self):
        """Clear all strategy logs"""
        try:
            result = messagebox.askyesno(
                "Clear All Logs", 
                "Are you sure you want to clear all strategy run logs?\n\nThis action cannot be undone."
            )
            
            if result:
                # Remove log files
                for file_path in [self.logger.log_file, self.logger.csv_file]:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                
                # Refresh display
                self.refresh_results()
                
                # Clear details
                for text_widget in [self.summary_text, self.params_text, self.metrics_text, self.trades_text]:
                    text_widget.delete(1.0, tk.END)
                
                messagebox.showinfo("Logs Cleared", "All strategy run logs have been cleared.")
        
        except Exception as e:
            logger.error(f"Error clearing logs: {str(e)}")
            messagebox.showerror("Clear Error", f"Failed to clear logs: {str(e)}")
