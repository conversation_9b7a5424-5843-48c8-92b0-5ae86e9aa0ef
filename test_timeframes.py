#!/usr/bin/env python3
"""
Test script to verify the new timeframe options work correctly
"""

from datetime import datetime, timedelta
from gui.constants import TIMEFRAME_OPTIONS

def test_timeframe_calculations():
    """Test the timeframe calculations for the new options"""
    
    # Use a fixed end date for testing
    end_date = datetime(2025, 6, 30)
    
    print("Testing Timeframe Calculations")
    print("=" * 50)
    print(f"End Date: {end_date.strftime('%Y-%m-%d')}")
    print()
    
    # Test all timeframe options
    timeframe_calculations = {
        "Last 30 Days": 30,
        "Last 90 Days": 90,
        "Last 3 Months": 90,  # Approximately 3 months
        "Last 6 Months": 180,
        "Last Year": 365,
        "Last 2 Years": 730,
        "Last 3 Years": 1095,  # 3 * 365
        "Last 5 Years": 1825,  # 5 * 365
        "Last 10 Years": 3650,  # 10 * 365
    }
    
    for timeframe, days in timeframe_calculations.items():
        start_date = end_date - timedelta(days=days)
        print(f"{timeframe:15} | {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} | {days:4} days")
    
    print()
    print("Available Timeframe Options:")
    print("-" * 30)
    for i, option in enumerate(TIMEFRAME_OPTIONS, 1):
        print(f"{i:2}. {option}")
    
    print()
    print("✅ All new timeframe options added successfully!")
    print("   - Last 3 Months")
    print("   - Last 3 Years") 
    print("   - Last 5 Years")
    print("   - Last 10 Years")

def test_data_coverage():
    """Test what kind of data coverage each timeframe provides"""
    
    print("\nData Coverage Analysis")
    print("=" * 50)
    
    # Typical trading days per year (accounting for weekends and holidays)
    trading_days_per_year = 252
    
    timeframes = [
        ("Last 30 Days", 30, "~22 trading days"),
        ("Last 90 Days", 90, "~66 trading days"),
        ("Last 3 Months", 90, "~66 trading days"),
        ("Last 6 Months", 180, "~126 trading days"),
        ("Last Year", 365, "~252 trading days"),
        ("Last 2 Years", 730, "~504 trading days"),
        ("Last 3 Years", 1095, "~756 trading days"),
        ("Last 5 Years", 1825, "~1,260 trading days"),
        ("Last 10 Years", 3650, "~2,520 trading days"),
    ]
    
    print(f"{'Timeframe':<15} | {'Calendar Days':<12} | {'Est. Trading Days':<18}")
    print("-" * 50)
    
    for timeframe, days, trading_days in timeframes:
        print(f"{timeframe:<15} | {days:>11} | {trading_days:<18}")

if __name__ == "__main__":
    test_timeframe_calculations()
    test_data_coverage()
    
    print("\n" + "=" * 50)
    print("🎯 Timeframe Testing Complete!")
    print("   The new timeframe options are ready for use in PyTS GUI")
    print("   Users can now select from 3 months to 10 years of historical data")
