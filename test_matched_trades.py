#!/usr/bin/env python3
"""
Test script to verify MatchedTrade serialization
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from TradingSystem import MatchedTrade
from gui.strategy_results_logger import StrategyResultsLogger
from datetime import datetime
import json

def create_sample_matched_trades():
    """Create sample MatchedTrade objects like those from TradingSystem"""
    trades = []
    
    # Create a long trade
    long_trade = MatchedTrade(
        buy_Date="2024-01-01",
        sell_Date="2024-01-05", 
        matched_quantity=100,
        buy_price=50.0,
        sell_price=55.0,
        profit=500.0,
        buy_order_name="LE",
        sell_order_name="LX",
        trade_type="LONG"
    )
    trades.append(long_trade)
    
    # Create a short trade
    short_trade = MatchedTrade(
        buy_Date="2024-01-10",
        sell_Date="2024-01-08",  # Note: for short trades, sell comes before buy
        matched_quantity=100,
        buy_price=48.0,
        sell_price=52.0,
        profit=400.0,
        buy_order_name="SX",
        sell_order_name="SE",
        trade_type="SHORT"
    )
    trades.append(short_trade)
    
    return trades

def test_matched_trade_serialization():
    """Test serialization of MatchedTrade objects"""
    print("Testing MatchedTrade serialization...")
    
    # Create logger
    logger = StrategyResultsLogger()
    
    # Create sample MatchedTrade objects
    matched_trades = create_sample_matched_trades()
    print(f"Created {len(matched_trades)} MatchedTrade objects")
    
    # Test serialization directly
    serialized_trades = logger._serialize_trades(matched_trades)
    print(f"Serialized {len(serialized_trades)} trades")
    
    # Print details of serialized trades
    for i, trade in enumerate(serialized_trades):
        print(f"\nTrade {i+1}:")
        for key, value in trade.items():
            print(f"  {key}: {value}")
    
    # Test full logging process
    timeframe_info = {
        'type': 'Custom Range',
        'start_date': '2024-01-01',
        'end_date': '2024-01-31',
        'total_bars': 21
    }
    
    parameters = {
        'LE': 4,
        'SE': 3,
        'LX': 2,
        'SX': 1
    }
    
    metrics = {
        'total_profit': 900.0,
        'total_trades': 2,
        'win_rate': 100.0,
        'profit_factor': float('inf'),
        'max_drawdown': 0.0,
        'sharpe_ratio': 2.5,
        'calmar_ratio': float('inf'),
        'sortino_ratio': 3.0,
        'DD_ratio': float('inf')
    }
    
    buy_hold_metrics = {
        'total_profit': 500.0,
        'total_trades': 1,
        'win_rate': 100.0,
        'profit_factor': float('inf'),
        'max_drawdown': -50.0,
        'sharpe_ratio': 1.0,
        'calmar_ratio': 10.0,
        'sortino_ratio': 1.2
    }
    
    additional_info = {
        'bar_size': 'Daily',
        'walk_forward': False,
        'train_period': 0,
        'test_period': 0,
        'period_unit': 'days'
    }
    
    print("\nLogging full strategy run with MatchedTrade objects...")
    run_id = logger.log_strategy_run(
        strategy_name="TestMatchedTrades",
        market_symbol="TESTMATCH",
        data_file="test_matched.csv",
        timeframe_info=timeframe_info,
        parameters=parameters,
        metrics=metrics,
        buy_hold_metrics=buy_hold_metrics,
        optimization_metric="total_profit",
        additional_info=additional_info,
        trades=matched_trades,
        include_trades=True
    )
    
    print(f"Logged run with ID: {run_id}")
    
    # Verify the trades were saved correctly
    print("\nVerifying MatchedTrade serialization...")
    
    # Check JSON file
    json_file = os.path.join("strategy_logs", "strategy_results.json")
    if os.path.exists(json_file):
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        if run_id in data:
            saved_trades = data[run_id].get('trades')
            if saved_trades:
                print(f"✓ Found {len(saved_trades)} MatchedTrades in JSON file")
                for i, trade in enumerate(saved_trades):
                    print(f"  Trade {i+1}: {trade['trade_type']} - Entry: {trade['entry_date']}, Exit: {trade['exit_date']}, Profit: ${trade['profit']}")
            else:
                print("✗ No trades found in JSON file")
        else:
            print(f"✗ Run ID {run_id} not found in JSON file")
    else:
        print("✗ JSON file not found")
    
    print("MatchedTrade serialization test completed!")

if __name__ == "__main__":
    test_matched_trade_serialization()
