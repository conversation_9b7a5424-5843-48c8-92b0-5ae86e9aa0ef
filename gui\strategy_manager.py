"""
Strategy management functionality for PyTS GUI
Handles strategy loading, parameter management, and optimization
"""

import os
import importlib
import sys
import itertools
from typing import Dict, Any, List, Tuple, Optional
import logging

from .constants import OPTIMIZATION_CONFIG, ERROR_MESSAGES

logger = logging.getLogger(__name__)

class StrategyManager:
    """Manages strategy loading, parameters, and optimization"""
    
    def __init__(self):
        self.current_strategy = None
        self.strategy_module = None
        self.parameter_widgets = {}
        self.optimization_results = None
        
    def get_available_strategies(self) -> List[str]:
        """Get list of available strategy files"""
        strategies = []
        strategies_dir = "Strategies"
        
        if os.path.exists(strategies_dir):
            for file in os.listdir(strategies_dir):
                if file.endswith('.py') and not file.startswith('__'):
                    strategy_name = file[:-3]  # Remove .py extension
                    strategies.append(strategy_name)
                    
        return sorted(strategies)
        
    def load_strategy(self, strategy_name: str) -> Dict[str, Any]:
        """
        Load strategy module and extract parameters
        
        Args:
            strategy_name: Name of strategy file (without .py)
            
        Returns:
            Dictionary containing strategy info and parameters
        """
        try:
            # Force reload of strategy module
            module_name = f"Strategies.{strategy_name}"
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])
                
            self.strategy_module = importlib.import_module(module_name)
            self.current_strategy = strategy_name
            
            # Extract strategy information
            strategy_info = {
                'name': strategy_name,
                'module': self.strategy_module,
                'has_params': hasattr(self.strategy_module, 'Params'),
                'parameters': {},
                'train_test_periods': {}
            }
            
            if strategy_info['has_params']:
                params_class = self.strategy_module.Params
                
                # Extract parameter ranges and current values
                if hasattr(params_class, 'param_ranges'):
                    strategy_info['parameters'] = {
                        'ranges': params_class.param_ranges,
                        'current': getattr(params_class, 'current_params', {})
                    }
                    
                # Extract train/test periods
                strategy_info['train_test_periods'] = self._extract_periods(params_class)
                
            logger.info(f"Successfully loaded strategy: {strategy_name}")
            return strategy_info
            
        except Exception as e:
            logger.error(f"Error loading strategy {strategy_name}: {str(e)}")
            raise ValueError(f"Failed to load strategy: {str(e)}")
            
    def _extract_periods(self, params_class) -> Dict[str, Any]:
        """Extract train/test period information from strategy"""
        periods = {}
        
        # Check for month-based periods
        if hasattr(params_class, 'training_months'):
            periods['unit'] = 'months'
            periods['train'] = params_class.training_months
            periods['test'] = getattr(params_class, 'testing_months', 1)
            
        # Check for day-based periods
        elif hasattr(params_class, 'training_days'):
            periods['unit'] = 'days'
            periods['train'] = params_class.training_days
            periods['test'] = getattr(params_class, 'testing_days', 30)
            
        return periods
        
    def update_strategy_parameters(self, parameter_values: Dict[str, Any]) -> None:
        """Update strategy parameters with new values"""
        if not self.strategy_module or not hasattr(self.strategy_module, 'Params'):
            raise ValueError("No strategy loaded or strategy has no parameters")
            
        try:
            # Update current_params in the strategy module
            for param_name, value in parameter_values.items():
                # Ensure integer conversion for parameters that might be used as indices
                if isinstance(value, float) and value.is_integer():
                    value = int(value)
                elif isinstance(value, float):
                    value = int(round(value))
                    
                self.strategy_module.Params.current_params[param_name] = value
                
            logger.info(f"Updated strategy parameters: {parameter_values}")
            
        except Exception as e:
            logger.error(f"Error updating parameters: {str(e)}")
            raise
            
    def run_optimization(self, data, progress_callback=None, walk_forward=False,
                        train_period=3, test_period=1, period_unit='months',
                        optimization_metric='total_profit') -> Dict[str, Any]:
        """
        Run parameter optimization using Bayesian optimization

        Args:
            data: Trading data for optimization
            progress_callback: Optional callback for progress updates
            walk_forward: If True, use walk-forward optimization; if False, use whole period
            train_period: Training period length
            test_period: Test period length
            period_unit: 'days' or 'months'
            optimization_metric: Metric to optimize ('total_profit', 'sharpe_ratio', etc.)

        Returns:
            Dictionary with optimization results
        """
        if not self.strategy_module:
            raise ValueError("No strategy loaded for optimization")

        # Store the optimization metric for use in performance calculation
        self.current_optimization_metric = optimization_metric
        logger.info(f"Optimization will use metric: {optimization_metric}")

        if not hasattr(self.strategy_module, 'Params') or not hasattr(self.strategy_module.Params, 'param_ranges'):
            raise ValueError("Strategy has no parameter ranges defined for optimization")

        try:
            param_ranges = self.strategy_module.Params.param_ranges

            if progress_callback:
                mode = "walk-forward" if walk_forward else "whole period"
                progress_callback(5, f"Starting {mode} Bayesian optimization for {len(param_ranges)} parameters")

            if walk_forward:
                # Run walk-forward Bayesian optimization
                best_params, best_performance = self._walk_forward_bayesian_optimization(
                    data, param_ranges, train_period, test_period, period_unit, progress_callback
                )
            else:
                # Run whole period Bayesian optimization
                best_params, best_performance = self._bayesian_optimization(
                    data, param_ranges, progress_callback
                )

            self.optimization_results = {
                'best_params': best_params,
                'best_performance': best_performance,
                'strategy': self.current_strategy,
                'optimization_mode': 'walk_forward' if walk_forward else 'whole_period'
            }

            if progress_callback:
                progress_callback(100, "Bayesian optimization completed")

            logger.info(f"Bayesian optimization completed. Best performance: {best_performance:.4f}")
            return self.optimization_results

        except Exception as e:
            logger.error(f"Bayesian optimization error: {str(e)}")
            raise

    def _bayesian_optimization(self, data, param_ranges, progress_callback=None) -> Tuple[Dict[str, Any], float]:
        """Perform Bayesian optimization using scikit-optimize"""
        try:
            from skopt import gp_minimize
            from skopt.space import Integer
            from skopt.utils import use_named_args
        except ImportError:
            logger.warning("scikit-optimize not available, falling back to random search")
            return self._random_search_optimization(data, param_ranges, progress_callback)

        best_params = self.strategy_module.Params.current_params.copy()
        best_performance = -float('inf')

        # Define search space
        dimensions = []
        param_names = list(param_ranges.keys())

        for param_name in param_names:
            min_val, max_val = param_ranges[param_name]
            dimensions.append(Integer(int(min_val), int(max_val), name=param_name))

        # Track evaluations for progress reporting
        self.evaluation_count = 0
        self.total_evaluations = OPTIMIZATION_CONFIG.get('bayesian_iterations', 50)

        @use_named_args(dimensions)
        def objective(**params):
            """Objective function for Bayesian optimization"""
            try:
                self.evaluation_count += 1

                # Update parameters
                for param_name, value in params.items():
                    self.strategy_module.Params.current_params[param_name] = int(value)

                # Run backtest
                trading_system = self.strategy_module.MyTradingSystem(data)
                trading_system.run_backtest()

                # Ensure trades are calculated and available
                if hasattr(trading_system, 'calculate_matched_trades'):
                    trading_system.calculate_matched_trades()

                # Copy matched trades to best_matched_trades if not already done
                if not hasattr(trading_system, 'best_matched_trades') or not trading_system.best_matched_trades:
                    if hasattr(trading_system, 'matched_trades'):
                        trading_system.best_matched_trades = trading_system.matched_trades

                # Calculate performance metric (negative because skopt minimizes)
                performance = self._calculate_performance_metric(trading_system)

                # Update progress
                if progress_callback and self.evaluation_count % 5 == 0:
                    progress = 10 + (self.evaluation_count / self.total_evaluations) * 85
                    progress_callback(progress, f"Bayesian optimization: {self.evaluation_count}/{self.total_evaluations}")

                # Return negative performance for minimization
                return -performance

            except Exception as e:
                logger.warning(f"Error in Bayesian optimization evaluation {self.evaluation_count}: {str(e)}")
                return 1000000  # Large positive value for minimization (bad performance)

        # Run Bayesian optimization
        logger.info(f"Whole period Bayesian optimization: {self.total_evaluations} evaluations on {len(data)} bars")
        if progress_callback:
            progress_callback(10, f"Whole period Bayesian optimization: {self.total_evaluations} evaluations")

        try:
            result = gp_minimize(
                func=objective,
                dimensions=dimensions,
                n_calls=self.total_evaluations,
                random_state=42,
                acq_func='EI',  # Expected Improvement
                n_initial_points=min(10, self.total_evaluations // 3)  # Initial random points
            )

            # Extract best parameters
            best_params = {}
            for i, param_name in enumerate(param_names):
                best_params[param_name] = int(result.x[i])

            # Best performance is negative of the minimum found
            best_performance = -result.fun

            logger.info(f"Bayesian optimization completed: {self.evaluation_count} evaluations")
            return best_params, best_performance

        except Exception as e:
            logger.error(f"Error in Bayesian optimization: {str(e)}")
            # Fallback to random search
            return self._random_search_optimization(data, param_ranges, progress_callback)

    def _random_search_optimization(self, data, param_ranges, progress_callback=None) -> Tuple[Dict[str, Any], float]:
        """Fallback random search optimization"""
        import random

        best_params = self.strategy_module.Params.current_params.copy()
        best_performance = -float('inf')

        n_iterations = OPTIMIZATION_CONFIG.get('random_search_iterations', 30)
        param_names = list(param_ranges.keys())

        if progress_callback:
            progress_callback(10, f"Running random search with {n_iterations} iterations")

        for i in range(n_iterations):
            try:
                # Generate random parameters
                test_params = {}
                for param_name in param_names:
                    min_val, max_val = param_ranges[param_name]
                    test_params[param_name] = random.randint(int(min_val), int(max_val))
                    self.strategy_module.Params.current_params[param_name] = test_params[param_name]

                # Run backtest
                trading_system = self.strategy_module.MyTradingSystem(data)
                trading_system.run_backtest()

                # Ensure trades are calculated and available
                if hasattr(trading_system, 'calculate_matched_trades'):
                    trading_system.calculate_matched_trades()

                if not hasattr(trading_system, 'best_matched_trades') or not trading_system.best_matched_trades:
                    if hasattr(trading_system, 'matched_trades'):
                        trading_system.best_matched_trades = trading_system.matched_trades

                # Calculate performance metric
                performance = self._calculate_performance_metric(trading_system)

                if performance > best_performance:
                    best_performance = performance
                    best_params = test_params.copy()

                # Update progress
                if progress_callback and i % 5 == 0:
                    progress = 10 + (i / n_iterations) * 85
                    progress_callback(progress, f"Random search: {i+1}/{n_iterations}")

            except Exception as e:
                logger.warning(f"Error in random search iteration {i}: {str(e)}")
                continue

        return best_params, best_performance

    def _walk_forward_bayesian_optimization(self, data, param_ranges, train_period, test_period,
                                          period_unit, progress_callback=None) -> Tuple[Dict[str, Any], float]:
        """Perform walk-forward Bayesian optimization"""
        from datetime import timedelta

        # Split data into walk-forward windows
        windows = self._create_walk_forward_windows(data, train_period, test_period, period_unit)

        if not windows:
            logger.warning("No valid walk-forward windows created, falling back to whole period optimization")
            return self._bayesian_optimization(data, param_ranges, progress_callback)

        logger.info(f"Walk-forward optimization: Created {len(windows)} windows")
        if progress_callback:
            progress_callback(5, f"Walk-forward optimization: {len(windows)} windows, 20 iterations per window")

        # Store results for each window
        window_results = []
        total_performance = 0

        for i, (train_data, test_data) in enumerate(windows):
            try:
                if progress_callback:
                    progress = 5 + (i / len(windows)) * 90
                    progress_callback(progress, f"Optimizing window {i+1}/{len(windows)}")

                # Optimize on training data with reduced iterations for efficiency
                window_best_params, _ = self._bayesian_optimization_reduced(
                    train_data, param_ranges, iterations=20  # Reduced iterations per window
                )

                # Test on out-of-sample data
                for param_name, value in window_best_params.items():
                    self.strategy_module.Params.current_params[param_name] = value

                trading_system = self.strategy_module.MyTradingSystem(test_data)
                trading_system.run_backtest()

                if hasattr(trading_system, 'calculate_matched_trades'):
                    trading_system.calculate_matched_trades()

                if not hasattr(trading_system, 'best_matched_trades') or not trading_system.best_matched_trades:
                    if hasattr(trading_system, 'matched_trades'):
                        trading_system.best_matched_trades = trading_system.matched_trades

                window_performance = self._calculate_performance_metric(trading_system)
                total_performance += window_performance

                window_results.append({
                    'params': window_best_params,
                    'performance': window_performance
                })

            except Exception as e:
                logger.warning(f"Error in walk-forward window {i}: {str(e)}")
                continue

        if not window_results:
            logger.error("No successful walk-forward windows")
            return self._bayesian_optimization(data, param_ranges, progress_callback)

        # Find best performing window or use average parameters
        best_window = max(window_results, key=lambda x: x['performance'])
        avg_performance = total_performance / len(window_results)

        logger.info(f"Walk-forward completed: {len(window_results)} windows, avg performance: {avg_performance:.4f}")
        return best_window['params'], avg_performance

    def _create_walk_forward_windows(self, data, train_period, test_period, period_unit):
        """Create walk-forward analysis windows"""
        from datetime import timedelta

        if not data or len(data) < 2:
            return []

        windows = []

        # Calculate period in days
        if period_unit == 'months':
            train_days = train_period * 30
            test_days = test_period * 30
        else:
            train_days = train_period
            test_days = test_period

        # Create non-overlapping windows for true walk-forward analysis
        start_idx = 0
        min_train_bars = max(100, len(data) // 8)  # Minimum training data (increased)
        min_test_bars = max(20, len(data) // 20)   # Minimum test data

        logger.info(f"Creating walk-forward windows: train={train_days} days, test={test_days} days")
        logger.info(f"Minimum bars: train={min_train_bars}, test={min_test_bars}")

        while start_idx + min_train_bars + min_test_bars < len(data):
            # Find training period end
            train_start_date = data[start_idx].Date
            train_end_date = train_start_date + timedelta(days=train_days)

            # Find training data end index
            train_end_idx = start_idx + min_train_bars  # Start with minimum
            for i in range(start_idx + min_train_bars, len(data)):
                if data[i].Date > train_end_date:
                    train_end_idx = i
                    break
            else:
                train_end_idx = len(data) - min_test_bars  # Leave room for test data

            if train_end_idx - start_idx < min_train_bars:
                break

            # Find test period (immediately after training)
            test_start_idx = train_end_idx
            test_end_date = train_end_date + timedelta(days=test_days)
            test_end_idx = test_start_idx + min_test_bars  # Start with minimum

            for i in range(test_start_idx + min_test_bars, len(data)):
                if data[i].Date > test_end_date:
                    test_end_idx = i
                    break
            else:
                test_end_idx = len(data)

            if test_end_idx - test_start_idx >= min_test_bars:
                train_data = data[start_idx:train_end_idx]
                test_data = data[test_start_idx:test_end_idx]

                logger.info(f"Window {len(windows)+1}: Train {len(train_data)} bars ({train_data[0].Date} to {train_data[-1].Date}), "
                           f"Test {len(test_data)} bars ({test_data[0].Date} to {test_data[-1].Date})")

                windows.append((train_data, test_data))

            # Move to next window (step forward by test period for non-overlapping)
            start_idx = test_end_idx

            # Limit number of windows to prevent excessive computation
            if len(windows) >= 10:  # Maximum 10 windows
                logger.info(f"Limiting to {len(windows)} windows for efficiency")
                break

        return windows

    def _bayesian_optimization_reduced(self, data, param_ranges, iterations=20) -> Tuple[Dict[str, Any], float]:
        """Perform reduced Bayesian optimization for walk-forward windows"""
        try:
            from skopt import gp_minimize
            from skopt.space import Integer
            from skopt.utils import use_named_args
        except ImportError:
            logger.warning("scikit-optimize not available, using random search for walk-forward")
            return self._random_search_optimization_reduced(data, param_ranges, iterations)

        best_params = self.strategy_module.Params.current_params.copy()

        # Define search space
        dimensions = []
        param_names = list(param_ranges.keys())

        for param_name in param_names:
            min_val, max_val = param_ranges[param_name]
            dimensions.append(Integer(int(min_val), int(max_val), name=param_name))

        # Track evaluations
        evaluation_count = 0

        @use_named_args(dimensions)
        def objective(**params):
            """Objective function for reduced Bayesian optimization"""
            nonlocal evaluation_count
            try:
                evaluation_count += 1

                # Update parameters
                for param_name, value in params.items():
                    self.strategy_module.Params.current_params[param_name] = int(value)

                # Run backtest
                trading_system = self.strategy_module.MyTradingSystem(data)
                trading_system.run_backtest()

                # Ensure trades are calculated and available
                if hasattr(trading_system, 'calculate_matched_trades'):
                    trading_system.calculate_matched_trades()

                if not hasattr(trading_system, 'best_matched_trades') or not trading_system.best_matched_trades:
                    if hasattr(trading_system, 'matched_trades'):
                        trading_system.best_matched_trades = trading_system.matched_trades

                # Calculate performance metric (negative because skopt minimizes)
                performance = self._calculate_performance_metric(trading_system)

                # Return negative performance for minimization
                return -performance

            except Exception as e:
                logger.warning(f"Error in reduced Bayesian optimization evaluation {evaluation_count}: {str(e)}")
                return 1000000  # Large positive value for minimization (bad performance)

        # Run reduced Bayesian optimization
        try:
            result = gp_minimize(
                func=objective,
                dimensions=dimensions,
                n_calls=iterations,
                random_state=42,
                acq_func='EI',  # Expected Improvement
                n_initial_points=min(5, iterations // 3)  # Fewer initial random points
            )

            # Extract best parameters
            best_params = {}
            for i, param_name in enumerate(param_names):
                best_params[param_name] = int(result.x[i])

            # Best performance is negative of the minimum found
            best_performance = -result.fun

            return best_params, best_performance

        except Exception as e:
            logger.error(f"Error in reduced Bayesian optimization: {str(e)}")
            # Fallback to random search
            return self._random_search_optimization_reduced(data, param_ranges, iterations)

    def _random_search_optimization_reduced(self, data, param_ranges, iterations=20) -> Tuple[Dict[str, Any], float]:
        """Reduced random search for walk-forward fallback"""
        import random

        best_params = self.strategy_module.Params.current_params.copy()
        best_performance = -float('inf')

        param_names = list(param_ranges.keys())

        for i in range(iterations):
            try:
                # Generate random parameters
                test_params = {}
                for param_name in param_names:
                    min_val, max_val = param_ranges[param_name]
                    test_params[param_name] = random.randint(int(min_val), int(max_val))
                    self.strategy_module.Params.current_params[param_name] = test_params[param_name]

                # Run backtest
                trading_system = self.strategy_module.MyTradingSystem(data)
                trading_system.run_backtest()

                # Ensure trades are calculated and available
                if hasattr(trading_system, 'calculate_matched_trades'):
                    trading_system.calculate_matched_trades()

                if not hasattr(trading_system, 'best_matched_trades') or not trading_system.best_matched_trades:
                    if hasattr(trading_system, 'matched_trades'):
                        trading_system.best_matched_trades = trading_system.matched_trades

                # Calculate performance metric
                performance = self._calculate_performance_metric(trading_system)

                if performance > best_performance:
                    best_performance = performance
                    best_params = test_params.copy()

            except Exception as e:
                logger.warning(f"Error in reduced random search iteration {i}: {str(e)}")
                continue

        return best_params, best_performance

    def _calculate_performance_metric(self, trading_system) -> float:
        """Calculate performance metric for optimization"""
        # Use the current optimization metric if set, otherwise use default
        metric_name = getattr(self, 'current_optimization_metric', OPTIMIZATION_CONFIG['default_metric'])

        # Check for trades in multiple possible attributes
        trades = None
        if hasattr(trading_system, 'best_matched_trades') and trading_system.best_matched_trades:
            trades = trading_system.best_matched_trades
        elif hasattr(trading_system, 'matched_trades') and trading_system.matched_trades:
            trades = trading_system.matched_trades

        if not trades:
            # No trades found - return a very negative value but not -inf
            logger.debug("No trades found for performance calculation")
            return -1000000  # Large negative number instead of -inf

        # Calculate the requested metric
        try:
            if metric_name == 'total_profit':
                total_profit = sum(trade.profit for trade in trades)
                logger.debug(f"Calculated total profit: {total_profit} from {len(trades)} trades")
                return total_profit

            elif metric_name == 'profit_factor':
                winning_trades = [t for t in trades if t.profit > 0]
                losing_trades = [t for t in trades if t.profit < 0]

                if not winning_trades or not losing_trades:
                    return 0

                gross_profit = sum(t.profit for t in winning_trades)
                gross_loss = abs(sum(t.profit for t in losing_trades))
                profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
                logger.debug(f"Calculated profit factor: {profit_factor}")
                return profit_factor if profit_factor != float('inf') else 1000

            elif metric_name == 'sharpe_ratio':
                profits = [trade.profit for trade in trades]
                if len(profits) > 1:
                    import statistics
                    mean_profit = statistics.mean(profits)
                    std_profit = statistics.stdev(profits)
                    sharpe = mean_profit / std_profit if std_profit > 0 else 0
                    logger.debug(f"Calculated Sharpe ratio: {sharpe} from {len(profits)} trades")
                    return sharpe
                elif len(profits) == 1:
                    return profits[0]
                return 0

            elif metric_name == 'win_rate':
                winning_trades = len([t for t in trades if t.profit > 0])
                win_rate = (winning_trades / len(trades)) * 100
                logger.debug(f"Calculated win rate: {win_rate}%")
                return win_rate

            elif metric_name == 'payoff_ratio':
                winning_trades = [t for t in trades if t.profit > 0]
                losing_trades = [t for t in trades if t.profit < 0]

                if not winning_trades or not losing_trades:
                    return 0

                avg_win = sum(t.profit for t in winning_trades) / len(winning_trades)
                avg_loss = abs(sum(t.profit for t in losing_trades) / len(losing_trades))
                payoff_ratio = avg_win / avg_loss if avg_loss > 0 else 0
                logger.debug(f"Calculated payoff ratio: {payoff_ratio}")
                return payoff_ratio

            elif metric_name in ['calmar_ratio', 'sortino_ratio', 'ddr_ratio', 'recovery_factor']:
                # For these complex ratios, we need to use the MetricsCalculator
                from .metrics_calculator import MetricsCalculator
                calculator = MetricsCalculator()
                metrics = calculator._calculate_additional_metrics(trades, trading_system)
                value = metrics.get(metric_name, 0)
                logger.debug(f"Calculated {metric_name}: {value}")
                return value

            else:
                logger.warning(f"Unknown optimization metric: {metric_name}, using total_profit")
                return sum(trade.profit for trade in trades)

        except Exception as e:
            logger.error(f"Error calculating {metric_name}: {str(e)}")
            return -1000000
        
    def create_new_strategy(self, strategy_name: str) -> str:
        """Create a new strategy file from template"""
        from .constants import STRATEGY_TEMPLATE
        
        strategy_path = f"Strategies/{strategy_name}.py"
        
        if os.path.exists(strategy_path):
            raise ValueError(f"Strategy '{strategy_name}' already exists")
            
        try:
            os.makedirs("Strategies", exist_ok=True)
            
            with open(strategy_path, 'w') as f:
                f.write(STRATEGY_TEMPLATE.replace("New Trading Strategy Template", f"{strategy_name} Strategy"))
                
            logger.info(f"Created new strategy: {strategy_name}")
            return strategy_path
            
        except Exception as e:
            logger.error(f"Error creating strategy: {str(e)}")
            raise
            
    def get_strategy_code(self, strategy_name: str) -> str:
        """Get strategy source code"""
        strategy_path = f"Strategies/{strategy_name}.py"
        
        if not os.path.exists(strategy_path):
            raise FileNotFoundError(f"Strategy file not found: {strategy_path}")
            
        try:
            with open(strategy_path, 'r') as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error reading strategy code: {str(e)}")
            raise
            
    def save_strategy_code(self, strategy_name: str, code: str) -> None:
        """Save strategy source code"""
        strategy_path = f"Strategies/{strategy_name}.py"

        try:
            with open(strategy_path, 'w') as f:
                f.write(code)

            # Reload the module to reflect changes
            module_name = f"Strategies.{strategy_name}"
            if module_name in sys.modules:
                importlib.reload(sys.modules[module_name])

            logger.info(f"Saved strategy code: {strategy_name}")

        except Exception as e:
            logger.error(f"Error saving strategy code: {str(e)}")
            raise

    def save_strategy_code_with_errors(self, strategy_name: str, code: str) -> Dict[str, Any]:
        """Save strategy source code even if it contains syntax errors"""
        strategy_path = f"Strategies/{strategy_name}.py"

        result = {
            'saved': False,
            'syntax_errors': [],
            'error': None
        }

        try:
            # First, try to compile the code to check for syntax errors
            try:
                compile(code, strategy_path, 'exec')
                # No syntax errors
                result['syntax_errors'] = []
            except SyntaxError as se:
                # Capture syntax error details
                error_msg = f"Line {se.lineno}: {se.msg}"
                if se.text:
                    error_msg += f" (near: '{se.text.strip()}')"
                result['syntax_errors'].append(error_msg)
            except Exception as e:
                # Other compilation errors
                result['syntax_errors'].append(f"Compilation error: {str(e)}")

            # Save the file regardless of syntax errors
            with open(strategy_path, 'w') as f:
                f.write(code)

            result['saved'] = True

            # Only try to reload the module if there are no syntax errors
            if not result['syntax_errors']:
                module_name = f"Strategies.{strategy_name}"
                if module_name in sys.modules:
                    try:
                        importlib.reload(sys.modules[module_name])
                        logger.info(f"Saved and reloaded strategy code: {strategy_name}")
                    except Exception as e:
                        logger.warning(f"Saved strategy but failed to reload module: {str(e)}")
                        result['syntax_errors'].append(f"Module reload failed: {str(e)}")
                else:
                    logger.info(f"Saved strategy code: {strategy_name}")
            else:
                logger.info(f"Saved strategy code with syntax errors: {strategy_name}")

        except Exception as e:
            # File system or other critical errors
            result['error'] = str(e)
            logger.error(f"Error saving strategy code: {str(e)}")

        return result
            
    def validate_strategy(self, strategy_name: str) -> Dict[str, Any]:
        """Validate strategy structure and parameters"""
        try:
            strategy_info = self.load_strategy(strategy_name)
            
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': []
            }
            
            # Check for required components
            if not strategy_info['has_params']:
                validation_result['warnings'].append("Strategy has no Params class")
                
            if 'MyTradingSystem' not in dir(strategy_info['module']):
                validation_result['errors'].append("Strategy missing MyTradingSystem class")
                validation_result['valid'] = False
                
            # Check parameter structure
            if strategy_info['parameters']:
                ranges = strategy_info['parameters'].get('ranges', {})
                current = strategy_info['parameters'].get('current', {})
                
                for param_name, (min_val, max_val) in ranges.items():
                    if param_name not in current:
                        validation_result['warnings'].append(f"Parameter '{param_name}' has no current value")
                    elif not (min_val <= current[param_name] <= max_val):
                        validation_result['warnings'].append(f"Parameter '{param_name}' current value outside range")
                        
            return validation_result
            
        except Exception as e:
            return {
                'valid': False,
                'errors': [str(e)],
                'warnings': []
            }

    @property
    def has_strategy(self) -> bool:
        """Check if a strategy is currently loaded"""
        return self.strategy_module is not None

    @property
    def current_strategy_name(self) -> Optional[str]:
        """Get name of currently loaded strategy"""
        return self.current_strategy
