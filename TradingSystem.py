import pandas as pd
import numpy as np
import time
import matplotlib.pyplot as plt
import matplotlib.dates as mdates

class Bar:
    def __init__(self, Date, Open, High, Low, Close, Volume):
        self.Date = Date
        self.Open = Open
        self.High = High
        self.Low = Low
        self.Close = Close
        self.Volume = Volume
    
    def __getitem__(self, key):
        """Enable slicing of the Bar object"""
        return Bar(
            Date=self.Date[key],
            Open=self.Open.iloc[key],
            High=self.High.iloc[key],
            Low=self.Low.iloc[key],
            Close=self.Close.iloc[key],
            Volume=self.Volume.iloc[key]
        )
class Trade:
    def __init__(self, action, quantity, price, Date, order_name):
        self.action = action  # "buy" or "sell"
        self.quantity = quantity
        self.price = price
        self.Date = Date  # Date of the trade
        self.order_name = order_name  # Name of the order
        self.timestamp = time.time()  # Timestamp of when the trade was created

class MatchedTrade:
    class TradeType:
        LONG = "LONG"
        SHORT = "SHORT"

    def __init__(self, buy_Date, sell_Date, matched_quantity, buy_price, sell_price, profit, buy_order_name, sell_order_name, trade_type):
        self.buy_Date = buy_Date
        self.sell_Date = sell_Date
        self.matched_quantity = matched_quantity
        self.buy_price = buy_price
        self.sell_price = sell_price
        self.profit = profit
        self.buy_order_name = buy_order_name
        self.sell_order_name = sell_order_name
        self.trade_type = trade_type

class TradingSystem:
    def __init__(self, bars, resampled_bars=None):
        self.Bars = bars
        self.Resampled_Bars = resampled_bars
        self.current_position = ""
        self.positions = []  # List to keep track of current positions
        self.trade_history = []  # To record all trades executed
        self.matched_trades = []  # To store matched trades
        self.best_matched_trades = []
        self.strategy_name = ''
        self.market = ''

        # Performance optimization: track unmatched trades separately
        self._unmatched_buys = []  # List of (index, trade) tuples
        self._unmatched_sells = []  # List of (index, trade) tuples
        self._last_matched_index = -1  # Track last processed trade for incremental matching

    def run_backtest(self):
        total_bars = len(self.Bars)
        for bar in range(total_bars):
            try:
                self.process_bar(bar)
            except Exception as e:
                print(f"Error processing bar {bar}/{total_bars}: {e}")
                # Continue processing other bars instead of stopping
                continue

    def buy(self, bar, quantity, order_type="market", price=0.0, order_name=""):
        if bar >= len(self.Bars):
            return

        if order_type == "limit":
            if self.Bars[bar].Low <= price:
                self.record_trade("buy", quantity, price, bar, order_name)
                if self.current_position == "":
                    self.current_position = "Long"
                else:
                    self.current_position = ""
        elif order_type == "stop":
            if self.Bars[bar].High >= price:
                self.record_trade("buy", quantity, price, bar, order_name)
                if self.current_position == "":
                    self.current_position = "Long"
                else:
                    self.current_position = ""
        else:  # market
            self.record_trade("buy", quantity, self.Bars[bar].Open, bar, order_name)
            if self.current_position == "":
                self.current_position = "Long"
            else:
                self.current_position = ""
        

    def sell(self, bar, quantity, order_type="market", price=0.0, order_name=""):
        if bar >= len(self.Bars):
            return

        if order_type == "limit":
            if self.Bars[bar].High >= price:
                self.record_trade("sell", quantity, price, bar, order_name)
                if self.current_position == "":
                    self.current_position = "Short"
                else:
                    self.current_position = ""
        elif order_type == "stop":
            if self.Bars[bar].Low <= price:
                self.record_trade("sell", quantity, price, bar, order_name)
                if self.current_position == "":
                    self.current_position = "Short"
                else:
                    self.current_position = ""
        else:  # market
            self.record_trade("sell", quantity, self.Bars[bar].Open, bar, order_name)
            if self.current_position == "":
                self.current_position = "Short"
            else:
                self.current_position = ""
    def record_trade(self, action, quantity, price, bar_index, order_name):
        trade = Trade(action, quantity, price, self.Bars[bar_index].Date, order_name)
        self.positions.append(trade)  # Track active positions
        self.trade_history.append(trade)  # Record all trades

        # Optimized incremental trade matching
        self._incremental_match_trades()

    def _incremental_match_trades(self):
        """Optimized incremental trade matching - only process new trades."""
        # Process only new trades since last matching
        new_trades_start = self._last_matched_index + 1

        for i in range(new_trades_start, len(self.trade_history)):
            trade = self.trade_history[i]
            if trade.action == "buy":
                self._unmatched_buys.append((i, trade))
            else:
                self._unmatched_sells.append((i, trade))

        # Match trades using the optimized algorithm
        self._match_unmatched_trades()
        self._last_matched_index = len(self.trade_history) - 1

    def _match_unmatched_trades(self):
        """Optimized matching of unmatched buy and sell trades."""
        # Use deque for O(1) pop operations from both ends
        from collections import deque

        # Convert to deques if they aren't already
        if not isinstance(self._unmatched_buys, deque):
            self._unmatched_buys = deque(self._unmatched_buys)
        if not isinstance(self._unmatched_sells, deque):
            self._unmatched_sells = deque(self._unmatched_sells)

        # Pre-allocate list for better performance
        new_matches = []

        while self._unmatched_buys and self._unmatched_sells:
            buy_idx, buy_trade = self._unmatched_buys[0]
            sell_idx, sell_trade = self._unmatched_sells[0]

            matched_quantity = min(buy_trade.quantity, sell_trade.quantity)

            # Optimize profit calculation - avoid repeated attribute access
            buy_price = buy_trade.price
            sell_price = sell_trade.price
            profit = (sell_price - buy_price) * matched_quantity

            # Determine trade type based on original order in trade_history
            trade_type = MatchedTrade.TradeType.LONG if buy_idx < sell_idx else MatchedTrade.TradeType.SHORT

            # Create matched trade object
            new_matches.append(MatchedTrade(
                buy_trade.Date, sell_trade.Date, matched_quantity,
                buy_price, sell_price, profit,
                buy_trade.order_name, sell_trade.order_name, trade_type
            ))

            # Update remaining quantities efficiently
            buy_qty = buy_trade.quantity
            sell_qty = sell_trade.quantity

            if buy_qty > sell_qty:
                buy_trade.quantity = buy_qty - matched_quantity
                self._unmatched_sells.popleft()
            elif buy_qty < sell_qty:
                sell_trade.quantity = sell_qty - matched_quantity
                self._unmatched_buys.popleft()
            else:
                self._unmatched_buys.popleft()
                self._unmatched_sells.popleft()

        # Extend matched_trades list once instead of multiple appends
        self.matched_trades.extend(new_matches)

    def calculate_matched_trades(self):
        """Optimized method for calculating matched trades."""
        # Only recalculate if there are new trades
        if self._last_matched_index >= len(self.trade_history) - 1:
            return self.matched_trades

        # Use collections.deque for better performance
        from collections import deque

        # Clear and recalculate everything (for compatibility)
        self.matched_trades.clear()
        self._unmatched_buys = deque()
        self._unmatched_sells = deque()
        self._last_matched_index = -1

        # Process all trades with optimized loop
        trade_history = self.trade_history  # Cache reference
        for i, trade in enumerate(trade_history):
            if trade.action == "buy":
                self._unmatched_buys.append((i, trade))
            else:
                self._unmatched_sells.append((i, trade))

        self._match_unmatched_trades()
        self._last_matched_index = len(trade_history) - 1
        return self.matched_trades

    def Active_Position(self):
        return self.current_position

    def export_matched_trades_to_csv(self, filename):
        if len(self.matched_trades) == 0:
            print("No matched trades found.")
            return

        data = []
        for trade in self.matched_trades:
            if trade.trade_type == MatchedTrade.TradeType.LONG:
                data.append([
                    "Long", trade.buy_order_name, trade.sell_order_name, trade.matched_quantity,
                    trade.buy_price, trade.sell_price, trade.profit, trade.buy_Date, trade.sell_Date
                ])
            else:
                data.append([
                    "Short", trade.sell_order_name, trade.buy_order_name, trade.matched_quantity,
                    trade.sell_price, trade.buy_price, trade.profit, trade.sell_Date, trade.buy_Date
                ])

        df = pd.DataFrame(data, columns=[
            "trade_type", "entry_name", "exit_order_name", "matched_quantity",
            "entry_price", "exit_price", "profit", "entry_Date", "exit_Date"
        ])
        df.to_csv(filename, index=False)
        print(f"Matched trades exported to {filename}.")

    def plot_equity_curves(start_capital, bars, matched_trades, bh_returns, bPrint=False):
        # Convert bars to dates
        dates = [bar.Date for bar in bars]
        
        # Initialize equity curves
        equity = {
            'overall': [start_capital],
            'long': [start_capital],
            'short': [start_capital],
            'buy_hold': [start_capital]
        }
        
        # Track trade dates for plotting markers
        trade_dates = {
            'long_entry': [],
            'long_exit': [],
            'short_entry': [],
            'short_exit': []
        }
        
        # Process matched trades
        for trade in matched_trades:
            if trade.trade_type == MatchedTrade.TradeType.LONG:
                # Long trade
                equity['overall'].append(equity['overall'][-1] + trade.profit)
                equity['long'].append(equity['long'][-1] + trade.profit)
                equity['short'].append(equity['short'][-1])  # No change for short curve
                
                # Record trade dates for markers
                trade_dates['long_entry'].append(trade.buy_Date)
                trade_dates['long_exit'].append(trade.sell_Date)
            else:
                # Short trade
                equity['overall'].append(equity['overall'][-1] + trade.profit)
                equity['short'].append(equity['short'][-1] + trade.profit)
                equity['long'].append(equity['long'][-1])  # No change for long curve
                
                # Record trade dates for markers
                trade_dates['short_entry'].append(trade.sell_Date)  # For short, sell is entry
                trade_dates['short_exit'].append(trade.buy_Date)    # and buy is exit
        
        # Process buy-and-hold returns (assuming bh_returns are daily percentage changes)
        for ret in bh_returns:
            equity['buy_hold'].append(equity['buy_hold'][-1] * (1 + ret/100))
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
        
        # Main equity curve (ax1)
        ax1.plot(dates[:len(equity['overall'])], equity['overall'], label='Strategy (Overall)', color='blue', linewidth=2)
        ax1.plot(dates[:len(equity['buy_hold'])], equity['buy_hold'], label='Buy & Hold', color='green', linestyle='--', linewidth=1.5)
        
        # Long/Short equity curves (ax2)
        ax2.plot(dates[:len(equity['long'])], equity['long'], label='Long Trades', color='red', linewidth=1.5)
        ax2.plot(dates[:len(equity['short'])], equity['short'], label='Short Trades', color='purple', linewidth=1.5)
        
        # Add trade markers to both plots
        for entry_date in trade_dates['long_entry']:
            idx = dates.index(entry_date)
            ax1.plot(entry_date, equity['overall'][idx], '^', color='lime', markersize=8)
            ax2.plot(entry_date, equity['long'][idx], '^', color='lime', markersize=8)
        
        for exit_date in trade_dates['long_exit']:
            idx = dates.index(exit_date)
            ax1.plot(exit_date, equity['overall'][idx], 'v', color='orange', markersize=8)
            ax2.plot(exit_date, equity['long'][idx], 'v', color='orange', markersize=8)
        
        for entry_date in trade_dates['short_entry']:
            idx = dates.index(entry_date)
            ax1.plot(entry_date, equity['overall'][idx], '^', color='cyan', markersize=8)
            ax2.plot(entry_date, equity['short'][idx], '^', color='cyan', markersize=8)
        
        for exit_date in trade_dates['short_exit']:
            idx = dates.index(exit_date)
            ax1.plot(exit_date, equity['overall'][idx], 'v', color='magenta', markersize=8)
            ax2.plot(exit_date, equity['short'][idx], 'v', color='magenta', markersize=8)
        
        # Formatting for both axes
        for ax in [ax1, ax2]:
            ax.grid(True, which='both', linestyle='--', linewidth=0.5)
            ax.legend()
            ax.set_ylabel('Equity ($)', fontsize=10)
        
        # Format x-axis dates
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax1.xaxis.set_major_locator(mdates.YearLocator())
        fig.autofmt_xdate()
        
        # Titles
        ax1.set_title(f'Equity Curve\n', fontsize=14, fontweight='bold')
        ax2.set_title('Long vs Short Trades Equity Curves', fontsize=12, fontweight='bold')
        
        # Calculate metrics
        metrics = {
            'overall': {
                'net_profit': equity['overall'][-1] - start_capital,
                'trades': len(matched_trades)
            },
            'long': {
                'net_profit': equity['long'][-1] - start_capital,
                'trades': sum(1 for t in matched_trades if t.trade_type == MatchedTrade.TradeType.LONG)
            },
            'short': {
                'net_profit': equity['short'][-1] - start_capital,
                'trades': sum(1 for t in matched_trades if t.trade_type == MatchedTrade.TradeType.SHORT)
            },
            'buy_hold': {
                'net_profit': equity['buy_hold'][-1] - start_capital
            }
        }
        
        # Add metrics text box
        text_str = (f"Starting Capital: ${start_capital:,.2f}\n"
                    f"Overall Net Profit: ${metrics['overall']['net_profit']:,.2f} ({metrics['overall']['trades']} trades)\n"
                    f"Long Trades Profit: ${metrics['long']['net_profit']:,.2f} ({metrics['long']['trades']} trades)\n"
                    f"Short Trades Profit: ${metrics['short']['net_profit']:,.2f} ({metrics['short']['trades']} trades)\n"
                    f"Buy & Hold Profit: ${metrics['buy_hold']['net_profit']:,.2f}")
        
        ax1.annotate(text_str, xy=(0.02, 0.15), xycoords='axes fraction', 
                     bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # Add legend for markers
        marker_legend = [
            plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='lime', markersize=10, label='Long Entry'),
            plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='orange', markersize=10, label='Long Exit'),
            plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='cyan', markersize=10, label='Short Entry'),
            plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='magenta', markersize=10, label='Short Exit')
        ]
        ax2.legend(handles=marker_legend, loc='upper left')
        
        # Add disclaimer
        disclaimer = ("Backtesting provides a hypothetical calculation of how a security or portfolio of securities, "
                     "subject to a trading strategy, would have performed over a historical time period.")
        fig.text(0.5, 0.01, disclaimer, ha='center', fontsize=8, color='gray')
        
        plt.tight_layout()
        
        if bPrint:
            print(f"\n=== Equity Curve Summary ===")
            print(f"Bars: {len(bars)}")
            print(f"Last Bar Date: {dates[-1].strftime('%m/%d/%Y')}")
            print(f"\n=== Performance Metrics ===")
            print(f"Overall Net Profit: ${metrics['overall']['net_profit']:,.2f} ({metrics['overall']['trades']} trades)")
            print(f"Long Trades Profit: ${metrics['long']['net_profit']:,.2f} ({metrics['long']['trades']} trades)")
            print(f"Short Trades Profit: ${metrics['short']['net_profit']:,.2f} ({metrics['short']['trades']} trades)")
            print(f"Buy & Hold Profit: ${metrics['buy_hold']['net_profit']:,.2f}")
        
        return fig

    def metrics(self, trades):
         return sum(trade.profit for trade in trades)

    def performance_metrics(self, bh_returns, bPrint=False, bPlot=True):
        if not self.best_matched_trades:
            if bPrint:
                print("No trades executed.")
            return None

        # Initialize metrics
        metrics = {
            "all": self._init_metrics_dict(),
            "long": self._init_metrics_dict(),
            "short": self._init_metrics_dict()
        }

        # Track equity curves separately
        equity_curve = {
            "all": [0],
            "long": [0],
            "short": [0]
        }

        # --- Trade Analysis ---
        for trade in self.best_matched_trades:
            trade_type = "long" if trade.trade_type == MatchedTrade.TradeType.LONG else "short"
            
            # Update metrics
            self._update_metrics(metrics["all"], trade)
            self._update_metrics(metrics[trade_type], trade)
            
            # Update equity curves
            equity_curve["all"].append(equity_curve["all"][-1] + trade.profit)
            equity_curve[trade_type].append(equity_curve[trade_type][-1] + trade.profit)

        # Process buy-and-hold returns (assuming bh_returns are daily percentage changes)
        equity_buy_hold = [0]
        for ret in bh_returns:
            equity_buy_hold.append(equity_buy_hold[-1] * (1 + ret/100))
        buy_hold_MDD = self._calculate_drawdown(equity_buy_hold)
        buy_hold_MDD = buy_hold_MDD if buy_hold_MDD != 0 else np.nan
        buy_hold_DDR = self._calculate_DDR(equity_buy_hold)
        # --- Advanced Metrics ---
        for key in metrics.keys():
            # Drawdown
            metrics[key]["max_drawdown"] = self._calculate_drawdown(equity_curve[key])
            
            # Risk/Reward
            metrics[key].update({
                "win_rate": (metrics[key]["wins"] / metrics[key]["total_trades"]) * 100 if metrics[key]["total_trades"] > 0 else 0,
                "profit_factor": metrics[key]["gross_profit"] / metrics[key]["gross_loss"] if metrics[key]["gross_loss"] > 0 else float('inf'),
                "payoff_ratio": (metrics[key]["avg_win"] / metrics[key]["avg_loss"]) if metrics[key]["avg_loss"] > 0 else float('inf'),
                "recovery_factor": metrics[key]["total_profit"] / abs(metrics[key]["max_drawdown"]),
                "sharpe_ratio": self._calculate_sharpe(
                    [t.profit for t in self.best_matched_trades 
                     if key == "all" or 
                     ("long" if t.trade_type == MatchedTrade.TradeType.LONG else "short") == key]
                ),
               "DD_ratio": self._calculate_DDR(equity_curve[key])
            })

        # --- Benchmark Comparison ---
        buy_hold_return = self.Bars[-1].Close - self.Bars[0].Close
        metrics["all"]["vs_buy_hold"] = metrics["all"]["total_profit"] - buy_hold_return

        # --- Print Results ---
        if bPrint:
            self._print_metrics(metrics, buy_hold_return, buy_hold_MDD, buy_hold_DDR, bPlot)

        return metrics

    # --- Helper Methods ---
    def _init_metrics_dict(self):
        return {
            "total_trades": 0,
            "total_profit": 0,
            "wins": 0,
            "losses": 0,
            "gross_profit": 0,
            "gross_loss": 0,
            "avg_win": 0,
            "avg_loss": 0,
            "max_drawdown": 0,
            "win_rate": 0,
            "profit_factor": 0,
            "payoff_ratio": 0,
            "recovery_factor": 0,
            "sharpe_ratio": 0,
            "DD_ratio": 0,
            "vs_buy_hold": 0
        }

    def _update_metrics(self, metrics_dict, trade):
        metrics_dict["total_trades"] += 1
        metrics_dict["total_profit"] += trade.profit
        
        if trade.profit > 0:
            metrics_dict["wins"] += 1
            metrics_dict["gross_profit"] += trade.profit
        else:
            metrics_dict["losses"] += 1
            metrics_dict["gross_loss"] += abs(trade.profit)
        
        # Update averages
        metrics_dict["avg_win"] = metrics_dict["gross_profit"] / metrics_dict["wins"] if metrics_dict["wins"] > 0 else 0
        metrics_dict["avg_loss"] = metrics_dict["gross_loss"] / metrics_dict["losses"] if metrics_dict["losses"] > 0 else 0

    def _calculate_drawdown(self, equity_curve):
        """Optimized drawdown calculation using vectorized operations."""
        if not equity_curve:
            return 0

        equity_array = np.array(equity_curve)
        # Calculate running maximum (peak)
        running_max = np.maximum.accumulate(equity_array)
        # Calculate drawdown
        drawdown = running_max - equity_array
        return float(np.max(drawdown))

    def _calculate_sharpe(self, returns):
        """Optimized Sharpe ratio calculation using NumPy."""
        if not returns:
            return 0

        returns_array = np.array(returns)
        avg_return = np.mean(returns_array)
        std_dev = np.std(returns_array)
        return float(avg_return / std_dev) if std_dev > 0 else 0

    def _calculate_DDR(self, equity_curve):
        equity_curve = pd.DataFrame(equity_curve)
        DD = equity_curve.cummax()-equity_curve
        sum_DD = DD.sum()
        count_zeros = DD.eq(0).sum()
        return ((equity_curve.iloc[-1]-equity_curve.iloc[0])*count_zeros/sum_DD).iloc[0]

    def _print_metrics(self, metrics, buy_hold_return, buy_hold_MDD, buy_hold_DDR, bPlot):
        # Create a combined table
        table_data = [
            ["Total Profit", 
             f"${metrics['all']['total_profit']:.2f}",
             f"${metrics['long']['total_profit']:.2f}",
             f"${metrics['short']['total_profit']:.2f}",
             f"${buy_hold_return:.2f}"],
            ["Total Trades", 
             metrics['all']['total_trades'],
             metrics['long']['total_trades'],
             metrics['short']['total_trades'],
             1],
            ["Win Rate", 
             f"{metrics['all']['win_rate']:.1f}%",
             f"{metrics['long']['win_rate']:.1f}%",
             f"{metrics['short']['win_rate']:.1f}%",
             "-"],             
            ["Profit Factor", 
             f"{metrics['all']['profit_factor']:.2f}",
             f"{metrics['long']['profit_factor']:.2f}",
             f"{metrics['short']['profit_factor']:.2f}",
             "-"],
            ["Payoff Ratio", 
             "-",
             f"{metrics['long']['payoff_ratio']:.2f}",
             f"{metrics['short']['payoff_ratio']:.2f}",
             "-"],
            ["Recovery Factor", 
             f"{metrics['all']['recovery_factor']:.2f}",
             f"{metrics['long']['recovery_factor']:.2f}",
             f"{metrics['short']['recovery_factor']:.2f}",
             f"{buy_hold_return/buy_hold_MDD:.2f}"],
            ["Max Drawdown", 
             f"${metrics['all']['max_drawdown']:.2f}",
             f"${metrics['long']['max_drawdown']:.2f}",
             f"${metrics['short']['max_drawdown']:.2f}",
             f"${buy_hold_MDD:.2f}"],
            ["Sharpe Ratio", 
             f"{metrics['all']['sharpe_ratio']:.2f}",
             f"{metrics['long']['sharpe_ratio']:.2f}",
             f"{metrics['short']['sharpe_ratio']:.2f}",
             "-"],
            ["DD Ratio", 
             f"{metrics['all']['DD_ratio']:.2f}",
             f"{metrics['long']['DD_ratio']:.2f}",
             f"{metrics['short']['DD_ratio']:.2f}",
             f"{buy_hold_DDR:.2f}"],
            ["", "", "", "", ""],
            ["Strategy Outperformance", 
             f"${metrics['all']['vs_buy_hold']:.2f}", 
             "", 
             "", ""]
        ]

        # Convert to DataFrame for nice formatting
        df = pd.DataFrame(table_data, columns=[
            "Metric", 
            "Overall Strategy", 
            "Long Trades", 
            "Short Trades",
            "Buy & Hold"
        ])
        
        # Print the table without index
        print("\nPerformance Metrics Summary:")
        print("Market: " + self.market)
        print("Strategy: "+ self.strategy_name+"\n")
        print(df.to_string(index=False, justify='left'))
        
        if bPlot:
            self.plot_equity_curves(0, self.Bars, self.best_matched_trades, buy_hold_return, True)
            plt.show()
