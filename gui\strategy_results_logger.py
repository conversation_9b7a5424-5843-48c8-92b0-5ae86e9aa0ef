"""
Strategy Results Logger - Comprehensive logging system for strategy backtests
Records market data, timeframes, performance statistics, hyperparameters, and trades
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
import pandas as pd
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class StrategyRunResult:
    """Data class to store complete strategy run results"""
    # Run metadata
    timestamp: str
    strategy_name: str
    market_symbol: str
    data_file: str
    
    # Timeframe information
    timeframe_type: str  # "All Data", "Last 30 Days", "Custom", etc.
    start_date: str
    end_date: str
    total_bars: int
    
    # Hyperparameters
    parameters: Dict[str, Any]
    optimization_metric: str
    
    # Key performance statistics
    total_profit: float
    total_trades: int
    win_rate: float
    profit_factor: float
    max_drawdown: float
    sharpe_ratio: float
    calmar_ratio: float
    sortino_ratio: float
    ddr_ratio: float
    
    # Detailed metrics by trade type
    all_trades_metrics: Dict[str, Any]
    long_trades_metrics: Dict[str, Any]
    short_trades_metrics: Dict[str, Any]
    buy_hold_metrics: Dict[str, Any]
    
    # Trade details (optional)
    trades: Optional[List[Dict[str, Any]]] = None
    
    # Additional metadata
    bar_size: str = ""
    walk_forward: bool = False
    train_period: int = 0
    test_period: int = 0
    period_unit: str = ""

class StrategyResultsLogger:
    """Manages logging and storage of strategy backtest results"""
    
    def __init__(self, log_directory: str = "strategy_logs"):
        """Initialize the logger with specified directory"""
        self.log_directory = log_directory
        self.ensure_log_directory()
        
        # Create main log file
        self.log_file = os.path.join(log_directory, "strategy_results.json")
        self.csv_file = os.path.join(log_directory, "strategy_results.csv")
        self.trades_csv_file = os.path.join(log_directory, "strategy_trades.csv")
        
    def ensure_log_directory(self):
        """Ensure log directory exists"""
        if not os.path.exists(self.log_directory):
            os.makedirs(self.log_directory)
            logger.info(f"Created strategy log directory: {self.log_directory}")
    
    def log_strategy_run(self, 
                        strategy_name: str,
                        market_symbol: str,
                        data_file: str,
                        timeframe_info: Dict[str, Any],
                        parameters: Dict[str, Any],
                        metrics: Dict[str, Any],
                        buy_hold_metrics: Dict[str, Any],
                        optimization_metric: str,
                        additional_info: Dict[str, Any] = None,
                        trades: List[Any] = None,
                        include_trades: bool = False) -> str:
        """
        Log a complete strategy run with all details
        
        Returns:
            str: Unique run ID for this log entry
        """
        try:
            # Generate unique run ID
            run_id = f"{strategy_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Extract key metrics safely
            all_metrics = metrics.get('all', {})
            long_metrics = metrics.get('long', {})
            short_metrics = metrics.get('short', {})
            
            # Prepare trade data if requested
            trade_data = None
            if include_trades and trades:
                trade_data = self._serialize_trades(trades)
            
            # Create result object
            result = StrategyRunResult(
                timestamp=datetime.now().isoformat(),
                strategy_name=strategy_name,
                market_symbol=market_symbol,
                data_file=data_file,
                
                # Timeframe
                timeframe_type=timeframe_info.get('type', 'Unknown'),
                start_date=timeframe_info.get('start_date', ''),
                end_date=timeframe_info.get('end_date', ''),
                total_bars=timeframe_info.get('total_bars', 0),
                
                # Parameters
                parameters=parameters,
                optimization_metric=optimization_metric,
                
                # Key performance stats
                total_profit=all_metrics.get('total_profit', 0),
                total_trades=all_metrics.get('total_trades', 0),
                win_rate=all_metrics.get('win_rate', 0),
                profit_factor=all_metrics.get('profit_factor', 0),
                max_drawdown=all_metrics.get('max_drawdown', 0),
                sharpe_ratio=all_metrics.get('sharpe_ratio', 0),
                calmar_ratio=all_metrics.get('calmar_ratio', 0),
                sortino_ratio=all_metrics.get('sortino_ratio', 0),
                ddr_ratio=all_metrics.get('DD_ratio', 0),
                
                # Detailed metrics
                all_trades_metrics=all_metrics,
                long_trades_metrics=long_metrics,
                short_trades_metrics=short_metrics,
                buy_hold_metrics=buy_hold_metrics,
                
                # Trade details
                trades=trade_data,
                
                # Additional info
                bar_size=additional_info.get('bar_size', '') if additional_info else '',
                walk_forward=additional_info.get('walk_forward', False) if additional_info else False,
                train_period=additional_info.get('train_period', 0) if additional_info else 0,
                test_period=additional_info.get('test_period', 0) if additional_info else 0,
                period_unit=additional_info.get('period_unit', '') if additional_info else ''
            )
            
            # Save to JSON log
            self._save_to_json(run_id, result)

            # Save to CSV summary
            self._save_to_csv(result)

            # Save trades to separate CSV if available
            if result.trades and include_trades:
                self._save_trades_to_csv(run_id, result)
            
            logger.info(f"Logged strategy run: {run_id}")
            return run_id
            
        except Exception as e:
            logger.error(f"Error logging strategy run: {str(e)}")
            return ""
    
    def _serialize_trades(self, trades: List[Any]) -> List[Dict[str, Any]]:
        """Convert trade objects to serializable dictionaries"""
        trade_data = []
        for trade in trades:
            try:
                # Handle MatchedTrade objects from TradingSystem
                if hasattr(trade, 'buy_Date') and hasattr(trade, 'sell_Date'):
                    # This is a MatchedTrade object
                    if trade.trade_type == "LONG":
                        entry_date = str(getattr(trade, 'buy_Date', ''))
                        exit_date = str(getattr(trade, 'sell_Date', ''))
                        entry_price = float(getattr(trade, 'buy_price', 0))
                        exit_price = float(getattr(trade, 'sell_price', 0))
                        entry_reason = str(getattr(trade, 'buy_order_name', ''))
                        exit_reason = str(getattr(trade, 'sell_order_name', ''))
                    else:  # SHORT
                        entry_date = str(getattr(trade, 'sell_Date', ''))
                        exit_date = str(getattr(trade, 'buy_Date', ''))
                        entry_price = float(getattr(trade, 'sell_price', 0))
                        exit_price = float(getattr(trade, 'buy_price', 0))
                        entry_reason = str(getattr(trade, 'sell_order_name', ''))
                        exit_reason = str(getattr(trade, 'buy_order_name', ''))

                    # Calculate bars held (approximate)
                    bars_held = 0
                    try:
                        if hasattr(trade, 'buy_Date') and hasattr(trade, 'sell_Date'):
                            from datetime import datetime
                            if isinstance(trade.buy_Date, str):
                                buy_dt = datetime.strptime(trade.buy_Date, '%Y-%m-%d')
                                sell_dt = datetime.strptime(trade.sell_Date, '%Y-%m-%d')
                            else:
                                buy_dt = trade.buy_Date
                                sell_dt = trade.sell_Date
                            bars_held = abs((sell_dt - buy_dt).days)
                    except:
                        bars_held = 0

                    trade_dict = {
                        'entry_date': entry_date,
                        'exit_date': exit_date,
                        'trade_type': str(getattr(trade, 'trade_type', '')),
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'quantity': float(getattr(trade, 'matched_quantity', 0)),
                        'profit': float(getattr(trade, 'profit', 0)),
                        'commission': 0.0,  # MatchedTrade doesn't have commission
                        'bars_held': bars_held,
                        'entry_reason': entry_reason,
                        'exit_reason': exit_reason
                    }
                else:
                    # Handle dictionary or other trade formats
                    trade_dict = {
                        'entry_date': str(getattr(trade, 'entry_date', trade.get('entry_date', '') if hasattr(trade, 'get') else '')),
                        'exit_date': str(getattr(trade, 'exit_date', trade.get('exit_date', '') if hasattr(trade, 'get') else '')),
                        'trade_type': str(getattr(trade, 'trade_type', trade.get('trade_type', '') if hasattr(trade, 'get') else '')),
                        'entry_price': float(getattr(trade, 'entry_price', trade.get('entry_price', 0) if hasattr(trade, 'get') else 0)),
                        'exit_price': float(getattr(trade, 'exit_price', trade.get('exit_price', 0) if hasattr(trade, 'get') else 0)),
                        'quantity': float(getattr(trade, 'quantity', trade.get('quantity', 0) if hasattr(trade, 'get') else 0)),
                        'profit': float(getattr(trade, 'profit', trade.get('profit', 0) if hasattr(trade, 'get') else 0)),
                        'commission': float(getattr(trade, 'commission', trade.get('commission', 0) if hasattr(trade, 'get') else 0)),
                        'bars_held': int(getattr(trade, 'bars_held', trade.get('bars_held', 0) if hasattr(trade, 'get') else 0)),
                        'entry_reason': str(getattr(trade, 'entry_reason', trade.get('entry_reason', '') if hasattr(trade, 'get') else '')),
                        'exit_reason': str(getattr(trade, 'exit_reason', trade.get('exit_reason', '') if hasattr(trade, 'get') else ''))
                    }

                trade_data.append(trade_dict)
            except Exception as e:
                logger.warning(f"Error serializing trade: {str(e)}")
                continue

        return trade_data
    
    def _save_to_json(self, run_id: str, result: StrategyRunResult):
        """Save detailed results to JSON file"""
        try:
            # Load existing data
            data = {}
            if os.path.exists(self.log_file):
                with open(self.log_file, 'r') as f:
                    data = json.load(f)
            
            # Add new result
            data[run_id] = asdict(result)
            
            # Save back to file
            with open(self.log_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"Error saving to JSON: {str(e)}")
    
    def _save_to_csv(self, result: StrategyRunResult):
        """Save summary to CSV file for easy analysis"""
        try:
            # Create summary row
            summary_data = {
                'Timestamp': result.timestamp,
                'Strategy': result.strategy_name,
                'Market': result.market_symbol,
                'Timeframe': result.timeframe_type,
                'Start_Date': result.start_date,
                'End_Date': result.end_date,
                'Total_Bars': result.total_bars,
                'Bar_Size': result.bar_size,
                'Optimization_Metric': result.optimization_metric,
                'Total_Profit': result.total_profit,
                'Total_Trades': result.total_trades,
                'Win_Rate': result.win_rate,
                'Profit_Factor': result.profit_factor,
                'Max_Drawdown': result.max_drawdown,
                'Sharpe_Ratio': result.sharpe_ratio,
                'Calmar_Ratio': result.calmar_ratio,
                'Sortino_Ratio': result.sortino_ratio,
                'DDR_Ratio': result.ddr_ratio,
                'Parameters': json.dumps(result.parameters)
            }
            
            # Convert to DataFrame
            df_new = pd.DataFrame([summary_data])
            
            # Append to existing CSV or create new
            if os.path.exists(self.csv_file):
                df_existing = pd.read_csv(self.csv_file)
                df_combined = pd.concat([df_existing, df_new], ignore_index=True)
            else:
                df_combined = df_new
            
            # Save to CSV
            df_combined.to_csv(self.csv_file, index=False)
            
        except Exception as e:
            logger.error(f"Error saving to CSV: {str(e)}")

    def _save_trades_to_csv(self, run_id: str, result: StrategyRunResult):
        """Save individual trades to CSV file"""
        try:
            if not result.trades:
                return

            # Prepare trade data with run information
            trades_data = []
            for trade in result.trades:
                trade_row = {
                    'Run_ID': run_id,
                    'Strategy': result.strategy_name,
                    'Market': result.market_symbol,
                    'Timestamp': result.timestamp,
                    'Entry_Date': trade.get('entry_date', ''),
                    'Exit_Date': trade.get('exit_date', ''),
                    'Trade_Type': trade.get('trade_type', ''),
                    'Entry_Price': trade.get('entry_price', 0),
                    'Exit_Price': trade.get('exit_price', 0),
                    'Quantity': trade.get('quantity', 0),
                    'Profit': trade.get('profit', 0),
                    'Commission': trade.get('commission', 0),
                    'Bars_Held': trade.get('bars_held', 0),
                    'Entry_Reason': trade.get('entry_reason', ''),
                    'Exit_Reason': trade.get('exit_reason', '')
                }
                trades_data.append(trade_row)

            # Convert to DataFrame
            df_new = pd.DataFrame(trades_data)

            # Append to existing CSV or create new
            if os.path.exists(self.trades_csv_file):
                df_existing = pd.read_csv(self.trades_csv_file)
                df_combined = pd.concat([df_existing, df_new], ignore_index=True)
            else:
                df_combined = df_new

            # Save to CSV
            df_combined.to_csv(self.trades_csv_file, index=False)
            logger.info(f"Saved {len(trades_data)} trades to CSV for run {run_id}")

        except Exception as e:
            logger.error(f"Error saving trades to CSV: {str(e)}")

    def get_strategy_history(self, strategy_name: str = None, limit: int = None) -> List[Dict[str, Any]]:
        """Get historical results for a strategy"""
        try:
            if not os.path.exists(self.log_file):
                return []
            
            with open(self.log_file, 'r') as f:
                data = json.load(f)
            
            results = []
            for run_id, result in data.items():
                if strategy_name is None or result.get('strategy_name') == strategy_name:
                    results.append(result)
            
            # Sort by timestamp (newest first)
            results.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
            
            if limit:
                results = results[:limit]
            
            return results
            
        except Exception as e:
            logger.error(f"Error getting strategy history: {str(e)}")
            return []
    
    def export_results_to_excel(self, filename: str = None) -> str:
        """Export all results to Excel file"""
        try:
            if filename is None:
                filename = f"strategy_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            
            filepath = os.path.join(self.log_directory, filename)
            
            if os.path.exists(self.csv_file):
                df = pd.read_csv(self.csv_file)
                df.to_excel(filepath, index=False)
                logger.info(f"Exported results to: {filepath}")
                return filepath
            else:
                logger.warning("No CSV file found to export")
                return ""
                
        except Exception as e:
            logger.error(f"Error exporting to Excel: {str(e)}")
            return ""

    def export_trades_to_csv(self, run_id: str = None, filepath: str = None) -> str:
        """Export trades to CSV file"""
        try:
            if not os.path.exists(self.trades_csv_file):
                logger.warning("No trades CSV file found")
                return ""

            df = pd.read_csv(self.trades_csv_file)

            # Filter by run_id if specified
            if run_id:
                df = df[df['Run_ID'] == run_id]
                if df.empty:
                    logger.warning(f"No trades found for run ID: {run_id}")
                    return ""

            # Use default filepath if not provided
            if not filepath:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"strategy_trades_export_{timestamp}.csv"
                filepath = os.path.join(self.log_directory, filename)

            df.to_csv(filepath, index=False)
            logger.info(f"Exported {len(df)} trades to: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error exporting trades to CSV: {str(e)}")
            return ""

    def get_trades_for_run(self, run_id: str) -> List[Dict[str, Any]]:
        """Get all trades for a specific run"""
        try:
            if not os.path.exists(self.log_file):
                return []

            with open(self.log_file, 'r') as f:
                data = json.load(f)

            if run_id in data and 'trades' in data[run_id]:
                return data[run_id]['trades'] or []

            return []

        except Exception as e:
            logger.error(f"Error getting trades for run {run_id}: {str(e)}")
            return []
