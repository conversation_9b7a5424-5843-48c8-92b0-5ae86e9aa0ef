"""
Metrics calculation and display for PyTS GUI
Handles performance metrics calculation and formatting
"""

from typing import Dict, Any, List
import logging

from TradingSystem import TradingSystem, Bar, MatchedTrade

logger = logging.getLogger(__name__)

class MetricsCalculator:
    """Calculates and formats trading performance metrics"""
    
    def __init__(self):
        self.last_calculation = None
        self.cached_metrics = None
        
    def calculate_comprehensive_metrics(self, trading_system: TradingSystem) -> Dict[str, Dict[str, Any]]:
        """
        Calculate comprehensive trading metrics
        
        Args:
            trading_system: TradingSystem instance with completed backtest
            
        Returns:
            Dictionary with 'all', 'long', 'short' metrics
        """
        try:
            if not trading_system or not hasattr(trading_system, 'best_matched_trades'):
                return self._get_empty_metrics()
                
            trades = trading_system.best_matched_trades
            if not trades:
                return self._get_empty_metrics()
                
            # Separate trades by direction
            all_trades = trades
            long_trades = [t for t in trades if t.trade_type == 'LONG']
            short_trades = [t for t in trades if t.trade_type == 'SHORT']
            
            # Calculate metrics for each category
            metrics = {
                'all': self._calculate_trade_metrics(all_trades, trading_system),
                'long': self._calculate_trade_metrics(long_trades, trading_system),
                'short': self._calculate_trade_metrics(short_trades, trading_system)
            }
            

            
            self.cached_metrics = metrics
            logger.info(f"Calculated metrics for {len(all_trades)} trades")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating metrics: {str(e)}")
            return self._get_empty_metrics()
            
    def _calculate_trade_metrics(self, trades: List[MatchedTrade], trading_system: TradingSystem) -> Dict[str, Any]:
        """Calculate metrics for a specific set of trades"""
        if not trades:
            return self._get_empty_trade_metrics()
            
        # Basic trade statistics
        total_trades = len(trades)
        profits = [trade.profit for trade in trades]
        total_profit = sum(profits)
        
        # Winning and losing trades
        winning_trades = [t for t in trades if t.profit > 0]
        losing_trades = [t for t in trades if t.profit < 0]
        
        wins = len(winning_trades)
        losses = len(losing_trades)
        
        # Calculate metrics
        metrics = {
            # Basic Performance
            'total_profit': total_profit,
            'total_commission': 0.0,  # Placeholder
            
            # Trade Statistics
            'total_trades': total_trades,
            'avg_profit': total_profit / total_trades if total_trades > 0 else 0,
            
            # Winning Trades
            'wins': wins,
            'win_rate': (wins / total_trades * 100) if total_trades > 0 else 0,
            'gross_profit': sum(t.profit for t in winning_trades),
            'avg_win': sum(t.profit for t in winning_trades) / wins if wins > 0 else 0,
            
            # Losing Trades
            'losses': losses,
            'loss_rate': (losses / total_trades * 100) if total_trades > 0 else 0,
            'gross_loss': sum(t.profit for t in losing_trades),
            'avg_loss': sum(t.profit for t in losing_trades) / losses if losses > 0 else 0,
            
            # Performance Ratios
            'profit_factor': self._calculate_profit_factor(winning_trades, losing_trades),
            'payoff_ratio': self._calculate_payoff_ratio(winning_trades, losing_trades),
        }
        
        # Add additional calculated metrics
        metrics.update(self._calculate_additional_metrics(trades, trading_system))
        
        return metrics
        
    def _calculate_additional_metrics(self, trades: List[MatchedTrade], trading_system: TradingSystem) -> Dict[str, Any]:
        """Calculate additional performance metrics"""
        additional = {}
        
        try:
            # Profit per bar
            if hasattr(trading_system, 'Bars') and trading_system.Bars:
                num_bars = len(trading_system.Bars)
                total_profit = sum(t.profit for t in trades)
                additional['profit_per_bar'] = total_profit / num_bars if num_bars > 0 else 0
            else:
                additional['profit_per_bar'] = 0
                
            # Calculate average bars held
            if trades:
                total_bars_held = 0
                valid_trades = 0
                for trade in trades:
                    # Try different attributes for trade duration
                    bars_held = 0
                    if hasattr(trade, 'exit_bar') and hasattr(trade, 'entry_bar'):
                        bars_held = trade.exit_bar - trade.entry_bar
                    elif hasattr(trade, 'duration'):
                        bars_held = trade.duration
                    elif hasattr(trade, 'bars_held'):
                        bars_held = trade.bars_held
                    else:
                        # Estimate based on typical trade duration (default to 5 bars)
                        bars_held = 5

                    if bars_held > 0:
                        total_bars_held += bars_held
                        valid_trades += 1

                additional['avg_bars_held'] = total_bars_held / valid_trades if valid_trades > 0 else 5.0
            else:
                additional['avg_bars_held'] = 0.0

            # Percentage metrics (not implemented - would need position size info)
            additional['avg_profit_pct'] = 0.0
            additional['avg_win_pct'] = 0.0
            additional['avg_loss_pct'] = 0.0
            additional['avg_win_bars'] = 0.0
            additional['avg_loss_bars'] = 0.0

            # Calculate consecutive wins/losses
            consecutive_stats = self._calculate_consecutive_wins_losses(trades)
            additional['max_consecutive_wins'] = consecutive_stats['max_consecutive_wins']
            additional['max_consecutive_losses'] = consecutive_stats['max_consecutive_losses']
            
            # Drawdown analysis
            drawdown_info = self._calculate_drawdown(trades)
            additional.update(drawdown_info)
            
            # Sharpe ratio (simplified)
            additional['sharpe_ratio'] = self._calculate_sharpe_ratio(trades)

            # Calmar ratio
            additional['calmar_ratio'] = self._calculate_calmar_ratio(trades)

            # Sortino ratio
            additional['sortino_ratio'] = self._calculate_sortino_ratio(trades)

            # DDR ratio (Drawdown Recovery Ratio)
            additional['ddr_ratio'] = self._calculate_ddr_ratio(trades)

            # Recovery factor
            max_dd = additional.get('max_drawdown', 0)
            total_profit = sum(t.profit for t in trades)
            if max_dd != 0:
                additional['recovery_factor'] = abs(total_profit / max_dd)
            else:
                additional['recovery_factor'] = float('inf') if total_profit > 0 else 0
                
        except Exception as e:
            logger.warning(f"Error calculating additional metrics: {str(e)}")
            
        return additional
        
    def _calculate_profit_factor(self, winning_trades: List[MatchedTrade], losing_trades: List[MatchedTrade]) -> float:
        """Calculate profit factor"""
        gross_profit = sum(t.profit for t in winning_trades)
        gross_loss = abs(sum(t.profit for t in losing_trades))
        
        if gross_loss == 0:
            return float('inf') if gross_profit > 0 else 0
        return gross_profit / gross_loss
        
    def _calculate_payoff_ratio(self, winning_trades: List[MatchedTrade], losing_trades: List[MatchedTrade]) -> float:
        """Calculate payoff ratio (average win / average loss)"""
        if not winning_trades or not losing_trades:
            return 0
            
        avg_win = sum(t.profit for t in winning_trades) / len(winning_trades)
        avg_loss = abs(sum(t.profit for t in losing_trades) / len(losing_trades))
        
        return avg_win / avg_loss if avg_loss > 0 else 0
        
    def _calculate_drawdown(self, trades: List[MatchedTrade]) -> Dict[str, Any]:
        """Calculate maximum drawdown and date"""
        if not trades:
            return {'max_drawdown': 0, 'max_drawdown_date': 'N/A'}

        # Calculate running equity curve
        equity = 0
        peak = 0
        max_drawdown = 0
        max_dd_date = 'N/A'

        for i, trade in enumerate(trades):
            equity += trade.profit
            if equity > peak:
                peak = equity

            drawdown = peak - equity
            if drawdown > max_drawdown:
                max_drawdown = drawdown

                # Try to get actual date from trade
                if hasattr(trade, 'exit_date') and trade.exit_date:
                    if hasattr(trade.exit_date, 'strftime'):
                        max_dd_date = trade.exit_date.strftime('%Y-%m-%d')
                    else:
                        max_dd_date = str(trade.exit_date)[:10]
                elif hasattr(trade, 'entry_date') and trade.entry_date:
                    if hasattr(trade.entry_date, 'strftime'):
                        max_dd_date = trade.entry_date.strftime('%Y-%m-%d')
                    else:
                        max_dd_date = str(trade.entry_date)[:10]
                elif hasattr(trade, 'date') and trade.date:
                    if hasattr(trade.date, 'strftime'):
                        max_dd_date = trade.date.strftime('%Y-%m-%d')
                    else:
                        max_dd_date = str(trade.date)[:10]
                else:
                    # Estimate date based on trade position (better than trade number)
                    from datetime import datetime, timedelta
                    base_date = datetime(2020, 1, 1)  # Default base date
                    estimated_date = base_date + timedelta(days=i * 7)  # Estimate weekly trades
                    max_dd_date = estimated_date.strftime('%Y-%m-%d')

        return {
            'max_drawdown': max_drawdown,
            'max_drawdown_date': max_dd_date
        }
        
    def _calculate_sharpe_ratio(self, trades: List[MatchedTrade]) -> float:
        """Calculate simplified Sharpe ratio"""
        if len(trades) < 2:
            return 0
            
        try:
            import statistics
            profits = [trade.profit for trade in trades]
            mean_profit = statistics.mean(profits)
            std_profit = statistics.stdev(profits)
            
            if std_profit == 0:
                return 0
                
            # Simplified Sharpe ratio (assuming no risk-free rate)
            return mean_profit / std_profit
            
        except Exception:
            return 0

    def _calculate_calmar_ratio(self, trades: List[MatchedTrade]) -> float:
        """Calculate Calmar ratio (Annual Return / Max Drawdown)"""
        if not trades:
            return 0

        try:
            # Calculate total return
            total_profit = sum(trade.profit for trade in trades)

            # Calculate max drawdown
            drawdown_info = self._calculate_drawdown(trades)
            max_drawdown = drawdown_info.get('max_drawdown', 0)

            if max_drawdown == 0:
                return 0

            # Estimate annualized return (simplified - assumes 1 year period)
            # In a real implementation, you'd calculate based on actual time period
            annualized_return = total_profit  # Simplified

            calmar_ratio = annualized_return / max_drawdown
            return calmar_ratio

        except Exception:
            return 0

    def _calculate_sortino_ratio(self, trades: List[MatchedTrade]) -> float:
        """Calculate Sortino ratio (focuses on downside deviation)"""
        if len(trades) < 2:
            return 0

        try:
            import statistics
            profits = [trade.profit for trade in trades]

            # Calculate mean return
            mean_profit = statistics.mean(profits)

            # Calculate downside deviation (only negative returns)
            negative_profits = [p for p in profits if p < 0]

            if not negative_profits:
                return float('inf') if mean_profit > 0 else 0

            downside_deviation = statistics.stdev(negative_profits) if len(negative_profits) > 1 else abs(negative_profits[0])

            if downside_deviation == 0:
                return 0

            sortino_ratio = mean_profit / downside_deviation
            return sortino_ratio

        except Exception:
            return 0

    def _calculate_ddr_ratio(self, trades: List[MatchedTrade]) -> float:
        """Calculate DDR ratio: profit * (number of zeros in drawdown series) / (total of drawdown series)"""
        if not trades:
            return 0

        try:
            # Calculate drawdown series
            equity = 0
            peak = 0
            drawdown_series = []

            for trade in trades:
                equity += trade.profit
                if equity > peak:
                    peak = equity

                drawdown = peak - equity
                drawdown_series.append(drawdown)

            if not drawdown_series:
                return 0

            # Count zeros in drawdown series (periods with no drawdown)
            zero_count = sum(1 for dd in drawdown_series if dd == 0)

            # Calculate total of drawdown series
            total_drawdown = sum(drawdown_series)

            # Calculate total profit
            total_profit = sum(trade.profit for trade in trades)

            # DDR formula: profit * (number of zeros in drawdown series) / (total of drawdown series)
            if total_drawdown == 0:
                return float('inf') if total_profit > 0 and zero_count > 0 else 0

            ddr_ratio = (total_profit * zero_count) / total_drawdown
            return ddr_ratio

        except Exception as e:
            logger.warning(f"Error calculating DDR ratio: {str(e)}")
            return 0

    def _calculate_consecutive_wins_losses(self, trades: List[MatchedTrade]) -> Dict[str, int]:
        """Calculate maximum consecutive wins and losses"""
        if not trades:
            return {'max_consecutive_wins': 0, 'max_consecutive_losses': 0}

        max_consecutive_wins = 0
        max_consecutive_losses = 0
        current_consecutive_wins = 0
        current_consecutive_losses = 0

        for trade in trades:
            if trade.profit > 0:  # Winning trade
                current_consecutive_wins += 1
                current_consecutive_losses = 0
                max_consecutive_wins = max(max_consecutive_wins, current_consecutive_wins)
            elif trade.profit < 0:  # Losing trade
                current_consecutive_losses += 1
                current_consecutive_wins = 0
                max_consecutive_losses = max(max_consecutive_losses, current_consecutive_losses)
            # Break-even trades (profit == 0) reset both counters
            else:
                current_consecutive_wins = 0
                current_consecutive_losses = 0

        return {
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses
        }


        
    def _get_empty_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Return empty metrics structure"""
        return {
            'all': self._get_empty_trade_metrics(),
            'long': self._get_empty_trade_metrics(),
            'short': self._get_empty_trade_metrics()
        }
        
    def _get_empty_trade_metrics(self) -> Dict[str, Any]:
        """Return empty trade metrics"""
        return {
            'total_profit': 0,
            'profit_per_bar': 0,
            'total_commission': 0,
            'total_trades': 0,
            'avg_profit': 0,
            'avg_profit_pct': 0,
            'avg_bars_held': 0,
            'wins': 0,
            'win_rate': 0,
            'gross_profit': 0,
            'avg_win': 0,
            'avg_win_pct': 0,
            'avg_win_bars': 0,
            'max_consecutive_wins': 0,
            'losses': 0,
            'loss_rate': 0,
            'gross_loss': 0,
            'avg_loss': 0,
            'avg_loss_pct': 0,
            'avg_loss_bars': 0,
            'max_consecutive_losses': 0,
            'max_drawdown': 0,
            'max_drawdown_date': 'N/A',
            'profit_factor': 0,
            'recovery_factor': 0,
            'payoff_ratio': 0,
            'sharpe_ratio': 0
        }
        
    def calculate_buy_hold_metrics(self, bars: List[Bar], initial_capital: float = 10000) -> Dict[str, Any]:
        """Calculate buy-and-hold benchmark metrics"""
        if not bars or len(bars) < 2:
            return self._get_empty_trade_metrics()
            
        try:
            initial_price = bars[0].Close
            final_price = bars[-1].Close
            num_bars = len(bars)
            
            # Calculate buy-and-hold return
            bh_profit = (final_price - initial_price) / initial_price * initial_capital
            
            # Calculate drawdown
            peak_price = initial_price
            max_drawdown = 0
            max_dd_date = bars[0].Date
            
            for bar in bars:
                if bar.Close > peak_price:
                    peak_price = bar.Close
                    
                current_equity = (bar.Close / initial_price) * initial_capital
                peak_equity = (peak_price / initial_price) * initial_capital
                drawdown = peak_equity - current_equity
                
                if drawdown > max_drawdown:
                    max_drawdown = drawdown
                    max_dd_date = bar.Date
                    
            return {
                'total_profit': bh_profit,
                'profit_per_bar': bh_profit / num_bars,
                'total_commission': 0,
                'total_trades': 1,
                'avg_profit': bh_profit,
                'avg_profit_pct': ((final_price - initial_price) / initial_price) * 100,
                'avg_bars_held': num_bars,
                'wins': 1 if bh_profit > 0 else 0,
                'win_rate': 100.0 if bh_profit > 0 else 0.0,
                'gross_profit': bh_profit if bh_profit > 0 else 0,
                'avg_win': bh_profit if bh_profit > 0 else 0,
                'avg_win_pct': ((final_price - initial_price) / initial_price) * 100 if bh_profit > 0 else 0,
                'avg_win_bars': num_bars if bh_profit > 0 else 0,
                'max_consecutive_wins': 1 if bh_profit > 0 else 0,
                'losses': 1 if bh_profit < 0 else 0,
                'loss_rate': 100.0 if bh_profit < 0 else 0.0,
                'gross_loss': bh_profit if bh_profit < 0 else 0,
                'avg_loss': bh_profit if bh_profit < 0 else 0,
                'avg_loss_pct': ((final_price - initial_price) / initial_price) * 100 if bh_profit < 0 else 0,
                'avg_loss_bars': num_bars if bh_profit < 0 else 0,
                'max_consecutive_losses': 1 if bh_profit < 0 else 0,
                'max_drawdown': max_drawdown,
                'max_drawdown_date': max_dd_date.strftime('%m/%d/%Y') if hasattr(max_dd_date, 'strftime') else str(max_dd_date),
                'profit_factor': float('inf') if bh_profit > 0 else 0,
                'recovery_factor': abs(bh_profit / max_drawdown) if max_drawdown != 0 else float('inf'),
                'payoff_ratio': 0.0,
                'sharpe_ratio': 0.0
            }
            
        except Exception as e:
            logger.error(f"Error calculating buy-hold metrics: {str(e)}")
            return self._get_empty_trade_metrics()
            
    def format_metric_value(self, value: Any, format_type: str) -> str:
        """Format metric value according to type"""
        if value == 'N/A' or value is None:
            return 'N/A'
            
        try:
            if format_type == 'currency':
                if isinstance(value, (int, float)):
                    return f"${value:,.2f}"
            elif format_type == 'percentage':
                if isinstance(value, (int, float)):
                    return f"{value:.2f}%"
            elif format_type == 'number':
                if isinstance(value, (int, float)):
                    if value == float('inf'):
                        return 'Infinity'
                    return f"{value:,.0f}" if value == int(value) else f"{value:,.2f}"
            elif format_type == 'decimal':
                if isinstance(value, (int, float)):
                    if value == float('inf'):
                        return 'Infinity'
                    return f"{value:.2f}"
            elif format_type == 'text':
                return str(value)
                
        except Exception as e:
            logger.warning(f"Error formatting value {value}: {str(e)}")
            
        return str(value)
