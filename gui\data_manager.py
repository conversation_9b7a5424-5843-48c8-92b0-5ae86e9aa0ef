"""
Data management functionality for PyTS GUI
Handles data loading, resampling, and validation
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Optional, Dict, Any
import logging

from TradingSystem import Bar
from .constants import FREQUENCY_MAP, CSV_COLUMN_MAPPINGS, ERROR_MESSAGES

logger = logging.getLogger(__name__)

class DataManager:
    """Manages data loading, validation, and resampling operations"""
    
    def __init__(self):
        self.original_data: Optional[List[Bar]] = None
        self.current_data: Optional[List[Bar]] = None
        self.data_info: Dict[str, Any] = {}
        
    def load_csv_data(self, file_path: str, progress_callback=None) -> List[Bar]:
        """
        Load data from CSV file with comprehensive validation and error handling
        
        Args:
            file_path: Path to CSV file
            progress_callback: Optional callback for progress updates
            
        Returns:
            List of Bar objects
            
        Raises:
            ValueError: If CSV format is invalid
            FileNotFoundError: If file doesn't exist
        """
        try:
            if progress_callback:
                progress_callback(10, "Reading CSV file...")
                
            # Detect CSV format and load with optimized settings
            df = self._load_and_validate_csv(file_path)
            
            if progress_callback:
                progress_callback(30, "Validating data...")
                
            # Validate required columns
            self._validate_columns(df)
            
            if progress_callback:
                progress_callback(50, "Converting to Bar objects...")
                
            # Convert to Bar objects efficiently
            bars = self._convert_to_bars(df)
            
            if progress_callback:
                progress_callback(80, "Finalizing...")
                
            # Store data and update info
            self.original_data = bars.copy()
            self.current_data = bars
            self._update_data_info()
            
            if progress_callback:
                progress_callback(100, "Data loaded successfully")
                
            logger.info(f"Successfully loaded {len(bars)} bars from {file_path}")
            return bars
            
        except Exception as e:
            logger.error(f"Error loading CSV data: {str(e)}")
            raise
            
    def _load_and_validate_csv(self, file_path: str) -> pd.DataFrame:
        """Load CSV with optimized pandas settings"""
        try:
            # Try different date parsing strategies
            date_parsers = [
                lambda x: pd.to_datetime(x, infer_datetime_format=True),
                lambda x: pd.to_datetime(x, format='%Y-%m-%d'),
                lambda x: pd.to_datetime(x, format='%m/%d/%Y'),
                lambda x: pd.to_datetime(x, format='%d/%m/%Y')
            ]
            
            df = None
            for date_parser in date_parsers:
                try:
                    df = pd.read_csv(
                        file_path,
                        dtype={
                            'Open': 'float64',
                            'High': 'float64',
                            'Low': 'float64', 
                            'Close': 'float64',
                            'Volume': 'int64'
                        },
                        parse_dates=['Date'],
                        date_parser=date_parser,
                        engine='c',
                        memory_map=True
                    )
                    break
                except:
                    continue
                    
            if df is None:
                # Fallback without date parsing
                df = pd.read_csv(file_path, engine='c', memory_map=True)
                df['Date'] = pd.to_datetime(df['Date'], infer_datetime_format=True)
                
            return df.sort_values('Date').reset_index(drop=True)
            
        except Exception as e:
            raise ValueError(f"Failed to load CSV file: {str(e)}")
            
    def _validate_columns(self, df: pd.DataFrame) -> None:
        """Validate that required columns are present"""
        required_cols = CSV_COLUMN_MAPPINGS['required_columns']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            available_cols = list(df.columns)
            raise ValueError(
                f"Missing required columns: {missing_cols}. "
                f"Available columns: {available_cols}. "
                f"Required columns: {required_cols}"
            )
            
        # Validate data types
        numeric_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
        for col in numeric_cols:
            if col in df.columns:
                try:
                    pd.to_numeric(df[col], errors='raise')
                except:
                    raise ValueError(f"Column '{col}' contains non-numeric data")
                    
    def _convert_to_bars(self, df: pd.DataFrame) -> List[Bar]:
        """Convert DataFrame to Bar objects efficiently"""
        bars = []
        
        # Use vectorized operations for better performance
        dates = df['Date'].values
        opens = df['Open'].astype(float).values
        highs = df['High'].astype(float).values
        lows = df['Low'].astype(float).values
        closes = df['Close'].astype(float).values
        volumes = df['Volume'].astype(int).values
        
        for i in range(len(df)):
            bar = Bar(
                Date=pd.to_datetime(dates[i]),
                Open=opens[i],
                High=highs[i],
                Low=lows[i],
                Close=closes[i],
                Volume=volumes[i]
            )
            bars.append(bar)
            
        return bars
        
    def resample_data(self, bar_size: str) -> Optional[List[Bar]]:
        """
        Resample data to specified bar size
        
        Args:
            bar_size: Target bar size (e.g., "30 Minutes", "Daily")
            
        Returns:
            Resampled Bar objects or None if error
        """
        if not self.original_data:
            logger.warning("No original data available for resampling")
            return None
            
        if bar_size not in FREQUENCY_MAP:
            logger.warning(f"Unsupported bar size: {bar_size}")
            return self.original_data
            
        try:
            freq = FREQUENCY_MAP[bar_size]
            
            # Convert to DataFrame
            df_data = []
            for bar in self.original_data:
                df_data.append({
                    'Date': bar.Date,
                    'Open': bar.Open,
                    'High': bar.High,
                    'Low': bar.Low,
                    'Close': bar.Close,
                    'Volume': bar.Volume
                })
                
            df = pd.DataFrame(df_data)
            df.set_index('Date', inplace=True)
            
            # Resample with proper OHLCV aggregation
            resampled = df.resample(freq).agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            }).dropna()
            
            # Convert back to Bar objects
            resampled_bars = []
            for date, row in resampled.iterrows():
                bar = Bar(
                    Date=date,
                    Open=row['Open'],
                    High=row['High'],
                    Low=row['Low'],
                    Close=row['Close'],
                    Volume=int(row['Volume'])
                )
                resampled_bars.append(bar)
                
            self.current_data = resampled_bars
            self._update_data_info()
            
            logger.info(f"Resampled data: {len(self.original_data)} → {len(resampled_bars)} bars ({bar_size})")
            return resampled_bars
            
        except Exception as e:
            logger.error(f"Error resampling data: {str(e)}")
            return self.original_data
            
    def filter_by_date_range(self, start_date: datetime, end_date: datetime) -> List[Bar]:
        """Filter current data by date range"""
        if not self.current_data:
            return []
            
        filtered_data = [
            bar for bar in self.current_data
            if start_date <= bar.Date <= end_date
        ]
        
        logger.info(f"Filtered data: {len(self.current_data)} → {len(filtered_data)} bars")
        return filtered_data
        
    def get_data_summary(self) -> Dict[str, Any]:
        """Get summary statistics of current data"""
        if not self.current_data:
            return {}
            
        data = self.current_data
        return {
            'total_bars': len(data),
            'date_range': {
                'start': data[0].Date,
                'end': data[-1].Date
            },
            'price_range': {
                'min': min(bar.Low for bar in data),
                'max': max(bar.High for bar in data)
            },
            'volume_stats': {
                'total': sum(bar.Volume for bar in data),
                'average': sum(bar.Volume for bar in data) / len(data)
            }
        }
        
    def _update_data_info(self) -> None:
        """Update internal data information"""
        if self.current_data:
            self.data_info = self.get_data_summary()
            
    def clear_data(self) -> None:
        """Clear all loaded data"""
        self.original_data = None
        self.current_data = None
        self.data_info = {}
        logger.info("Data cleared")
        
    @property
    def has_data(self) -> bool:
        """Check if data is loaded"""
        return self.current_data is not None and len(self.current_data) > 0
        
    @property
    def data_count(self) -> int:
        """Get number of bars in current data"""
        return len(self.current_data) if self.current_data else 0
