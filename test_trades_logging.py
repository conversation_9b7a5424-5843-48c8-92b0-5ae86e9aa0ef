#!/usr/bin/env python3
"""
Test script to verify trades logging functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.strategy_results_logger import StrategyResultsLogger
from datetime import datetime, timedelta
import json

def create_sample_trades():
    """Create sample trade data for testing"""
    trades = []
    base_date = datetime(2024, 1, 1)
    
    for i in range(5):
        entry_date = base_date + timedelta(days=i*10)
        exit_date = entry_date + timedelta(days=3)
        
        trade = {
            'entry_date': entry_date.strftime('%Y-%m-%d'),
            'exit_date': exit_date.strftime('%Y-%m-%d'),
            'trade_type': 'Long' if i % 2 == 0 else 'Short',
            'entry_price': 100.0 + i * 5,
            'exit_price': 105.0 + i * 5 if i % 2 == 0 else 95.0 + i * 5,
            'quantity': 100,
            'profit': 500.0 if i % 2 == 0 else -500.0,
            'commission': 2.0,
            'bars_held': 3,
            'entry_reason': f'Signal {i+1}',
            'exit_reason': f'Exit {i+1}'
        }
        trades.append(trade)
    
    return trades

def create_sample_metrics():
    """Create sample metrics data"""
    metrics = {
        'total_profit': 1500.0,
        'total_trades': 5,
        'win_rate': 60.0,
        'profit_factor': 1.5,
        'max_drawdown': -500.0,
        'sharpe_ratio': 1.2,
        'calmar_ratio': 2.0,
        'sortino_ratio': 1.8,
        'DD_ratio': 3.0
    }
    
    long_metrics = {
        'total_profit': 1000.0,
        'total_trades': 3,
        'win_rate': 66.7,
        'profit_factor': 2.0,
        'max_drawdown': -200.0,
        'sharpe_ratio': 1.5,
        'calmar_ratio': 2.5,
        'sortino_ratio': 2.0,
        'DD_ratio': 5.0
    }
    
    short_metrics = {
        'total_profit': 500.0,
        'total_trades': 2,
        'win_rate': 50.0,
        'profit_factor': 1.0,
        'max_drawdown': -300.0,
        'sharpe_ratio': 0.8,
        'calmar_ratio': 1.5,
        'sortino_ratio': 1.5,
        'DD_ratio': 1.7
    }
    
    buy_hold_metrics = {
        'total_profit': 2000.0,
        'total_trades': 1,
        'win_rate': 100.0,
        'profit_factor': float('inf'),
        'max_drawdown': -100.0,
        'sharpe_ratio': 0.9,
        'calmar_ratio': 20.0,
        'sortino_ratio': 1.2
    }
    
    return metrics, long_metrics, short_metrics, buy_hold_metrics

def test_trades_logging():
    """Test the trades logging functionality"""
    print("Testing trades logging functionality...")
    
    # Create logger
    logger = StrategyResultsLogger()
    
    # Create sample data
    trades = create_sample_trades()
    all_metrics, long_metrics, short_metrics, buy_hold_metrics = create_sample_metrics()
    
    print(f"Created {len(trades)} sample trades")
    
    # Test parameters
    parameters = {
        'LE': 4,
        'SE': 3,
        'UseADX': True,
        'ADXPeriod': 14
    }
    
    additional_info = {
        'bar_size': 'Daily',
        'walk_forward': False,
        'train_period': 12,
        'test_period': 3,
        'period_unit': 'months'
    }
    
    # Log with trades included
    print("Logging strategy run with trades included...")
    run_id = logger.log_strategy_run(
        strategy_name="TestTradesStrategy",
        market_symbol="TEST",
        data_file="test_data.csv",
        timeframe_type="Last Year",
        start_date="2024-01-01",
        end_date="2024-12-31",
        total_bars=252,
        parameters=parameters,
        metrics=all_metrics,
        buy_hold_metrics=buy_hold_metrics,
        optimization_metric="total_profit",
        additional_info=additional_info,
        trades=trades,
        include_trades=True
    )
    
    print(f"Logged run with ID: {run_id}")
    
    # Verify the trades were saved
    print("Verifying trades were saved...")
    
    # Check JSON file
    json_file = os.path.join("strategy_logs", "strategy_results.json")
    if os.path.exists(json_file):
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        if run_id in data:
            saved_trades = data[run_id].get('trades')
            if saved_trades:
                print(f"✓ Found {len(saved_trades)} trades in JSON file")
                print(f"  First trade: {saved_trades[0]}")
            else:
                print("✗ No trades found in JSON file")
        else:
            print(f"✗ Run ID {run_id} not found in JSON file")
    else:
        print("✗ JSON file not found")
    
    # Check CSV trades file
    trades_csv_file = os.path.join("strategy_logs", "strategy_trades.csv")
    if os.path.exists(trades_csv_file):
        import pandas as pd
        df = pd.read_csv(trades_csv_file)
        print(f"✓ Found {len(df)} trades in CSV file")
        print(f"  Columns: {list(df.columns)}")
        if len(df) > 0:
            print(f"  Sample row: {df.iloc[0].to_dict()}")
    else:
        print("✗ Trades CSV file not found")
    
    print("Test completed!")

if __name__ == "__main__":
    test_trades_logging()
