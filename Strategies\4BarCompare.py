import pandas as pd
import numpy as np
from datetime import datetime, time
from Tools import Tools
from TradingSystem import TradingSystem, Bar, Trade, MatchedTrade
from TA import TA

class Params:
    training_months = 12
    testing_months = 3

    param_ranges = {
        "LE": (1, 20),
        "SE": (1, 20),
        "LX": (1, 20),
        "SX": (1, 20),
        "ADXPeriod": (2, 30)
    }

    # Bayesian optimization settings
    bayesian_n_calls = 20  # Number of evaluations for Bayesian optimization
    bayesian_n_initial_points = 10  # Number of random initial points

    current_params = {
        "LE": 2,
        "SE": 2,
        "LX": 2,
        "SX": 2,
        "ADXPeriod": 14
    }

    bPlot = False

class MyTradingSystem(TradingSystem):
    def __init__(self, bars, resampled_bars=None):
        super().__init__(bars, resampled_bars)

    def process_bar(self, bar):
        # Ensure we have enough bars for calculations
        min_bars_needed = max(
            Params.current_params["LE"],
            Params.current_params["SE"],
            Params.current_params["LX"],
            Params.current_params["SX"],
            Params.current_params["ADXPeriod"]
        )

        if bar < min_bars_needed:
            return


        adx_value = TA.ADX(bar, self.Bars, Params.current_params["ADXPeriod"])

        # Exit logic
        if self.Active_Position() != "":
            if self.Active_Position() == "Long":
                if self.Bars[bar].Close > self.Bars[bar-Params.current_params["LX"]].Close:
                    self.sell(bar + 1, 1, "market", 0, "LX")
            elif self.Active_Position() == "Short":
                if self.Bars[bar].Close < self.Bars[bar-Params.current_params["SX"]].Close:
                    self.buy(bar + 1, 1, "market", 0, "SX")
        else:
            # Entry logic - only trade when ADX shows trending market
            if adx_value >= 25:  # Only trade when there's a trend
                # Long entry: current close is lower than LE bars ago
                if self.Bars[bar].Close < self.Bars[bar-Params.current_params["LE"]].Close:
                    self.buy(bar + 1, 1, "market", 0, "LE")
                # Short entry: current close is higher than SE bars ago
                elif self.Bars[bar].Close > self.Bars[bar-Params.current_params["SE"]].Close:
                    self.sell(bar + 1, 1, "market", 0, "SE")












