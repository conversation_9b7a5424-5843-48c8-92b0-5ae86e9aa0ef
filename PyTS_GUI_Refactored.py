"""
PyTS GUI - Refactored and Optimized Version
Professional Trading System GUI with clean architecture
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
import os
import sys
import re
import keyword

# Add gui module to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.constants import *
from gui.data_manager import DataManager
from gui.strategy_manager import StrategyManager
from gui.theme_manager import ThemeManager
from gui.metrics_calculator import MetricsCalculator
from gui.strategy_results_logger import StrategyResultsLogger
from gui.strategy_results_viewer import StrategyResultsViewer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pyts_gui.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class PyTSGUI:
    """
    Main GUI class for PyTS Trading System
    Refactored for better maintainability and performance
    """
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        
        # Initialize managers
        self.data_manager = DataManager()
        self.strategy_manager = StrategyManager()
        self.theme_manager = ThemeManager(self.root)
        self.metrics_calculator = MetricsCalculator()
        self.results_logger = StrategyResultsLogger()
        
        # GUI state
        self.trading_system = None
        self.backtest_results = None
        self.optimization_thread = None
        
        # Initialize GUI components
        self.setup_gui()
        self.setup_theme()

        # Now safe to use log_message and load strategies
        self.log_message("PyTS GUI initialized successfully")

        # Load available strategies after all GUI components are created
        self.refresh_strategies()
        
    def setup_window(self) -> None:
        """Configure main window properties"""
        self.root.title(f"{APP_TITLE} v{APP_VERSION}")
        self.root.geometry(DEFAULT_GEOMETRY)
        self.root.minsize(1200, 800)
        
        # Center window on screen
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (self.root.winfo_width() // 2)
        y = (self.root.winfo_screenheight() // 2) - (self.root.winfo_height() // 2)
        self.root.geometry(f"+{x}+{y}")
        
    def setup_gui(self) -> None:
        """Initialize GUI components"""
        self.create_menu()
        self.create_main_layout()
        self.create_status_bar()
        
    def setup_theme(self) -> None:
        """Apply theme to GUI"""
        self.theme_manager.setup_dark_theme()
        self.theme_manager.apply_theme_to_children(self.root)
        
    def create_menu(self) -> None:
        """Create application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Load Data...", command=self.load_data, accelerator="Ctrl+O")
        file_menu.add_separator()
        file_menu.add_command(label="New Strategy...", command=self.create_new_strategy)
        file_menu.add_command(label="Save Strategy", command=self.save_current_strategy, accelerator="Ctrl+S")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit, accelerator="Ctrl+Q")
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Optimize Parameters", command=self.run_optimization)
        tools_menu.add_command(label="Validate Strategy", command=self.validate_current_strategy)
        tools_menu.add_separator()
        tools_menu.add_command(label="Reset Theme", command=self.theme_manager.reset_theme)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        
        # Bind keyboard shortcuts
        self.root.bind('<Control-o>', lambda e: self.load_data())
        self.root.bind('<Control-s>', lambda e: self.save_current_strategy())
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.bind('<F5>', lambda e: self._apply_syntax_highlighting())
        
    def create_main_layout(self) -> None:
        """Create main GUI layout with notebook tabs"""
        # Create main notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create tabs
        self.create_data_tab()
        self.create_strategy_tab()
        self.create_parameters_tab()
        self.create_results_tab()
        self.create_heatmap_tab()
        self.create_strategy_history_tab()
        self.create_bulk_backtest_tab()
        self.create_log_tab()
        
    def create_data_tab(self) -> None:
        """Create data management tab"""
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text="Data")
        
        # Data controls frame
        controls_frame = ttk.LabelFrame(self.data_frame, text="Data Controls")
        controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # File selection
        file_frame = ttk.Frame(controls_frame)
        file_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.theme_manager.create_themed_button(file_frame, text="Load CSV Data",
                                               command=self.load_data).pack(side=tk.LEFT, padx=(0, 10))
        
        self.file_label = ttk.Label(file_frame, text="No file selected")
        self.file_label.pack(side=tk.LEFT)
        
        # Bar size controls
        controls_grid = ttk.Frame(controls_frame)
        controls_grid.pack(fill=tk.X, padx=5, pady=5)

        # Bar size
        ttk.Label(controls_grid, text="Bar Size:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.bar_size_var = tk.StringVar(value=DEFAULTS['bar_size'])
        self.bar_size_combo = ttk.Combobox(controls_grid, textvariable=self.bar_size_var,
                                          values=BAR_SIZE_OPTIONS, state="readonly", width=12)
        self.bar_size_combo.grid(row=0, column=1, padx=(0, 15))
        self.bar_size_combo.bind('<<ComboboxSelected>>', self.on_bar_size_changed)
        
        # Date range controls (initially hidden)
        self.date_range_frame = ttk.Frame(controls_frame)
        
        # Data information display
        info_frame = ttk.LabelFrame(self.data_frame, text="Data Information")
        info_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.data_info_text = tk.Text(info_frame, height=10, wrap=tk.WORD,
                                     bg='#3c3c3c', fg='#ffffff',
                                     insertbackground='#ffffff',
                                     selectbackground='#404040',
                                     selectforeground='#ffffff')
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.data_info_text.yview)
        self.data_info_text.configure(yscrollcommand=info_scrollbar.set)

        self.data_info_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        info_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def create_strategy_tab(self) -> None:
        """Create strategy management tab"""
        self.strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.strategy_frame, text="Strategy")
        
        # Strategy selection
        selection_frame = ttk.LabelFrame(self.strategy_frame, text="Strategy Selection")
        selection_frame.pack(fill=tk.X, padx=5, pady=5)
        
        strategy_controls = ttk.Frame(selection_frame)
        strategy_controls.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(strategy_controls, text="Strategy:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.strategy_var = tk.StringVar()
        self.strategy_combo = ttk.Combobox(strategy_controls, textvariable=self.strategy_var,
                                          state="readonly", width=20)
        self.strategy_combo.pack(side=tk.LEFT, padx=(0, 10))
        self.strategy_combo.bind('<<ComboboxSelected>>', self.on_strategy_selected)
        
        self.theme_manager.create_themed_button(strategy_controls, text="Refresh",
                                               command=self.refresh_strategies).pack(side=tk.LEFT, padx=(0, 10))
        self.theme_manager.create_themed_button(strategy_controls, text="New Strategy",
                                               command=self.create_new_strategy).pack(side=tk.LEFT)
        
        # Strategy code editor
        editor_frame = ttk.LabelFrame(self.strategy_frame, text="Strategy Code")
        editor_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Editor toolbar
        toolbar = ttk.Frame(editor_frame)
        toolbar.pack(fill=tk.X, padx=5, pady=5)

        self.theme_manager.create_themed_button(toolbar, text="Save", command=self.save_current_strategy).pack(side=tk.LEFT, padx=(0, 5))
        self.theme_manager.create_themed_button(toolbar, text="Validate", command=self.validate_current_strategy).pack(side=tk.LEFT, padx=(0, 10))

        # Syntax status indicator
        self.syntax_status_label = ttk.Label(toolbar, text="Syntax: OK", foreground="green")
        self.syntax_status_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # Code editor with line numbers
        editor_container = ttk.Frame(editor_frame)
        editor_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Line numbers widget
        self.line_numbers = tk.Text(editor_container, width=4, padx=3, takefocus=0,
                                   border=0, state='disabled', wrap='none',
                                   font=('Consolas', 10),
                                   bg='#2b2b2b', fg='#888888',
                                   selectbackground='#2b2b2b',
                                   selectforeground='#888888')

        # Main text editor
        self.strategy_text = tk.Text(editor_container, wrap=tk.NONE, font=('Consolas', 10),
                                    bg='#3c3c3c', fg='#ffffff',
                                    insertbackground='#ffffff',
                                    selectbackground='#404040',
                                    selectforeground='#ffffff')

        # Scrollbars for code editor
        v_scrollbar = ttk.Scrollbar(editor_container, orient=tk.VERTICAL, command=self._on_text_scroll)
        h_scrollbar = ttk.Scrollbar(editor_container, orient=tk.HORIZONTAL, command=self.strategy_text.xview)
        self.strategy_text.configure(yscrollcommand=self._on_text_configure, xscrollcommand=h_scrollbar.set)

        # Grid layout
        self.line_numbers.grid(row=0, column=0, sticky='ns')
        self.strategy_text.grid(row=0, column=1, sticky='nsew')
        v_scrollbar.grid(row=0, column=2, sticky='ns')
        h_scrollbar.grid(row=1, column=1, sticky='ew')

        editor_container.grid_rowconfigure(0, weight=1)
        editor_container.grid_columnconfigure(1, weight=1)

        # Bind events for line number updates and syntax highlighting
        self.strategy_text.bind('<KeyPress>', self._on_text_change)
        self.strategy_text.bind('<KeyRelease>', self._on_text_change)
        self.strategy_text.bind('<Button-1>', self._on_text_change)
        self.strategy_text.bind('<MouseWheel>', self._on_text_change)

        # Initialize syntax highlighting
        self._setup_syntax_highlighting()

        # Initialize line numbers
        self._update_line_numbers()

    def _on_text_scroll(self, *args):
        """Handle scrollbar movement to sync line numbers and text"""
        self.strategy_text.yview(*args)
        self.line_numbers.yview(*args)

    def _on_text_configure(self, *args):
        """Handle text widget scroll configuration"""
        # Find and update the vertical scrollbar
        try:
            for child in self.strategy_text.master.winfo_children():
                if isinstance(child, ttk.Scrollbar) and child.cget('orient') == 'vertical':
                    child.set(*args)
                    break
        except:
            pass
        # Sync line numbers view
        if len(args) >= 1:
            self.line_numbers.yview_moveto(float(args[0]))

    def _on_text_change(self, event=None):
        """Handle text changes to update line numbers and syntax highlighting"""
        # Schedule line number update and syntax highlighting after the current event
        self.root.after_idle(self._update_line_numbers)
        self.root.after_idle(self._apply_syntax_highlighting)

    def _update_line_numbers(self):
        """Update line numbers display"""
        try:
            # Get the number of lines in the text widget
            line_count = int(self.strategy_text.index('end-1c').split('.')[0])

            # Generate line numbers
            line_numbers_text = '\n'.join(str(i) for i in range(1, line_count + 1))

            # Update line numbers widget
            self.line_numbers.config(state='normal')
            self.line_numbers.delete('1.0', 'end')
            self.line_numbers.insert('1.0', line_numbers_text)
            self.line_numbers.config(state='disabled')

            # Sync the view
            self.line_numbers.yview_moveto(self.strategy_text.yview()[0])

        except Exception as e:
            # Silently handle any errors during line number updates
            pass

    def _setup_syntax_highlighting(self):
        """Setup syntax highlighting tags for Python code"""
        # Define color scheme for syntax highlighting
        self.syntax_colors = {
            'keyword': '#569cd6',      # Blue for keywords
            'string': '#ce9178',       # Orange for strings
            'comment': '#6a9955',      # Green for comments
            'number': '#b5cea8',       # Light green for numbers
            'operator': '#d4d4d4',     # Light gray for operators
            'builtin': '#4ec9b0',      # Cyan for built-ins
            'class': '#4ec9b0',        # Cyan for class names
            'function': '#dcdcaa',     # Yellow for function names
            'error': '#f44747'         # Red for errors
        }

        # Configure text tags for syntax highlighting
        for tag_name, color in self.syntax_colors.items():
            self.strategy_text.tag_configure(tag_name, foreground=color)

        # Special configuration for error highlighting
        self.strategy_text.tag_configure('error', background='#3c1e1e', foreground='#f44747')

    def _apply_syntax_highlighting(self):
        """Apply syntax highlighting to the current text"""
        try:
            # Get all text content
            content = self.strategy_text.get(1.0, tk.END)

            # Clear existing tags
            for tag_name in self.syntax_colors.keys():
                self.strategy_text.tag_remove(tag_name, 1.0, tk.END)

            # Apply syntax highlighting
            self._highlight_python_syntax(content)

            # Check for syntax errors and highlight them
            self._highlight_syntax_errors(content)

        except Exception as e:
            # Silently handle any errors during syntax highlighting
            pass

    def _highlight_python_syntax(self, content):
        """Apply Python syntax highlighting to the text content"""
        lines = content.split('\n')

        for line_num, line in enumerate(lines, 1):
            line_start = f"{line_num}.0"

            # Skip empty lines
            if not line.strip():
                continue

            # Highlight comments
            comment_match = re.search(r'#.*$', line)
            if comment_match:
                start_col = comment_match.start()
                end_col = comment_match.end()
                start_pos = f"{line_num}.{start_col}"
                end_pos = f"{line_num}.{end_col}"
                self.strategy_text.tag_add('comment', start_pos, end_pos)
                # Don't process the rest of the line after comment
                line = line[:start_col]

            # Highlight strings (both single and double quotes)
            for string_pattern in [r'"[^"]*"', r"'[^']*'"]:
                for match in re.finditer(string_pattern, line):
                    start_pos = f"{line_num}.{match.start()}"
                    end_pos = f"{line_num}.{match.end()}"
                    self.strategy_text.tag_add('string', start_pos, end_pos)

            # Highlight numbers
            for match in re.finditer(r'\b\d+\.?\d*\b', line):
                start_pos = f"{line_num}.{match.start()}"
                end_pos = f"{line_num}.{match.end()}"
                self.strategy_text.tag_add('number', start_pos, end_pos)

            # Highlight Python keywords
            python_keywords = keyword.kwlist + ['True', 'False', 'None']
            for kw in python_keywords:
                pattern = r'\b' + re.escape(kw) + r'\b'
                for match in re.finditer(pattern, line):
                    start_pos = f"{line_num}.{match.start()}"
                    end_pos = f"{line_num}.{match.end()}"
                    self.strategy_text.tag_add('keyword', start_pos, end_pos)

            # Highlight built-in functions
            builtins = ['print', 'len', 'range', 'str', 'int', 'float', 'list', 'dict', 'tuple', 'set',
                       'abs', 'max', 'min', 'sum', 'round', 'sorted', 'reversed', 'enumerate', 'zip']
            for builtin in builtins:
                pattern = r'\b' + re.escape(builtin) + r'\b'
                for match in re.finditer(pattern, line):
                    start_pos = f"{line_num}.{match.start()}"
                    end_pos = f"{line_num}.{match.end()}"
                    self.strategy_text.tag_add('builtin', start_pos, end_pos)

            # Highlight class definitions
            class_match = re.search(r'\bclass\s+(\w+)', line)
            if class_match:
                start_pos = f"{line_num}.{class_match.start(1)}"
                end_pos = f"{line_num}.{class_match.end(1)}"
                self.strategy_text.tag_add('class', start_pos, end_pos)

            # Highlight function definitions
            func_match = re.search(r'\bdef\s+(\w+)', line)
            if func_match:
                start_pos = f"{line_num}.{func_match.start(1)}"
                end_pos = f"{line_num}.{func_match.end(1)}"
                self.strategy_text.tag_add('function', start_pos, end_pos)

            # Highlight operators
            operators = ['+', '-', '*', '/', '//', '%', '**', '=', '==', '!=', '<', '>', '<=', '>=',
                        '&', '|', '^', '~', '<<', '>>', 'and', 'or', 'not', 'in', 'is']
            for op in operators:
                if op.isalpha():
                    pattern = r'\b' + re.escape(op) + r'\b'
                else:
                    pattern = re.escape(op)
                for match in re.finditer(pattern, line):
                    start_pos = f"{line_num}.{match.start()}"
                    end_pos = f"{line_num}.{match.end()}"
                    self.strategy_text.tag_add('operator', start_pos, end_pos)

    def _highlight_syntax_errors(self, content):
        """Highlight syntax errors in the code and update status"""
        try:
            # Try to compile the code to find syntax errors
            compile(content, '<string>', 'exec')
            # No syntax errors, remove any existing error highlighting
            self.strategy_text.tag_remove('error', 1.0, tk.END)
            # Update status label
            if hasattr(self, 'syntax_status_label'):
                self.syntax_status_label.config(text="Syntax: OK", foreground="green")

        except SyntaxError as se:
            # Highlight the line with the syntax error
            if se.lineno:
                error_line_start = f"{se.lineno}.0"
                error_line_end = f"{se.lineno}.end"
                self.strategy_text.tag_add('error', error_line_start, error_line_end)

            # Update status label with error info
            if hasattr(self, 'syntax_status_label'):
                error_msg = f"Syntax Error: Line {se.lineno}" if se.lineno else "Syntax Error"
                self.syntax_status_label.config(text=error_msg, foreground="red")

        except Exception:
            # Other compilation errors - don't highlight specific lines
            if hasattr(self, 'syntax_status_label'):
                self.syntax_status_label.config(text="Syntax: Unknown Error", foreground="orange")

    def create_parameters_tab(self) -> None:
        """Create parameters configuration tab"""
        self.params_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.params_frame, text="Parameters")
        
        # Test/Train periods
        self.create_period_controls()
        
        # Strategy parameters
        params_label_frame = ttk.LabelFrame(self.params_frame, text="Strategy Parameters")
        params_label_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Scrollable parameters frame
        canvas = tk.Canvas(params_label_frame)
        scrollbar = ttk.Scrollbar(params_label_frame, orient="vertical", command=canvas.yview)
        self.params_scrollable_frame = ttk.Frame(canvas)
        
        self.params_scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=self.params_scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        self.theme_manager.apply_dark_theme_to_widget(canvas)
        
        # Parameter widgets storage
        self.param_widgets = {}
        
    def create_period_controls(self) -> None:
        """Create train/test period controls"""
        period_frame = ttk.LabelFrame(self.params_frame, text="Backtest & Train/Test Periods")
        period_frame.pack(fill=tk.X, padx=5, pady=5)

        # Backtest period controls
        backtest_controls = ttk.Frame(period_frame)
        backtest_controls.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(backtest_controls, text="Backtest Period:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))

        # Start date with dropdown controls
        ttk.Label(backtest_controls, text="From:").grid(row=0, column=1, sticky=tk.W, padx=(10, 5))

        # Start date frame
        start_date_frame = ttk.Frame(backtest_controls)
        start_date_frame.grid(row=0, column=2, padx=(0, 10))

        # Start date dropdowns
        self.start_year_var = tk.StringVar()
        self.start_month_var = tk.StringVar()
        self.start_day_var = tk.StringVar()

        # Year dropdown
        years = [str(year) for year in range(2000, 2030)]
        self.start_year_combo = ttk.Combobox(start_date_frame, textvariable=self.start_year_var,
                                           values=years, width=6, state="readonly")
        self.start_year_combo.pack(side=tk.LEFT, padx=(0, 2))
        self.start_year_combo.bind('<<ComboboxSelected>>', self.on_start_date_changed)

        # Month dropdown
        months = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12']
        self.start_month_combo = ttk.Combobox(start_date_frame, textvariable=self.start_month_var,
                                            values=months, width=4, state="readonly")
        self.start_month_combo.pack(side=tk.LEFT, padx=(0, 2))
        self.start_month_combo.bind('<<ComboboxSelected>>', self.on_start_date_changed)

        # Day dropdown
        days = [f"{day:02d}" for day in range(1, 32)]
        self.start_day_combo = ttk.Combobox(start_date_frame, textvariable=self.start_day_var,
                                          values=days, width=4, state="readonly")
        self.start_day_combo.pack(side=tk.LEFT)
        self.start_day_combo.bind('<<ComboboxSelected>>', self.on_start_date_changed)

        # End date with dropdown controls
        ttk.Label(backtest_controls, text="To:").grid(row=0, column=3, sticky=tk.W, padx=(10, 5))

        # End date frame
        end_date_frame = ttk.Frame(backtest_controls)
        end_date_frame.grid(row=0, column=4, padx=(0, 10))

        # End date dropdowns
        self.end_year_var = tk.StringVar()
        self.end_month_var = tk.StringVar()
        self.end_day_var = tk.StringVar()

        # Year dropdown
        self.end_year_combo = ttk.Combobox(end_date_frame, textvariable=self.end_year_var,
                                         values=years, width=6, state="readonly")
        self.end_year_combo.pack(side=tk.LEFT, padx=(0, 2))
        self.end_year_combo.bind('<<ComboboxSelected>>', self.on_end_date_changed)

        # Month dropdown
        self.end_month_combo = ttk.Combobox(end_date_frame, textvariable=self.end_month_var,
                                          values=months, width=4, state="readonly")
        self.end_month_combo.pack(side=tk.LEFT, padx=(0, 2))
        self.end_month_combo.bind('<<ComboboxSelected>>', self.on_end_date_changed)

        # Day dropdown
        self.end_day_combo = ttk.Combobox(end_date_frame, textvariable=self.end_day_var,
                                        values=days, width=4, state="readonly")
        self.end_day_combo.pack(side=tk.LEFT)
        self.end_day_combo.bind('<<ComboboxSelected>>', self.on_end_date_changed)

        # Keep the original string variables for compatibility
        self.start_date_var = tk.StringVar(value="")
        self.end_date_var = tk.StringVar(value="")

        # Use all data checkbox
        self.use_all_data_var = tk.BooleanVar(value=True)
        use_all_cb = ttk.Checkbutton(backtest_controls, text="Use All Data",
                                    variable=self.use_all_data_var,
                                    command=self.on_use_all_data_changed)
        use_all_cb.grid(row=0, column=5, padx=(10, 0))

        # Second row for timeframe selection
        timeframe_row = ttk.Frame(period_frame)
        timeframe_row.pack(fill=tk.X, padx=5, pady=(5, 0))

        ttk.Label(timeframe_row, text="Timeframe:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.timeframe_var = tk.StringVar(value=DEFAULTS['timeframe'])
        self.timeframe_combo = ttk.Combobox(timeframe_row, textvariable=self.timeframe_var,
                                           values=TIMEFRAME_OPTIONS, state="readonly", width=15)
        self.timeframe_combo.grid(row=0, column=1, padx=(0, 15))
        self.timeframe_combo.bind('<<ComboboxSelected>>', self.on_timeframe_changed)

        ttk.Label(timeframe_row, text="(Quick date range selection)",
                 font=('Arial', 8)).grid(row=0, column=2, sticky=tk.W, padx=(5, 0))

        # Separator
        separator = ttk.Separator(period_frame, orient='horizontal')
        separator.pack(fill=tk.X, padx=5, pady=10)

        # Train/Test period controls
        controls = ttk.Frame(period_frame)
        controls.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(controls, text="Train/Test Periods:").grid(row=0, column=0, sticky=tk.W, padx=(0, 15))

        # Period unit
        ttk.Label(controls, text="Unit:").grid(row=0, column=1, sticky=tk.W, padx=(0, 5))
        self.period_unit_var = tk.StringVar(value=DEFAULTS['period_unit'])
        unit_combo = ttk.Combobox(controls, textvariable=self.period_unit_var,
                                 values=["days", "months"], width=8, state="readonly")
        unit_combo.grid(row=0, column=2, padx=(0, 15))

        # Train period
        ttk.Label(controls, text="Train:").grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        self.train_period_var = tk.IntVar(value=DEFAULTS['train_period'])
        train_entry = ttk.Entry(controls, textvariable=self.train_period_var, width=8)
        train_entry.grid(row=0, column=4, padx=(0, 15))

        # Test period
        ttk.Label(controls, text="Test:").grid(row=0, column=5, sticky=tk.W, padx=(0, 5))
        self.test_period_var = tk.IntVar(value=DEFAULTS['test_period'])
        test_entry = ttk.Entry(controls, textvariable=self.test_period_var, width=8)
        test_entry.grid(row=0, column=6, padx=(0, 15))

        # Walk-forward
        self.walk_forward_var = tk.BooleanVar(value=DEFAULTS['walk_forward'])
        walk_cb = ttk.Checkbutton(controls, text="Walk-Forward", variable=self.walk_forward_var)
        walk_cb.grid(row=0, column=7)

        # Optimization metric selection (second row)
        opt_controls = ttk.Frame(period_frame)
        opt_controls.pack(fill=tk.X, padx=5, pady=(5, 5))

        ttk.Label(opt_controls, text="Optimization Metric:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))

        # Create optimization metric dropdown
        metric_names = [name for name, _ in OPTIMIZATION_METRICS]
        self.optimization_metric_var = tk.StringVar(value=metric_names[0])  # Default to first option
        self.optimization_metric_combo = ttk.Combobox(opt_controls,
                                                     textvariable=self.optimization_metric_var,
                                                     values=metric_names,
                                                     state="readonly",
                                                     width=20)
        self.optimization_metric_combo.grid(row=0, column=1, padx=(0, 10))

        ttk.Label(opt_controls, text="(Metric to optimize during parameter optimization)",
                 font=('Arial', 8)).grid(row=0, column=2, sticky=tk.W, padx=(5, 0))

        # Initially disable date entries since "Use All Data" is checked
        self.on_use_all_data_changed()

    def get_selected_optimization_metric(self) -> str:
        """Get the key for the selected optimization metric"""
        selected_name = self.optimization_metric_var.get()
        for name, key in OPTIMIZATION_METRICS:
            if name == selected_name:
                return key
        return OPTIMIZATION_CONFIG['default_metric']  # Fallback
        
    def create_results_tab(self) -> None:
        """Create results display tab"""
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text="Results")
        
        # Backtest controls
        controls_frame = ttk.Frame(self.results_frame)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.theme_manager.create_themed_button(controls_frame, text="Run Backtest",
                                               command=self.run_backtest).pack(side=tk.LEFT, padx=(0, 10))
        self.theme_manager.create_themed_button(controls_frame, text="Optimize Parameters",
                                               command=self.run_optimization).pack(side=tk.LEFT, padx=(0, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(controls_frame, variable=self.progress_var, 
                                           maximum=100, length=200)
        self.progress_bar.pack(side=tk.LEFT, padx=(10, 5))
        
        self.progress_label = ttk.Label(controls_frame, text="Ready")
        self.progress_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # Create horizontal paned window for metrics and plots
        results_paned = ttk.PanedWindow(self.results_frame, orient=tk.HORIZONTAL)
        results_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left side - Metrics display
        metrics_frame = ttk.LabelFrame(results_paned, text="Performance Metrics")
        results_paned.add(metrics_frame, weight=1)

        # Create metrics treeview
        self.create_metrics_display(metrics_frame)

        # Right side - Plots
        plots_frame = ttk.LabelFrame(results_paned, text="Performance Plots")
        results_paned.add(plots_frame, weight=1)

        # Create plots display
        self.create_plots_display(plots_frame)
        
    def create_metrics_display(self, parent) -> None:
        """Create metrics display treeview"""
        columns = ('Metric', 'All Trades', 'Long Trades', 'Short Trades', 'Buy & Hold')
        self.metrics_tree = ttk.Treeview(parent, columns=columns, show='headings', height=25)
        
        # Configure columns
        for col in columns:
            self.metrics_tree.heading(col, text=col)
            width = 180 if col == 'Metric' else 120
            self.metrics_tree.column(col, width=width)
            
        # Scrollbar for metrics
        metrics_scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.metrics_tree.yview)
        self.metrics_tree.configure(yscrollcommand=metrics_scrollbar.set)
        
        self.metrics_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        metrics_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_plots_display(self, parent) -> None:
        """Create performance plots display"""
        # Create notebook for different plot types
        plots_notebook = ttk.Notebook(parent)
        plots_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Drawdown plot tab
        self.drawdown_frame = ttk.Frame(plots_notebook)
        plots_notebook.add(self.drawdown_frame, text="Equity & Drawdown")

        # Controls for drawdown plot
        dd_controls = ttk.Frame(self.drawdown_frame)
        dd_controls.pack(fill=tk.X, padx=5, pady=5)

        # Checkbox to toggle buy-and-hold curves
        self.show_buy_hold_var = tk.BooleanVar(value=True)
        buy_hold_cb = ttk.Checkbutton(dd_controls, text="Show Buy & Hold Comparison",
                                     variable=self.show_buy_hold_var,
                                     command=self.update_performance_plots)
        buy_hold_cb.pack(side=tk.LEFT)

        self.drawdown_canvas = tk.Canvas(self.drawdown_frame, bg='#3c3c3c', highlightthickness=0)
        dd_scrollbar = ttk.Scrollbar(self.drawdown_frame, orient=tk.VERTICAL, command=self.drawdown_canvas.yview)
        self.drawdown_canvas.configure(yscrollcommand=dd_scrollbar.set)

        self.drawdown_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        dd_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # P&L Distribution tab
        self.pnl_frame = ttk.Frame(plots_notebook)
        plots_notebook.add(self.pnl_frame, text="P&L Distribution")

        self.pnl_canvas = tk.Canvas(self.pnl_frame, bg='#3c3c3c', highlightthickness=0)
        pnl_scrollbar = ttk.Scrollbar(self.pnl_frame, orient=tk.VERTICAL, command=self.pnl_canvas.yview)
        self.pnl_canvas.configure(yscrollcommand=pnl_scrollbar.set)

        self.pnl_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        pnl_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Add placeholder text
        self.drawdown_canvas.create_text(200, 150, text="Drawdown plot will appear here\nafter running backtest",
                                        fill='#ffffff', font=('Arial', 12), anchor='center')
        self.pnl_canvas.create_text(200, 150, text="P&L distribution will appear here\nafter running backtest",
                                   fill='#ffffff', font=('Arial', 12), anchor='center')

    def create_heatmap_tab(self) -> None:
        """Create return heatmap tab"""
        self.heatmap_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.heatmap_frame, text="Return Heatmap")

        # Heatmap controls
        heatmap_controls = ttk.Frame(self.heatmap_frame)
        heatmap_controls.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(heatmap_controls, text="Return Heatmap - Monthly Returns by Year").pack(side=tk.LEFT)

        # Heatmap canvas
        self.heatmap_canvas = tk.Canvas(self.heatmap_frame, bg='#3c3c3c', highlightthickness=0)
        heatmap_scrollbar = ttk.Scrollbar(self.heatmap_frame, orient=tk.VERTICAL, command=self.heatmap_canvas.yview)
        self.heatmap_canvas.configure(yscrollcommand=heatmap_scrollbar.set)

        self.heatmap_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        heatmap_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Add placeholder text
        self.heatmap_canvas.create_text(400, 200, text="Return heatmap will appear here\nafter running backtest",
                                       fill='#ffffff', font=('Arial', 12), anchor='center')

    def create_log_tab(self) -> None:
        """Create log display tab"""
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="Log")
        
        # Log controls
        log_controls = ttk.Frame(self.log_frame)
        log_controls.pack(fill=tk.X, padx=5, pady=5)
        
        self.theme_manager.create_themed_button(log_controls, text="Clear Log", command=self.clear_log).pack(side=tk.LEFT)
        
        # Log display
        log_container = ttk.Frame(self.log_frame)
        log_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(log_container, wrap=tk.WORD, height=20,
                               bg='#3c3c3c', fg='#ffffff',
                               insertbackground='#ffffff',
                               selectbackground='#404040',
                               selectforeground='#ffffff')
        log_scrollbar = ttk.Scrollbar(log_container, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_strategy_history_tab(self) -> None:
        """Create strategy results history tab"""
        self.history_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.history_frame, text="Strategy History")

        # Initialize the strategy results viewer
        self.strategy_results_viewer = StrategyResultsViewer(self.history_frame, self.theme_manager)

    def create_bulk_backtest_tab(self) -> None:
        """Create bulk backtest tab"""
        self.bulk_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.bulk_frame, text="Bulk Backtest")

        # Create main container with paned window
        main_paned = ttk.PanedWindow(self.bulk_frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel for selection
        left_frame = ttk.LabelFrame(main_paned, text="Selection")
        main_paned.add(left_frame, weight=1)

        # Right panel for results
        right_frame = ttk.LabelFrame(main_paned, text="Bulk Results")
        main_paned.add(right_frame, weight=2)

        self.create_bulk_selection_panel(left_frame)
        self.create_bulk_results_panel(right_frame)

    def create_status_bar(self) -> None:
        """Create status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5, pady=2)
        
        # Data count label
        self.data_count_label = ttk.Label(self.status_bar, text="No data loaded")
        self.data_count_label.pack(side=tk.RIGHT, padx=5, pady=2)

    # Event Handlers
    def load_data(self) -> None:
        """Load data from CSV file"""
        try:
            file_path = filedialog.askopenfilename(
                title="Select CSV Data File",
                filetypes=SUPPORTED_FILE_TYPES
            )

            if not file_path:
                return

            self.log_message(f"Loading data from: {file_path}")
            self.update_progress(0, "Loading data...")

            # Load data in separate thread to prevent GUI freezing
            def load_thread():
                try:
                    self.data_manager.load_csv_data(
                        file_path,
                        progress_callback=self.update_progress_threadsafe
                    )

                    # Apply current bar size if not default
                    bar_size = self.bar_size_var.get()
                    if bar_size != "Daily":
                        resampled = self.data_manager.resample_data(bar_size)
                        if resampled:
                            self.log_message(f"Data resampled to {bar_size}")

                    # Update GUI in main thread
                    self.root.after(0, lambda: self.on_data_loaded(file_path))

                except Exception as e:
                    self.root.after(0, lambda: self.on_data_load_error(str(e)))

            threading.Thread(target=load_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"Error loading data: {str(e)}")

    def on_data_loaded(self, file_path: str) -> None:
        """Handle successful data loading"""
        self.file_label.config(text=os.path.basename(file_path))

        # Store file path and symbol for logging
        self.data_manager.current_file = file_path
        filename = os.path.basename(file_path)
        self.data_manager.current_symbol = os.path.splitext(filename)[0]  # Remove extension

        self.update_data_info_display()
        self.update_status(f"Loaded {self.data_manager.data_count} bars")
        self.update_progress(100, "Data loaded successfully")
        self.log_message(f"Successfully loaded {self.data_manager.data_count} bars")

        # Update date fields with data range
        if self.data_manager.current_data and not self.use_all_data_var.get():
            data = self.data_manager.current_data
            start_date = data[0].Date.strftime('%Y-%m-%d') if hasattr(data[0].Date, 'strftime') else str(data[0].Date)
            end_date = data[-1].Date.strftime('%Y-%m-%d') if hasattr(data[-1].Date, 'strftime') else str(data[-1].Date)
            self.start_date_var.set(start_date)
            self.end_date_var.set(end_date)

            # Update dropdown selections
            self.set_date_dropdowns_from_string(start_date, is_start=True)
            self.set_date_dropdowns_from_string(end_date, is_start=False)

    def on_data_load_error(self, error_msg: str) -> None:
        """Handle data loading error"""
        self.log_message(f"Error loading data: {error_msg}")
        self.update_progress(0, "Error loading data")
        messagebox.showerror("Data Loading Error", error_msg)

    def on_bar_size_changed(self, event=None) -> None:
        """Handle bar size selection change"""
        if not self.data_manager.has_data:
            return

        bar_size = self.bar_size_var.get()
        self.log_message(f"Changing bar size to: {bar_size}")

        try:
            resampled_data = self.data_manager.resample_data(bar_size)
            if resampled_data:
                self.update_data_info_display()
                self.log_message(f"Data resampled to {bar_size}: {len(resampled_data)} bars")
            else:
                self.log_message("Failed to resample data")

        except Exception as e:
            self.log_message(f"Error resampling data: {str(e)}")

    def on_start_date_changed(self, event=None) -> None:
        """Handle start date dropdown changes"""
        self.update_start_date_string()

    def on_end_date_changed(self, event=None) -> None:
        """Handle end date dropdown changes"""
        self.update_end_date_string()

    def update_start_date_string(self) -> None:
        """Update start_date_var from dropdown selections"""
        year = self.start_year_var.get()
        month = self.start_month_var.get()
        day = self.start_day_var.get()

        if year and month and day:
            date_str = f"{year}-{month}-{day}"
            self.start_date_var.set(date_str)

    def update_end_date_string(self) -> None:
        """Update end_date_var from dropdown selections"""
        year = self.end_year_var.get()
        month = self.end_month_var.get()
        day = self.end_day_var.get()

        if year and month and day:
            date_str = f"{year}-{month}-{day}"
            self.end_date_var.set(date_str)

    def set_date_dropdowns_from_string(self, date_str: str, is_start: bool = True) -> None:
        """Set dropdown values from date string (YYYY-MM-DD format)"""
        try:
            if date_str and len(date_str) >= 10:
                year, month, day = date_str[:10].split('-')

                if is_start:
                    self.start_year_var.set(year)
                    self.start_month_var.set(month)
                    self.start_day_var.set(day)
                else:
                    self.end_year_var.set(year)
                    self.end_month_var.set(month)
                    self.end_day_var.set(day)
        except ValueError:
            pass  # Invalid date format, ignore

    def on_timeframe_changed(self, event=None) -> None:
        """Handle timeframe selection change"""
        timeframe = self.timeframe_var.get()
        self.log_message(f"Timeframe changed to: {timeframe}")

        if timeframe == "Custom":
            # Enable custom date range
            self.use_all_data_var.set(False)
            self.on_use_all_data_changed()
        elif timeframe == "All Data":
            # Use all available data
            self.use_all_data_var.set(True)
            self.on_use_all_data_changed()
        else:
            # Apply predefined timeframe
            self.apply_predefined_timeframe_to_dates(timeframe)

    def apply_predefined_timeframe_to_dates(self, timeframe: str) -> None:
        """Apply predefined timeframe to date fields"""
        if not self.data_manager.has_data or not self.data_manager.current_data:
            return

        try:
            # Get data range
            data = self.data_manager.current_data
            end_date = data[-1].Date

            # Calculate start date based on timeframe
            if timeframe == "Last 30 Days":
                start_date = end_date - timedelta(days=30)
            elif timeframe == "Last 90 Days":
                start_date = end_date - timedelta(days=90)
            elif timeframe == "Last 3 Months":
                start_date = end_date - timedelta(days=90)  # Approximately 3 months
            elif timeframe == "Last 6 Months":
                start_date = end_date - timedelta(days=180)
            elif timeframe == "Last Year":
                start_date = end_date - timedelta(days=365)
            elif timeframe == "Last 2 Years":
                start_date = end_date - timedelta(days=730)
            elif timeframe == "Last 3 Years":
                start_date = end_date - timedelta(days=1095)  # 3 * 365
            elif timeframe == "Last 5 Years":
                start_date = end_date - timedelta(days=1825)  # 5 * 365
            elif timeframe == "Last 10 Years":
                start_date = end_date - timedelta(days=3650)  # 10 * 365
            else:
                return

            # Enable custom date range and set dates
            self.use_all_data_var.set(False)
            self.on_use_all_data_changed()

            # Format and set dates
            start_str = start_date.strftime('%Y-%m-%d') if hasattr(start_date, 'strftime') else str(start_date)[:10]
            end_str = end_date.strftime('%Y-%m-%d') if hasattr(end_date, 'strftime') else str(end_date)[:10]

            self.start_date_var.set(start_str)
            self.end_date_var.set(end_str)

            # Update dropdown selections
            self.set_date_dropdowns_from_string(start_str, is_start=True)
            self.set_date_dropdowns_from_string(end_str, is_start=False)

            self.log_message(f"Applied timeframe '{timeframe}': {start_str} to {end_str}")

        except Exception as e:
            self.log_message(f"Error applying timeframe: {str(e)}")

    def on_strategy_selected(self, event=None) -> None:
        """Handle strategy selection"""
        strategy_name = self.strategy_var.get()
        if not strategy_name:
            return

        try:
            self.log_message(f"Loading strategy: {strategy_name}")
            strategy_info = self.strategy_manager.load_strategy(strategy_name)

            # Load strategy code
            code = self.strategy_manager.get_strategy_code(strategy_name)
            self.strategy_text.delete(1.0, tk.END)
            self.strategy_text.insert(1.0, code)

            # Update line numbers and apply syntax highlighting after loading code
            self._update_line_numbers()
            self._apply_syntax_highlighting()

            # Load parameters
            self.load_strategy_parameters(strategy_info)

            # Load train/test periods
            self.load_strategy_periods(strategy_info)

            self.log_message(f"Strategy '{strategy_name}' loaded successfully")

        except Exception as e:
            self.log_message(f"Error loading strategy: {str(e)}")
            messagebox.showerror("Strategy Error", f"Failed to load strategy: {str(e)}")

    def refresh_strategies(self) -> None:
        """Refresh available strategies list"""
        try:
            # Check if GUI components are initialized
            if not hasattr(self, 'strategy_combo'):
                self.log_message("GUI not fully initialized, skipping strategy refresh")
                return

            strategies = self.strategy_manager.get_available_strategies()
            self.strategy_combo['values'] = strategies

            if strategies and not self.strategy_var.get():
                self.strategy_var.set(strategies[0])
                self.on_strategy_selected()

            self.log_message(f"Found {len(strategies)} strategies")

        except Exception as e:
            self.log_message(f"Error refreshing strategies: {str(e)}")

    def load_strategy_parameters(self, strategy_info: Dict[str, Any]) -> None:
        """Load strategy parameters into GUI"""
        # Check if GUI components are initialized
        if not hasattr(self, 'params_scrollable_frame'):
            self.log_message("GUI not fully initialized, skipping parameter loading")
            return

        # Clear existing parameters
        for widget in self.params_scrollable_frame.winfo_children():
            widget.destroy()
        self.param_widgets.clear()

        if not strategy_info.get('parameters'):
            return

        param_ranges = strategy_info['parameters'].get('ranges', {})
        current_params = strategy_info['parameters'].get('current', {})

        row = 0
        for param_name, (min_val, max_val) in param_ranges.items():
            # Parameter label
            ttk.Label(self.params_scrollable_frame, text=param_name).grid(
                row=row, column=0, sticky='w', padx=5, pady=2)

            # Parameter entry
            var = tk.DoubleVar(value=current_params.get(param_name, min_val))
            entry = ttk.Entry(self.params_scrollable_frame, textvariable=var, width=10)
            entry.grid(row=row, column=1, padx=5, pady=2)

            # Range label
            ttk.Label(self.params_scrollable_frame,
                     text=f"({min_val} - {max_val})").grid(
                row=row, column=2, sticky='w', padx=5, pady=2)

            self.param_widgets[param_name] = var
            row += 1

    def load_strategy_periods(self, strategy_info: Dict[str, Any]) -> None:
        """Load train/test periods from strategy"""
        periods = strategy_info.get('train_test_periods', {})

        if periods:
            unit = periods.get('unit', 'months')
            train = periods.get('train', 3)
            test = periods.get('test', 1)

            self.period_unit_var.set(unit)
            self.train_period_var.set(train)
            self.test_period_var.set(test)

            self.log_message(f"Loaded periods: {train} {unit} train, {test} {unit} test")

    def on_use_all_data_changed(self) -> None:
        """Handle use all data checkbox change"""
        use_all = self.use_all_data_var.get()

        # Enable/disable date dropdown fields
        state = 'disabled' if use_all else 'readonly'

        # Start date dropdowns
        self.start_year_combo.config(state=state)
        self.start_month_combo.config(state=state)
        self.start_day_combo.config(state=state)

        # End date dropdowns
        self.end_year_combo.config(state=state)
        self.end_month_combo.config(state=state)
        self.end_day_combo.config(state=state)

        if use_all:
            # Clear date fields when using all data
            self.start_date_var.set("")
            self.end_date_var.set("")
            self.start_year_var.set("")
            self.start_month_var.set("")
            self.start_day_var.set("")
            self.end_year_var.set("")
            self.end_month_var.set("")
            self.end_day_var.set("")
        else:
            # Set default dates if data is available
            if self.data_manager.has_data and self.data_manager.current_data:
                data = self.data_manager.current_data
                start_date = data[0].Date.strftime('%Y-%m-%d') if hasattr(data[0].Date, 'strftime') else str(data[0].Date)
                end_date = data[-1].Date.strftime('%Y-%m-%d') if hasattr(data[-1].Date, 'strftime') else str(data[-1].Date)
                self.start_date_var.set(start_date)
                self.end_date_var.set(end_date)

                # Update dropdown selections
                self.set_date_dropdowns_from_string(start_date, is_start=True)
                self.set_date_dropdowns_from_string(end_date, is_start=False)



    def draw_price_chart(self) -> None:
        """Draw a simple price chart"""
        try:
            data = self.data_manager.current_data
            if not data or len(data) < 2:
                return

            # Get canvas dimensions
            canvas_width = self.chart_canvas.winfo_width()
            canvas_height = self.chart_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width, canvas_height = 800, 400

            # Calculate price range
            prices = [bar.Close for bar in data]
            min_price = min(prices)
            max_price = max(prices)
            price_range = max_price - min_price

            if price_range == 0:
                price_range = 1

            # Draw price line
            points = []
            for i, bar in enumerate(data):
                x = (i / len(data)) * (canvas_width - 40) + 20
                y = canvas_height - 40 - ((bar.Close - min_price) / price_range) * (canvas_height - 80)
                points.extend([x, y])

            if len(points) >= 4:
                self.chart_canvas.create_line(points, fill='#00ff00', width=2)

            # Add labels
            self.chart_canvas.create_text(canvas_width//2, 20, text=f"Price Chart - {len(data)} bars",
                                         fill='#ffffff', font=('Arial', 12))
            self.chart_canvas.create_text(20, canvas_height//2, text=f"${min_price:.2f}",
                                         fill='#ffffff', font=('Arial', 10))
            self.chart_canvas.create_text(20, 40, text=f"${max_price:.2f}",
                                         fill='#ffffff', font=('Arial', 10))

        except Exception as e:
            self.log_message(f"Error drawing price chart: {str(e)}")

    def draw_volume_chart(self) -> None:
        """Draw a simple volume chart"""
        try:
            data = self.data_manager.current_data
            if not data:
                return

            canvas_width = self.chart_canvas.winfo_width()
            canvas_height = self.chart_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width, canvas_height = 800, 400

            volumes = [bar.Volume for bar in data]
            max_volume = max(volumes) if volumes else 1

            # Draw volume bars
            bar_width = max(1, (canvas_width - 40) / len(data))
            for i, bar in enumerate(data):
                x = i * bar_width + 20
                height = (bar.Volume / max_volume) * (canvas_height - 80)
                y = canvas_height - 40 - height

                self.chart_canvas.create_rectangle(x, y, x + bar_width - 1, canvas_height - 40,
                                                  fill='#0080ff', outline='#0080ff')

            # Add labels
            self.chart_canvas.create_text(canvas_width//2, 20, text=f"Volume Chart - {len(data)} bars",
                                         fill='#ffffff', font=('Arial', 12))

        except Exception as e:
            self.log_message(f"Error drawing volume chart: {str(e)}")

    def draw_equity_chart(self) -> None:
        """Draw equity curve with drawdown at top if backtest results available"""
        try:
            if not self.trading_system or not hasattr(self.trading_system, 'best_matched_trades'):
                self.chart_canvas.create_text(400, 200, text="Run backtest first to see equity curve",
                                             fill='#ffffff', font=('Arial', 12), anchor='center')
                return

            trades = self.trading_system.best_matched_trades
            if not trades:
                return

            canvas_width = self.chart_canvas.winfo_width()
            canvas_height = self.chart_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width, canvas_height = 800, 400

            # Calculate equity curve and drawdown
            equity = 0
            peak = 0
            equity_points = [0]
            drawdown_points = [0]

            for trade in trades:
                equity += trade.profit
                equity_points.append(equity)

                if equity > peak:
                    peak = equity

                drawdown = peak - equity
                drawdown_points.append(drawdown)

            min_equity = min(equity_points)
            max_equity = max(equity_points)
            equity_range = max_equity - min_equity
            max_drawdown = max(drawdown_points)

            if equity_range == 0:
                equity_range = 1
            if max_drawdown == 0:
                max_drawdown = 1

            # Split canvas: top 25% for drawdown, bottom 75% for equity
            drawdown_height = canvas_height * 0.25
            equity_height = canvas_height * 0.75
            drawdown_top = 40
            equity_top = drawdown_height + 60

            # Draw drawdown curve at top
            dd_points = []
            for i, dd_val in enumerate(drawdown_points):
                x = (i / len(drawdown_points)) * (canvas_width - 40) + 20
                y = drawdown_top + (dd_val / max_drawdown) * (drawdown_height - 20)
                dd_points.extend([x, y])

            if len(dd_points) >= 4:
                self.chart_canvas.create_line(dd_points, fill='#ff0000', width=2)

            # Draw zero line for drawdown
            zero_y = drawdown_top
            self.chart_canvas.create_line(20, zero_y, canvas_width - 20, zero_y,
                                         fill='#808080', width=1, dash=(5, 5))

            # Draw equity curve at bottom
            eq_points = []
            for i, equity_val in enumerate(equity_points):
                x = (i / len(equity_points)) * (canvas_width - 40) + 20
                y = equity_top + equity_height - 20 - ((equity_val - min_equity) / equity_range) * (equity_height - 40)
                eq_points.extend([x, y])

            if len(eq_points) >= 4:
                self.chart_canvas.create_line(eq_points, fill='#00ff00', width=2)

            # Add labels
            self.chart_canvas.create_text(canvas_width//2, 20,
                                         text=f"Drawdown (Red) & Equity Curve (Green) - {len(trades)} trades",
                                         fill='#ffffff', font=('Arial', 12))

            # Drawdown labels
            self.chart_canvas.create_text(20, drawdown_top + 10, text="0",
                                         fill='#ffffff', font=('Arial', 9))
            self.chart_canvas.create_text(20, drawdown_top + drawdown_height - 10,
                                         text=f"-${max_drawdown:.0f}",
                                         fill='#ffffff', font=('Arial', 9))

            # Equity labels
            self.chart_canvas.create_text(20, equity_top + 20, text=f"${max_equity:.0f}",
                                         fill='#ffffff', font=('Arial', 9))
            self.chart_canvas.create_text(20, equity_top + equity_height - 20, text=f"${min_equity:.0f}",
                                         fill='#ffffff', font=('Arial', 9))

        except Exception as e:
            self.log_message(f"Error drawing equity chart: {str(e)}")

    def draw_return_heatmap(self, trades) -> None:
        """Draw return heatmap with monthly returns by year"""
        try:
            # Clear existing heatmap
            self.heatmap_canvas.delete("all")

            if not trades or not self.data_manager.has_data:
                self.heatmap_canvas.create_text(400, 200, text="No data available for heatmap",
                                               fill='#ffffff', font=('Arial', 12), anchor='center')
                return

            # Get backtest data for date range
            backtest_data = self.get_backtest_data()
            if not backtest_data or len(backtest_data) < 2:
                return

            # Calculate monthly returns
            monthly_returns = self.calculate_monthly_returns(trades, backtest_data)

            if not monthly_returns:
                self.heatmap_canvas.create_text(400, 200, text="Insufficient data for heatmap",
                                               fill='#ffffff', font=('Arial', 12), anchor='center')
                return

            # Draw the heatmap
            self.draw_heatmap_grid(monthly_returns)

        except Exception as e:
            self.log_message(f"Error drawing return heatmap: {str(e)}")

    def calculate_monthly_returns(self, trades, backtest_data):
        """Calculate monthly returns from trades covering entire backtest period"""
        try:
            # Get the full backtest period
            start_date = backtest_data[0].Date
            end_date = backtest_data[-1].Date

            # Extract start and end year/month
            if hasattr(start_date, 'year'):
                start_year, start_month = start_date.year, start_date.month
                end_year, end_month = end_date.year, end_date.month
            else:
                # Parse string dates
                start_str = str(start_date)[:10]
                end_str = str(end_date)[:10]
                start_year, start_month = int(start_str[:4]), int(start_str[5:7])
                end_year, end_month = int(end_str[:4]), int(end_str[5:7])

            # Initialize all months in the period with 0
            monthly_returns = {}
            current_year, current_month = start_year, start_month

            while (current_year, current_month) <= (end_year, end_month):
                monthly_returns[(current_year, current_month)] = 0

                # Move to next month
                current_month += 1
                if current_month > 12:
                    current_month = 1
                    current_year += 1

            # Create a more detailed mapping of trades to dates
            # First, create equity progression for each bar
            equity_by_date = {}
            equity = 0

            # Map each trade to its estimated date and build cumulative equity
            trade_dates = []
            for i, trade in enumerate(trades):
                equity += trade.profit

                # Estimate trade date more accurately
                if hasattr(trade, 'exit_date') and trade.exit_date:
                    trade_date = trade.exit_date
                elif hasattr(trade, 'entry_date') and trade.entry_date:
                    trade_date = trade.entry_date
                else:
                    # Better estimation: distribute trades evenly across backtest period
                    progress = (i + 1) / len(trades)
                    total_days = len(backtest_data)
                    bar_index = min(int(progress * total_days), total_days - 1)
                    trade_date = backtest_data[bar_index].Date

                trade_dates.append((trade_date, equity))

                # Store equity at this date
                if hasattr(trade_date, 'year'):
                    date_key = (trade_date.year, trade_date.month, trade_date.day)
                else:
                    date_str = str(trade_date)[:10]
                    year, month, day = int(date_str[:4]), int(date_str[5:7]), int(date_str[8:10])
                    date_key = (year, month, day)

                equity_by_date[date_key] = equity

            # Now calculate monthly equity snapshots (end of month values)
            monthly_equity = {}

            # For each month in the period, find the equity at month end
            sorted_months = sorted(monthly_returns.keys())
            current_equity = 0

            for month_key in sorted_months:
                year, month = month_key

                # Find the latest equity value in this month
                month_end_equity = current_equity  # Default to previous month's end

                for date_key, equity_val in equity_by_date.items():
                    date_year, date_month, _ = date_key
                    if date_year == year and date_month == month:
                        month_end_equity = equity_val  # Update to latest in month

                monthly_equity[month_key] = month_end_equity
                current_equity = month_end_equity

            # Calculate monthly returns (change from previous month)
            prev_equity = 0
            for month_key in sorted_months:
                current_equity = monthly_equity.get(month_key, prev_equity)
                monthly_return = current_equity - prev_equity
                monthly_returns[month_key] = monthly_return
                prev_equity = current_equity

            self.log_message(f"Calculated returns for {len(monthly_returns)} months from {start_year}-{start_month:02d} to {end_year}-{end_month:02d}")
            return monthly_returns

        except Exception as e:
            self.log_message(f"Error calculating monthly returns: {str(e)}")
            return {}

    def draw_heatmap_grid(self, monthly_returns):
        """Draw the heatmap grid with monthly returns"""
        try:
            # Get canvas dimensions
            canvas_width = self.heatmap_canvas.winfo_width()
            canvas_height = self.heatmap_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width, canvas_height = 800, 600

            # Organize data by year and month
            years = sorted(set(year for year, _ in monthly_returns.keys()))
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

            if not years:
                return

            # Calculate grid dimensions
            margin_left = 80
            margin_top = 60
            margin_right = 100
            margin_bottom = 40

            grid_width = canvas_width - margin_left - margin_right
            grid_height = canvas_height - margin_top - margin_bottom

            cell_width = grid_width / 13  # 12 months + 1 year total
            cell_height = grid_height / len(years)

            # Calculate color scale
            all_returns = list(monthly_returns.values())
            if all_returns:
                max_return = max(all_returns)
                min_return = min(all_returns)
                return_range = max_return - min_return if max_return != min_return else 1
            else:
                max_return = min_return = return_range = 0

            # Draw headers
            # Month headers
            for i, month in enumerate(months):
                x = margin_left + i * cell_width + cell_width / 2
                y = margin_top - 20
                self.heatmap_canvas.create_text(x, y, text=month, fill='#ffffff',
                                               font=('Arial', 10), anchor='center')

            # Year total header
            x = margin_left + 12 * cell_width + cell_width / 2
            y = margin_top - 20
            self.heatmap_canvas.create_text(x, y, text="Year", fill='#ffffff',
                                           font=('Arial', 10, 'bold'), anchor='center')

            # Draw grid and data
            for row, year in enumerate(years):
                # Year label
                y = margin_top + row * cell_height + cell_height / 2
                self.heatmap_canvas.create_text(margin_left - 20, y, text=str(year),
                                               fill='#ffffff', font=('Arial', 10), anchor='center')

                # Calculate year total
                year_total = sum(monthly_returns.get((year, month), 0) for month in range(1, 13))

                # Draw monthly cells
                for col, month_num in enumerate(range(1, 13)):
                    month_return = monthly_returns.get((year, month_num), 0)

                    # Calculate cell position
                    x1 = margin_left + col * cell_width
                    y1 = margin_top + row * cell_height
                    x2 = x1 + cell_width
                    y2 = y1 + cell_height

                    # Calculate color based on return
                    if return_range > 0:
                        intensity = (month_return - min_return) / return_range
                    else:
                        intensity = 0.5

                    # Color scheme: red for negative, green for positive
                    if month_return > 0:
                        # Green for positive returns
                        green_intensity = int(255 * (1 - intensity * 0.7))
                        color = f"#{green_intensity:02x}ff{green_intensity:02x}"
                    elif month_return < 0:
                        # Red for negative returns
                        red_intensity = int(255 * (1 - abs(intensity) * 0.7))
                        color = f"#ff{red_intensity:02x}{red_intensity:02x}"
                    else:
                        color = "#808080"

                    # Draw cell
                    self.heatmap_canvas.create_rectangle(x1, y1, x2, y2, fill=color, outline='#333333')

                    # Add text if return is significant
                    if abs(month_return) > 0.01:  # Only show if > $0.01
                        # Always use black text for better readability
                        self.heatmap_canvas.create_text(x1 + cell_width/2, y1 + cell_height/2,
                                                       text=f"${month_return:.0f}",
                                                       fill='#000000', font=('Arial', 8), anchor='center')

                # Draw year total cell
                x1 = margin_left + 12 * cell_width
                y1 = margin_top + row * cell_height
                x2 = x1 + cell_width
                y2 = y1 + cell_height

                # Year total color (stronger intensity)
                if return_range > 0:
                    year_intensity = (year_total - min_return * 12) / (return_range * 12) if return_range > 0 else 0.5
                else:
                    year_intensity = 0.5

                if year_total > 0:
                    # Green for positive year
                    year_green_intensity = int(255 * (1 - year_intensity * 0.8))
                    year_color = f"#{year_green_intensity:02x}ff{year_green_intensity:02x}"
                elif year_total < 0:
                    # Red for negative year
                    year_red_intensity = int(255 * (1 - abs(year_intensity) * 0.8))
                    year_color = f"#ff{year_red_intensity:02x}{year_red_intensity:02x}"
                else:
                    year_color = "#808080"

                self.heatmap_canvas.create_rectangle(x1, y1, x2, y2, fill=year_color, outline='#333333', width=2)

                # Year total text - always black for better readability
                self.heatmap_canvas.create_text(x1 + cell_width/2, y1 + cell_height/2,
                                               text=f"${year_total:.0f}",
                                               fill='#000000', font=('Arial', 9, 'bold'), anchor='center')

            # Add title and legend
            self.heatmap_canvas.create_text(canvas_width/2, 20,
                                           text="Monthly Returns Heatmap",
                                           fill='#ffffff', font=('Arial', 14, 'bold'), anchor='center')

            # Color legend
            legend_y = canvas_height - 20
            self.heatmap_canvas.create_text(canvas_width/2, legend_y,
                                           text="Green: Positive Returns | Red: Negative Returns | Gray: No Trades",
                                           fill='#ffffff', font=('Arial', 10), anchor='center')

        except Exception as e:
            self.log_message(f"Error drawing heatmap grid: {str(e)}")

    def on_chart_type_changed(self, event=None) -> None:
        """Handle chart type selection change"""
        self.update_chart()

    def run_backtest(self) -> None:
        """Run backtest with current strategy and data"""
        if not self.data_manager.has_data:
            messagebox.showwarning("No Data", ERROR_MESSAGES['no_data'])
            return

        if not self.strategy_manager.has_strategy:
            messagebox.showwarning("No Strategy", ERROR_MESSAGES['no_strategy'])
            return

        try:
            self.log_message("Starting backtest...")
            self.update_progress(0, "Running backtest...")

            # Update strategy parameters
            self.update_strategy_parameters()

            # Run backtest in separate thread
            def backtest_thread():
                try:
                    # Get filtered data based on timeframe
                    backtest_data = self.get_backtest_data()

                    # Create trading system instance
                    strategy_module = self.strategy_manager.strategy_module
                    self.trading_system = strategy_module.MyTradingSystem(backtest_data)

                    # Run backtest
                    self.trading_system.run_backtest()

                    # Ensure trades are calculated and available
                    if hasattr(self.trading_system, 'calculate_matched_trades'):
                        self.trading_system.calculate_matched_trades()

                    # Copy matched trades to best_matched_trades if not already done
                    if not hasattr(self.trading_system, 'best_matched_trades') or not self.trading_system.best_matched_trades:
                        if hasattr(self.trading_system, 'matched_trades'):
                            self.trading_system.best_matched_trades = self.trading_system.matched_trades



                    # Calculate metrics
                    metrics = self.metrics_calculator.calculate_comprehensive_metrics(self.trading_system)
                    buy_hold_metrics = self.metrics_calculator.calculate_buy_hold_metrics(backtest_data)

                    # Update GUI in main thread
                    self.root.after(0, lambda: self.on_backtest_complete(metrics, buy_hold_metrics))

                except Exception as e:
                    self.root.after(0, lambda: self.on_backtest_error(str(e)))

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"Error starting backtest: {str(e)}")

    def update_strategy_parameters(self) -> None:
        """Update strategy parameters from GUI"""
        if not self.param_widgets:
            return

        try:
            parameter_values = {}
            for param_name, var in self.param_widgets.items():
                parameter_values[param_name] = var.get()

            self.strategy_manager.update_strategy_parameters(parameter_values)
            self.log_message(f"Updated parameters: {parameter_values}")

        except Exception as e:
            self.log_message(f"Error updating parameters: {str(e)}")

    def get_backtest_data(self) -> List:
        """Get data for backtesting based on current period settings"""
        if not self.data_manager.current_data:
            return []

        # Check if using all data or custom period
        if self.use_all_data_var.get():
            return self.data_manager.current_data
        else:
            # Use custom date range
            start_date_str = self.start_date_var.get().strip()
            end_date_str = self.end_date_var.get().strip()

            if not start_date_str or not end_date_str:
                self.log_message("Warning: Date range not specified, using all data")
                return self.data_manager.current_data

            try:
                from datetime import datetime
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

                # Filter data by date range
                filtered_data = []
                for bar in self.data_manager.current_data:
                    bar_date = bar.Date.date() if hasattr(bar.Date, 'date') else bar.Date
                    if start_date <= bar_date <= end_date:
                        filtered_data.append(bar)

                if filtered_data:
                    self.log_message(f"Using date range {start_date_str} to {end_date_str}: {len(filtered_data)} bars")
                    return filtered_data
                else:
                    self.log_message("Warning: No data found in specified date range, using all data")
                    return self.data_manager.current_data

            except ValueError as e:
                self.log_message(f"Error parsing dates: {str(e)}, using all data")
                return self.data_manager.current_data

    def apply_predefined_timeframe(self, timeframe: str) -> List:
        """Apply predefined timeframe to data"""
        if not self.data_manager.current_data:
            return []

        end_date = self.data_manager.current_data[-1].Date

        if timeframe == "Last 30 Days":
            start_date = end_date - timedelta(days=30)
        elif timeframe == "Last 90 Days":
            start_date = end_date - timedelta(days=90)
        elif timeframe == "Last 3 Months":
            start_date = end_date - timedelta(days=90)  # Approximately 3 months
        elif timeframe == "Last 6 Months":
            start_date = end_date - timedelta(days=180)
        elif timeframe == "Last Year":
            start_date = end_date - timedelta(days=365)
        elif timeframe == "Last 2 Years":
            start_date = end_date - timedelta(days=730)
        elif timeframe == "Last 3 Years":
            start_date = end_date - timedelta(days=1095)  # 3 * 365
        elif timeframe == "Last 5 Years":
            start_date = end_date - timedelta(days=1825)  # 5 * 365
        elif timeframe == "Last 10 Years":
            start_date = end_date - timedelta(days=3650)  # 10 * 365
        else:
            return self.data_manager.current_data

        return self.data_manager.filter_by_date_range(start_date, end_date)

    def on_backtest_complete(self, metrics: Dict, buy_hold_metrics: Dict) -> None:
        """Handle successful backtest completion"""
        self.backtest_results = metrics
        self.update_metrics_display(metrics, buy_hold_metrics)
        self.update_performance_plots()  # Update drawdown and P&L plots
        self.update_progress(100, "Backtest completed")
        self.log_message("Backtest completed successfully")

        # Log strategy results
        self.log_strategy_results(metrics, buy_hold_metrics)

    def on_backtest_error(self, error_msg: str) -> None:
        """Handle backtest error"""
        self.log_message(f"Backtest error: {error_msg}")
        self.update_progress(0, "Backtest failed")
        messagebox.showerror("Backtest Error", error_msg)

    def run_optimization(self) -> None:
        """Run parameter optimization"""
        if not self.data_manager.has_data:
            messagebox.showwarning("No Data", ERROR_MESSAGES['no_data'])
            return

        if not self.strategy_manager.has_strategy:
            messagebox.showwarning("No Strategy", ERROR_MESSAGES['no_strategy'])
            return

        try:
            self.log_message("Starting optimization...")
            self.update_progress(0, "Starting optimization...")

            # Run optimization in separate thread
            def optimization_thread():
                try:
                    backtest_data = self.get_backtest_data()

                    # Get walk-forward settings
                    walk_forward = self.walk_forward_var.get()
                    train_period = self.train_period_var.get()
                    test_period = self.test_period_var.get()
                    period_unit = self.period_unit_var.get()

                    # Get selected optimization metric
                    optimization_metric = self.get_selected_optimization_metric()
                    self.log_message(f"Starting optimization with metric: {optimization_metric}")

                    results = self.strategy_manager.run_optimization(
                        backtest_data,
                        progress_callback=self.update_progress_threadsafe,
                        walk_forward=walk_forward,
                        train_period=train_period,
                        test_period=test_period,
                        period_unit=period_unit,
                        optimization_metric=optimization_metric
                    )

                    self.root.after(0, lambda: self.on_optimization_complete(results))

                except Exception as e:
                    self.root.after(0, lambda: self.on_optimization_error(str(e)))

            self.optimization_thread = threading.Thread(target=optimization_thread, daemon=True)
            self.optimization_thread.start()

        except Exception as e:
            self.log_message(f"Error starting optimization: {str(e)}")

    def on_optimization_complete(self, results: Dict) -> None:
        """Handle successful optimization completion"""
        best_params = results['best_params']
        best_performance = results['best_performance']

        # Update parameter widgets with optimized values
        for param_name, value in best_params.items():
            if param_name in self.param_widgets:
                self.param_widgets[param_name].set(value)

        self.update_progress(100, "Optimization completed")
        self.log_message(f"Optimization completed. Best performance: {best_performance:.4f}")
        self.log_message(f"Optimized parameters: {best_params}")

        # Automatically run backtest with optimized parameters to update metrics
        self.log_message("Running backtest with optimized parameters...")

        # Small delay to allow GUI to update, then run backtest
        self.root.after(100, self.run_backtest_after_optimization)

    def run_backtest_after_optimization(self) -> None:
        """Run backtest after optimization to update metrics"""
        try:
            # Update strategy parameters first
            self.update_strategy_parameters()

            # Run backtest in separate thread
            def backtest_thread():
                try:
                    # Get filtered data based on timeframe
                    backtest_data = self.get_backtest_data()

                    # Create trading system instance
                    strategy_module = self.strategy_manager.strategy_module
                    self.trading_system = strategy_module.MyTradingSystem(backtest_data)

                    # Run backtest
                    self.trading_system.run_backtest()

                    # Ensure trades are calculated and available
                    if hasattr(self.trading_system, 'calculate_matched_trades'):
                        self.trading_system.calculate_matched_trades()

                    # Copy matched trades to best_matched_trades if not already done
                    if not hasattr(self.trading_system, 'best_matched_trades') or not self.trading_system.best_matched_trades:
                        if hasattr(self.trading_system, 'matched_trades'):
                            self.trading_system.best_matched_trades = self.trading_system.matched_trades



                    # Calculate metrics
                    metrics = self.metrics_calculator.calculate_comprehensive_metrics(self.trading_system)
                    buy_hold_metrics = self.metrics_calculator.calculate_buy_hold_metrics(backtest_data)

                    # Update GUI in main thread
                    self.root.after(0, lambda: self.on_backtest_complete_after_optimization(metrics, buy_hold_metrics))

                except Exception as e:
                    self.root.after(0, lambda: self.on_backtest_error(str(e)))

            threading.Thread(target=backtest_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"Error running backtest after optimization: {str(e)}")

    def on_backtest_complete_after_optimization(self, metrics: Dict, buy_hold_metrics: Dict) -> None:
        """Handle backtest completion after optimization"""
        self.backtest_results = metrics
        self.update_metrics_display(metrics, buy_hold_metrics)
        self.update_performance_plots()  # Update drawdown and P&L plots
        self.log_message("Backtest completed after optimization - metrics updated")

        # Log strategy results
        self.log_strategy_results(metrics, buy_hold_metrics, is_optimized=True)

        # Show completion message
        total_profit = metrics.get('all', {}).get('total_profit', 0)
        total_trades = metrics.get('all', {}).get('total_trades', 0)
        win_rate = metrics.get('all', {}).get('win_rate', 0)

        messagebox.showinfo("Optimization Complete",
                           f"Optimization and backtest completed!\n\n"
                           f"Results with optimized parameters:\n"
                           f"Total Profit: ${total_profit:.2f}\n"
                           f"Total Trades: {total_trades}\n"
                           f"Win Rate: {win_rate:.1f}%\n\n"
                           f"Metrics and plots have been updated.")

    def on_optimization_error(self, error_msg: str) -> None:
        """Handle optimization error"""
        self.log_message(f"Optimization error: {error_msg}")
        self.update_progress(0, "Optimization failed")
        messagebox.showerror("Optimization Error", error_msg)

    def update_metrics_display(self, metrics: Dict, buy_hold_metrics: Dict) -> None:
        """Update metrics display with results"""
        # Clear existing metrics
        for item in self.metrics_tree.get_children():
            self.metrics_tree.delete(item)

        # Define metrics structure
        metrics_sections = [
            # Basic Performance
            ('Net Profit', 'total_profit', 'currency'),
            ('Profit per Bar', 'profit_per_bar', 'currency'),
            ('Total Commission', 'total_commission', 'currency'),
            ('', '', ''),  # Separator

            # Trade Statistics
            ('Number of Trades', 'total_trades', 'number'),
            ('Average Profit', 'avg_profit', 'currency'),
            ('Average Profit %', 'avg_profit_pct', 'percentage'),
            ('Average Bars Held', 'avg_bars_held', 'decimal'),
            ('', '', ''),  # Separator

            # Winning Trades
            ('Winning Trades', 'wins', 'number'),
            ('Win Rate', 'win_rate', 'percentage'),
            ('Gross Profit', 'gross_profit', 'currency'),
            ('Average Win', 'avg_win', 'currency'),
            ('Max Consecutive Winners', 'max_consecutive_wins', 'number'),
            ('', '', ''),  # Separator

            # Losing Trades
            ('Losing Trades', 'losses', 'number'),
            ('Loss Rate', 'loss_rate', 'percentage'),
            ('Gross Loss', 'gross_loss', 'currency'),
            ('Average Loss', 'avg_loss', 'currency'),
            ('Max Consecutive Losses', 'max_consecutive_losses', 'number'),
            ('', '', ''),  # Separator

            # Risk Metrics
            ('Maximum Drawdown', 'max_drawdown', 'currency'),
            ('Maximum Drawdown Date', 'max_drawdown_date', 'text'),
            ('', '', ''),  # Separator

            # Performance Ratios
            ('Profit Factor', 'profit_factor', 'decimal'),
            ('Recovery Factor', 'recovery_factor', 'decimal'),
            ('Payoff Ratio', 'payoff_ratio', 'decimal'),
            ('Sharpe Ratio', 'sharpe_ratio', 'decimal'),
            ('Calmar Ratio', 'calmar_ratio', 'decimal'),
            ('Sortino Ratio', 'sortino_ratio', 'decimal'),
            ('DDR Ratio', 'ddr_ratio', 'decimal'),
        ]

        # Add metrics to tree
        for label, metric_key, format_type in metrics_sections:
            if label == '':  # Separator
                self.metrics_tree.insert('', 'end', values=('', '', '', '', ''))
                continue

            # Get values
            all_val = metrics.get('all', {}).get(metric_key, 'N/A')
            long_val = metrics.get('long', {}).get(metric_key, 'N/A')
            short_val = metrics.get('short', {}).get(metric_key, 'N/A')
            bh_val = buy_hold_metrics.get(metric_key, 'N/A')

            # Format values
            all_str = self.metrics_calculator.format_metric_value(all_val, format_type)
            long_str = self.metrics_calculator.format_metric_value(long_val, format_type)
            short_str = self.metrics_calculator.format_metric_value(short_val, format_type)
            bh_str = self.metrics_calculator.format_metric_value(bh_val, format_type)

            # Insert into tree
            self.metrics_tree.insert('', 'end', values=(label, all_str, long_str, short_str, bh_str))

    def update_performance_plots(self) -> None:
        """Update performance plots with backtest results"""
        try:
            if not self.trading_system or not hasattr(self.trading_system, 'best_matched_trades'):
                return

            trades = self.trading_system.best_matched_trades
            if not trades:
                return

            self.draw_drawdown_plot(trades)
            self.draw_pnl_histogram(trades)
            self.draw_return_heatmap(trades)

        except Exception as e:
            self.log_message(f"Error updating performance plots: {str(e)}")

    def draw_drawdown_plot(self, trades) -> None:
        """Draw drawdown plot with strategy and buy-and-hold comparison"""
        try:
            # Clear existing plot
            self.drawdown_canvas.delete("all")

            if not self.data_manager.has_data or not self.data_manager.current_data:
                return

            # Get the data used for backtesting
            backtest_data = self.get_backtest_data()
            if not backtest_data or len(backtest_data) < 2:
                return

            # Calculate strategy equity and drawdown using cumsum approach
            # Extract trade profits and create cumulative sum
            trade_profits = [trade.profit for trade in trades]

            # Create equity curve using cumulative sum
            import numpy as np
            if trade_profits:
                # Start with 0, then cumulative sum of profits
                equity_curve = [0] + np.cumsum(trade_profits).tolist()
            else:
                equity_curve = [0]

            # Create corresponding trade dates
            trade_dates = [backtest_data[0].Date]  # Start with first date
            if len(trades) > 0:
                # Distribute trade dates evenly across the backtest period
                total_bars = len(backtest_data)
                for i in range(len(trades)):
                    # Map each trade to a proportional position in the data
                    bar_index = min(int((i + 1) / len(trades) * total_bars), total_bars - 1)
                    trade_dates.append(backtest_data[bar_index].Date)

            # Calculate drawdown curve using cumsum-based equity
            drawdown_curve = []
            peak = 0
            for equity_val in equity_curve:
                if equity_val > peak:
                    peak = equity_val
                drawdown = peak - equity_val
                drawdown_curve.append(drawdown)

            # Calculate buy-and-hold equity - simple single position, no multipliers
            if len(trades) > 0 and len(backtest_data) > 1:
                initial_price = backtest_data[0].Close

                # Simple buy-and-hold: buy at start, hold until each trade point
                bh_equity_curve = [0]  # Start with 0

                # Calculate total strategy capital to match buy-and-hold investment
                total_strategy_loss = sum(abs(trade.profit) for trade in trades if trade.profit < 0)
                estimated_capital = max(10000, total_strategy_loss * 2)  # Conservative capital estimate

                for i in range(len(trades)):
                    # Get the price at this trade point
                    bar_index = min(int((i + 1) / len(trades) * len(backtest_data)), len(backtest_data) - 1)
                    current_price = backtest_data[bar_index].Close

                    # Simple buy-and-hold calculation: (current_price - initial_price) / initial_price * capital
                    price_change_pct = (current_price - initial_price) / initial_price if initial_price != 0 else 0
                    bh_equity = price_change_pct * estimated_capital
                    bh_equity_curve.append(bh_equity)
            else:
                # No trades, so no progression
                bh_equity_curve = [0] * len(equity_curve)

            # Calculate buy-and-hold drawdown curve using cumsum-based equity
            bh_drawdown_curve = []
            bh_peak = 0
            for bh_equity_val in bh_equity_curve:
                if bh_equity_val > bh_peak:
                    bh_peak = bh_equity_val
                bh_drawdown = bh_peak - bh_equity_val
                bh_drawdown_curve.append(bh_drawdown)

            if len(equity_curve) < 2:
                return

            # Get canvas dimensions
            canvas_width = self.drawdown_canvas.winfo_width()
            canvas_height = self.drawdown_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width, canvas_height = 400, 300

            # Calculate proper scaling based on what's being displayed
            if self.show_buy_hold_var.get():
                # Include buy-and-hold in scaling
                max_drawdown = max(max(drawdown_curve) if drawdown_curve else 0,
                                  max(bh_drawdown_curve) if bh_drawdown_curve else 0)
                max_equity = max(max(equity_curve), max(bh_equity_curve))
                min_equity = min(min(equity_curve), min(bh_equity_curve))
            else:
                # Scale only for strategy
                max_drawdown = max(drawdown_curve) if drawdown_curve else 0
                max_equity = max(equity_curve) if equity_curve else 0
                min_equity = min(equity_curve) if equity_curve else 0

            equity_range = max_equity - min_equity

            if max_drawdown == 0:
                max_drawdown = 1
            if equity_range == 0:
                equity_range = 1

            # Define plot areas with proper margins
            margin_top = 40
            margin_bottom = 50
            margin_left = 80
            margin_right = 20

            plot_width = canvas_width - margin_left - margin_right
            plot_height = canvas_height - margin_top - margin_bottom

            # Split plot: top 60% for equity, bottom 40% for drawdown
            equity_height = plot_height * 0.6
            drawdown_height = plot_height * 0.4

            equity_top = margin_top
            drawdown_top = margin_top + equity_height + 20

            # Draw strategy equity curve in top section (green)
            strategy_equity_points = []
            for i, equity_val in enumerate(equity_curve):
                x = margin_left + (i / (len(equity_curve) - 1)) * plot_width
                y = equity_top + equity_height - ((equity_val - min_equity) / equity_range) * equity_height
                strategy_equity_points.extend([x, y])

            if len(strategy_equity_points) >= 4:
                self.drawdown_canvas.create_line(strategy_equity_points, fill='#00ff00', width=2)

            # Draw buy-and-hold equity curve in top section (blue) - if enabled
            if self.show_buy_hold_var.get():
                bh_equity_points = []
                for i, equity_val in enumerate(bh_equity_curve):
                    x = margin_left + (i / (len(bh_equity_curve) - 1)) * plot_width
                    y = equity_top + equity_height - ((equity_val - min_equity) / equity_range) * equity_height
                    bh_equity_points.extend([x, y])

                if len(bh_equity_points) >= 4:
                    self.drawdown_canvas.create_line(bh_equity_points, fill='#4444ff', width=2)

            # Draw strategy drawdown curve in bottom section (red)
            strategy_dd_points = []
            for i, dd_val in enumerate(drawdown_curve):
                x = margin_left + (i / (len(drawdown_curve) - 1)) * plot_width
                y = drawdown_top + (dd_val / max_drawdown) * drawdown_height
                strategy_dd_points.extend([x, y])

            if len(strategy_dd_points) >= 4:
                self.drawdown_canvas.create_line(strategy_dd_points, fill='#ff0000', width=2)

            # Draw buy-and-hold drawdown curve in bottom section (orange) - if enabled
            if self.show_buy_hold_var.get():
                bh_dd_points = []
                for i, dd_val in enumerate(bh_drawdown_curve):
                    x = margin_left + (i / (len(bh_drawdown_curve) - 1)) * plot_width
                    y = drawdown_top + (dd_val / max_drawdown) * drawdown_height
                    bh_dd_points.extend([x, y])

                if len(bh_dd_points) >= 4:
                    self.drawdown_canvas.create_line(bh_dd_points, fill='#ff8800', width=2)

            # Draw zero line for drawdown
            zero_y = drawdown_top
            self.drawdown_canvas.create_line(margin_left, zero_y, canvas_width - margin_right, zero_y,
                                           fill='#808080', width=1, dash=(5, 5))

            # Add time-based x-axis labels (years)
            start_year = trade_dates[0].year if hasattr(trade_dates[0], 'year') else 2020
            end_year = trade_dates[-1].year if hasattr(trade_dates[-1], 'year') else 2024

            # Draw year markers
            for year in range(start_year, end_year + 1):
                # Find approximate position for this year
                year_progress = (year - start_year) / max(1, (end_year - start_year))
                x = margin_left + year_progress * plot_width

                # Draw year line
                self.drawdown_canvas.create_line(x, equity_top + equity_height,
                                               x, drawdown_top + drawdown_height,
                                               fill='#666666', width=1, dash=(2, 2))

                # Year label
                self.drawdown_canvas.create_text(x, canvas_height - margin_bottom + 15,
                                               text=str(year),
                                               fill='#ffffff', font=('Arial', 9), anchor='center')

            # Add axis labels
            # Equity axis labels
            self.drawdown_canvas.create_text(margin_left - 10, equity_top,
                                           text=f"${max_equity:.0f}",
                                           fill='#ffffff', font=('Arial', 9), anchor='e')
            self.drawdown_canvas.create_text(margin_left - 10, equity_top + equity_height,
                                           text=f"${min_equity:.0f}",
                                           fill='#ffffff', font=('Arial', 9), anchor='e')

            # Drawdown axis labels
            self.drawdown_canvas.create_text(margin_left - 10, drawdown_top,
                                           text="$0",
                                           fill='#ffffff', font=('Arial', 9), anchor='e')
            self.drawdown_canvas.create_text(margin_left - 10, drawdown_top + drawdown_height,
                                           text=f"-${max_drawdown:.0f}",
                                           fill='#ffffff', font=('Arial', 9), anchor='e')

            # Title and enhanced legend
            if self.show_buy_hold_var.get():
                title_text = f"Strategy vs Buy & Hold - {len(trades)} trades"
                legend_text = "Equity: Green=Strategy, Blue=Buy&Hold | Drawdown: Red=Strategy, Orange=Buy&Hold"

                # Performance comparison
                strategy_profit = equity_curve[-1] if equity_curve else 0
                bh_profit = bh_equity_curve[-1] if bh_equity_curve else 0
                comparison_text = f"Final P&L: Strategy ${strategy_profit:.0f} | Buy&Hold ${bh_profit:.0f}"
            else:
                title_text = f"Strategy Performance - {len(trades)} trades"
                legend_text = "Equity: Green=Strategy | Drawdown: Red=Strategy"

                # Strategy only
                strategy_profit = equity_curve[-1] if equity_curve else 0
                comparison_text = f"Final P&L: Strategy ${strategy_profit:.0f}"

            self.drawdown_canvas.create_text(canvas_width//2, 15,
                                           text=title_text,
                                           fill='#ffffff', font=('Arial', 12), anchor='center')
            self.drawdown_canvas.create_text(canvas_width//2, canvas_height - 30,
                                           text=legend_text,
                                           fill='#ffffff', font=('Arial', 9), anchor='center')
            self.drawdown_canvas.create_text(canvas_width//2, canvas_height - 10,
                                           text=comparison_text,
                                           fill='#ffffff', font=('Arial', 9), anchor='center')

        except Exception as e:
            self.log_message(f"Error drawing drawdown plot: {str(e)}")

    def draw_pnl_histogram(self, trades) -> None:
        """Draw P&L distribution histogram with correct scaling"""
        try:
            # Clear existing plot
            self.pnl_canvas.delete("all")

            if not trades:
                return

            # Get P&L values
            profits = [trade.profit for trade in trades]

            # Create histogram bins
            min_profit = min(profits)
            max_profit = max(profits)
            profit_range = max_profit - min_profit

            if profit_range == 0:
                # All trades have same profit - create single bar
                self.pnl_canvas.create_text(200, 150,
                                          text=f"All trades: ${profits[0]:.2f}",
                                          fill='#ffffff', font=('Arial', 12), anchor='center')
                return

            # Calculate optimal number of bins
            num_bins = min(max(5, int(len(profits) ** 0.5)), 20)  # Between 5 and 20 bins
            bin_width = profit_range / num_bins

            # Count trades in each bin
            bins = []
            for i in range(num_bins):
                bin_start = min_profit + i * bin_width
                bin_end = bin_start + bin_width
                if i == num_bins - 1:  # Last bin includes upper bound
                    count = sum(1 for p in profits if bin_start <= p <= bin_end)
                else:
                    count = sum(1 for p in profits if bin_start <= p < bin_end)
                bins.append((bin_start, bin_end, count))

            # Get canvas dimensions
            canvas_width = self.pnl_canvas.winfo_width()
            canvas_height = self.pnl_canvas.winfo_height()

            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width, canvas_height = 400, 300

            # Define plot area with proper margins
            margin_top = 30
            margin_bottom = 50
            margin_left = 60
            margin_right = 20

            plot_width = canvas_width - margin_left - margin_right
            plot_height = canvas_height - margin_top - margin_bottom

            # Draw histogram bars
            max_count = max(bin_count for _, _, bin_count in bins) if bins else 1
            if max_count == 0:
                max_count = 1

            bar_width = plot_width / len(bins)

            for i, (bin_start, bin_end, count) in enumerate(bins):
                if count == 0:
                    continue

                x = margin_left + i * bar_width
                height = (count / max_count) * plot_height
                y = margin_top + plot_height - height

                # Color bars: green for profit, red for loss, yellow for mixed
                if bin_end <= 0:
                    color = '#ff4444'  # Red for losses
                elif bin_start >= 0:
                    color = '#44ff44'  # Green for profits
                else:
                    color = '#ffff44'  # Yellow for bins crossing zero

                self.pnl_canvas.create_rectangle(x + 1, y, x + bar_width - 1, margin_top + plot_height,
                                               fill=color, outline='#ffffff', width=1)

            # Draw zero line if applicable
            if min_profit < 0 < max_profit:
                zero_x = margin_left + ((0 - min_profit) / profit_range) * plot_width
                self.pnl_canvas.create_line(zero_x, margin_top, zero_x, margin_top + plot_height,
                                          fill='#ffffff', width=2)
                # Zero label
                self.pnl_canvas.create_text(zero_x, margin_top + plot_height + 15,
                                          text="$0", fill='#ffffff', font=('Arial', 9), anchor='center')

            # Add axis labels
            # Y-axis (count)
            self.pnl_canvas.create_text(margin_left - 10, margin_top,
                                      text=str(max_count), fill='#ffffff', font=('Arial', 9), anchor='e')
            self.pnl_canvas.create_text(margin_left - 10, margin_top + plot_height,
                                      text="0", fill='#ffffff', font=('Arial', 9), anchor='e')

            # X-axis (profit range)
            self.pnl_canvas.create_text(margin_left, margin_top + plot_height + 15,
                                      text=f"${min_profit:.0f}", fill='#ffffff', font=('Arial', 9), anchor='center')
            self.pnl_canvas.create_text(margin_left + plot_width, margin_top + plot_height + 15,
                                      text=f"${max_profit:.0f}", fill='#ffffff', font=('Arial', 9), anchor='center')

            # Title and statistics
            winning_trades = len([p for p in profits if p > 0])
            losing_trades = len([p for p in profits if p < 0])

            self.pnl_canvas.create_text(canvas_width//2, 15,
                                      text=f"P&L Distribution - {len(trades)} trades",
                                      fill='#ffffff', font=('Arial', 12), anchor='center')
            self.pnl_canvas.create_text(canvas_width//2, canvas_height - 15,
                                      text=f"Wins: {winning_trades} | Losses: {losing_trades} | Win Rate: {(winning_trades/len(trades)*100):.1f}%",
                                      fill='#ffffff', font=('Arial', 10), anchor='center')

        except Exception as e:
            self.log_message(f"Error drawing P&L histogram: {str(e)}")



    def update_data_info_display(self) -> None:
        """Update data information display"""
        if not self.data_manager.has_data:
            self.data_info_text.delete(1.0, tk.END)
            self.data_info_text.insert(tk.END, "No data loaded")
            self.data_count_label.config(text="No data loaded")
            return

        try:
            summary = self.data_manager.get_data_summary()

            info_text = f"""Data Summary:
Total Bars: {summary['total_bars']:,}
Date Range: {summary['date_range']['start'].strftime('%Y-%m-%d')} to {summary['date_range']['end'].strftime('%Y-%m-%d')}
Price Range: ${summary['price_range']['min']:.2f} - ${summary['price_range']['max']:.2f}
Total Volume: {summary['volume_stats']['total']:,}
Average Volume: {summary['volume_stats']['average']:,.0f}

Current Bar Size: {self.bar_size_var.get()}
Current Timeframe: {self.timeframe_var.get()}
"""

            self.data_info_text.delete(1.0, tk.END)
            self.data_info_text.insert(tk.END, info_text)

            self.data_count_label.config(text=f"{summary['total_bars']:,} bars loaded")

        except Exception as e:
            self.log_message(f"Error updating data info: {str(e)}")

    # Utility Methods
    def log_message(self, message: str) -> None:
        """Add message to log display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # Only update GUI if log_text widget exists
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)

        # Always log to file
        logger.info(message)

    def clear_log(self) -> None:
        """Clear log display"""
        self.log_text.delete(1.0, tk.END)

    def update_progress(self, value: float, text: str = "") -> None:
        """Update progress bar and label"""
        self.progress_var.set(value)
        if text:
            self.progress_label.config(text=text)
        self.root.update_idletasks()

    def update_progress_threadsafe(self, value: float, text: str = "") -> None:
        """Thread-safe progress update"""
        self.root.after(0, lambda: self.update_progress(value, text))

    def update_status(self, message: str) -> None:
        """Update status bar message"""
        self.status_label.config(text=message)

    # Menu Actions
    def create_new_strategy(self) -> None:
        """Create new strategy file"""
        from tkinter.simpledialog import askstring

        strategy_name = askstring("New Strategy", "Enter strategy name:")
        if not strategy_name:
            return

        try:
            file_path = self.strategy_manager.create_new_strategy(strategy_name)
            self.log_message(f"Created new strategy: {file_path}")

            # Refresh strategies and select new one
            self.refresh_strategies()
            self.strategy_var.set(strategy_name)
            self.on_strategy_selected()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create strategy: {str(e)}")

    def save_current_strategy(self) -> None:
        """Save current strategy code - saves even with syntax errors"""
        if not self.strategy_manager.current_strategy_name:
            messagebox.showwarning("No Strategy", "No strategy selected to save")
            return

        try:
            code = self.strategy_text.get(1.0, tk.END)

            # Try to save the code - this will save even if there are syntax errors
            save_result = self.strategy_manager.save_strategy_code_with_errors(
                self.strategy_manager.current_strategy_name,
                code
            )

            if save_result['saved']:
                self.log_message(f"Saved strategy: {self.strategy_manager.current_strategy_name}")

                if save_result['syntax_errors']:
                    # Show warning about syntax errors but confirm save was successful
                    error_msg = "Strategy saved successfully, but contains syntax errors:\n\n"
                    error_msg += "\n".join(save_result['syntax_errors'])
                    error_msg += "\n\nThe file has been saved. You can continue editing to fix the errors."
                    messagebox.showwarning("Syntax Errors Detected", error_msg)
                else:
                    messagebox.showinfo("Success", "Strategy saved successfully with no syntax errors")
            else:
                # This should rarely happen - only for file system errors
                messagebox.showerror("Save Error", f"Failed to save strategy: {save_result['error']}")

        except Exception as e:
            # Fallback error handling
            self.log_message(f"Error saving strategy: {str(e)}")
            messagebox.showerror("Error", f"Failed to save strategy: {str(e)}")

    def validate_current_strategy(self) -> None:
        """Validate current strategy"""
        if not self.strategy_manager.current_strategy_name:
            messagebox.showwarning("No Strategy", "No strategy selected to validate")
            return

        try:
            result = self.strategy_manager.validate_strategy(
                self.strategy_manager.current_strategy_name
            )

            if result['valid']:
                message = "Strategy validation passed!"
                if result['warnings']:
                    message += f"\n\nWarnings:\n" + "\n".join(result['warnings'])
                messagebox.showinfo("Validation Result", message)
            else:
                message = "Strategy validation failed!\n\nErrors:\n" + "\n".join(result['errors'])
                if result['warnings']:
                    message += f"\n\nWarnings:\n" + "\n".join(result['warnings'])
                messagebox.showerror("Validation Result", message)

        except Exception as e:
            messagebox.showerror("Error", f"Validation failed: {str(e)}")

    def show_about(self) -> None:
        """Show about dialog"""
        about_text = f"""{APP_TITLE} v{APP_VERSION}

Professional Trading System GUI

Features:
• Advanced data management with resampling
• Strategy development and testing
• Parameter optimization
• Comprehensive performance metrics
• Professional dark theme

Built with Python and Tkinter
"""
        messagebox.showinfo("About", about_text)

    def run(self) -> None:
        """Start the GUI application"""
        try:
            self.log_message(f"Starting {APP_TITLE} v{APP_VERSION}")
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("Application interrupted by user")
        except Exception as e:
            logger.error(f"Application error: {str(e)}")
            messagebox.showerror("Application Error", f"An error occurred: {str(e)}")
        finally:
            self.cleanup()

    def cleanup(self) -> None:
        """Cleanup resources before exit"""
        try:
            # Stop any running threads
            if hasattr(self, 'optimization_thread') and self.optimization_thread and self.optimization_thread.is_alive():
                # Note: In a production app, you'd want proper thread cancellation
                pass

            self.log_message("Application cleanup completed")

        except Exception as e:
            logger.error(f"Cleanup error: {str(e)}")

    def log_strategy_results(self, metrics: Dict, buy_hold_metrics: Dict, is_optimized: bool = False) -> None:
        """Log strategy results to the results logger"""
        try:
            self.log_message("Starting strategy results logging...")

            # Get current strategy information
            strategy_name = self.strategy_var.get() if hasattr(self, 'strategy_var') else "Unknown"
            market_symbol = getattr(self.data_manager, 'current_symbol', 'Unknown')
            data_file = getattr(self.data_manager, 'current_file', 'Unknown')

            self.log_message(f"Strategy: {strategy_name}, Market: {market_symbol}, File: {data_file}")

            # Get timeframe information
            timeframe_info = {
                'type': self.timeframe_var.get() if hasattr(self, 'timeframe_var') else 'Unknown',
                'start_date': self.start_date_var.get() if hasattr(self, 'start_date_var') else '',
                'end_date': self.end_date_var.get() if hasattr(self, 'end_date_var') else '',
                'total_bars': len(self.data_manager.current_data) if self.data_manager.has_data else 0
            }

            # Get current parameters
            parameters = {}
            if hasattr(self, 'param_widgets'):
                for param_name, widget_info in self.param_widgets.items():
                    try:
                        # Handle different variable types
                        var = widget_info['var']
                        if hasattr(var, 'get'):
                            value = var.get()
                            if widget_info['type'] == 'int':
                                parameters[param_name] = int(value)
                            elif widget_info['type'] == 'float':
                                parameters[param_name] = float(value)
                            elif widget_info['type'] == 'bool':
                                parameters[param_name] = bool(value)
                            else:
                                parameters[param_name] = value
                        else:
                            parameters[param_name] = str(var)
                    except (ValueError, AttributeError, TypeError) as e:
                        self.log_message(f"Warning: Could not get parameter {param_name}: {str(e)}")
                        parameters[param_name] = 'Unknown'

            # Get optimization metric
            optimization_metric = self.get_selected_optimization_metric() if hasattr(self, 'get_selected_optimization_metric') else 'Total Profit'

            # Additional information
            additional_info = {
                'bar_size': self.bar_size_var.get() if hasattr(self, 'bar_size_var') else '',
                'walk_forward': is_optimized,
                'train_period': int(self.train_period_var.get()) if hasattr(self, 'train_period_var') else 0,
                'test_period': int(self.test_period_var.get()) if hasattr(self, 'test_period_var') else 0,
                'period_unit': self.period_unit_var.get() if hasattr(self, 'period_unit_var') else ''
            }

            # Get trades if available
            trades = None
            if hasattr(self.trading_system, 'best_matched_trades'):
                trades = self.trading_system.best_matched_trades
                self.log_message(f"Found {len(trades) if trades else 0} trades for logging")

            # Determine if trades should be included based on user preference
            try:
                include_trades = self.strategy_results_viewer.show_trades_var.get()
            except (AttributeError, NameError):
                include_trades = True  # Default to including trades
            self.log_message(f"Include trades setting: {include_trades}")

            # Log the results
            self.log_message(f"Calling results logger with {len(parameters)} parameters...")
            run_id = self.results_logger.log_strategy_run(
                strategy_name=strategy_name,
                market_symbol=market_symbol,
                data_file=data_file,
                timeframe_info=timeframe_info,
                parameters=parameters,
                metrics=metrics,
                buy_hold_metrics=buy_hold_metrics,
                optimization_metric=optimization_metric,
                additional_info=additional_info,
                trades=trades,
                include_trades=include_trades
            )
            self.log_message(f"Results logger returned run_id: {run_id}")

            if run_id:
                self.log_message(f"Strategy results logged: {run_id}")
                # Refresh the results viewer if it exists
                if hasattr(self, 'strategy_results_viewer'):
                    self.strategy_results_viewer.refresh_results()
            else:
                self.log_message("Failed to log strategy results")

        except Exception as e:
            logger.error(f"Error logging strategy results: {str(e)}")
            self.log_message(f"Error logging results: {str(e)}")

    def create_bulk_selection_panel(self, parent):
        """Create the bulk backtest selection panel"""
        # Data files selection
        data_frame = ttk.LabelFrame(parent, text="Data Files")
        data_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Data files listbox with scrollbar
        data_list_frame = ttk.Frame(data_frame)
        data_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.bulk_data_listbox = tk.Listbox(data_list_frame, selectmode=tk.MULTIPLE, height=8)
        data_scrollbar = ttk.Scrollbar(data_list_frame, orient=tk.VERTICAL, command=self.bulk_data_listbox.yview)
        self.bulk_data_listbox.configure(yscrollcommand=data_scrollbar.set)

        self.bulk_data_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        data_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Data files buttons
        data_buttons_frame = ttk.Frame(data_frame)
        data_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(data_buttons_frame, text="Add Data Files", command=self.add_bulk_data_files).pack(side=tk.LEFT, padx=2)
        ttk.Button(data_buttons_frame, text="Remove Selected", command=self.remove_bulk_data_files).pack(side=tk.LEFT, padx=2)
        ttk.Button(data_buttons_frame, text="Clear All", command=self.clear_bulk_data_files).pack(side=tk.LEFT, padx=2)

        # Strategies selection
        strategy_frame = ttk.LabelFrame(parent, text="Strategies")
        strategy_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Strategies listbox with scrollbar
        strategy_list_frame = ttk.Frame(strategy_frame)
        strategy_list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.bulk_strategy_listbox = tk.Listbox(strategy_list_frame, selectmode=tk.MULTIPLE, height=8)
        strategy_scrollbar = ttk.Scrollbar(strategy_list_frame, orient=tk.VERTICAL, command=self.bulk_strategy_listbox.yview)
        self.bulk_strategy_listbox.configure(yscrollcommand=strategy_scrollbar.set)

        self.bulk_strategy_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        strategy_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Strategy buttons
        strategy_buttons_frame = ttk.Frame(strategy_frame)
        strategy_buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(strategy_buttons_frame, text="Add Strategy Files", command=self.add_bulk_strategy_files).pack(side=tk.LEFT, padx=2)
        ttk.Button(strategy_buttons_frame, text="Remove Selected", command=self.remove_bulk_strategy_files).pack(side=tk.LEFT, padx=2)
        ttk.Button(strategy_buttons_frame, text="Clear All", command=self.clear_bulk_strategy_files).pack(side=tk.LEFT, padx=2)

        # Bulk backtest controls
        controls_frame = ttk.LabelFrame(parent, text="Backtest Controls")
        controls_frame.pack(fill=tk.X, padx=5, pady=5)

        # Progress bar
        self.bulk_progress = ttk.Progressbar(controls_frame, mode='determinate')
        self.bulk_progress.pack(fill=tk.X, padx=5, pady=5)

        # Status label
        self.bulk_status_label = ttk.Label(controls_frame, text="Ready")
        self.bulk_status_label.pack(pady=2)

        # Control buttons
        buttons_frame = ttk.Frame(controls_frame)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)

        self.bulk_start_button = ttk.Button(buttons_frame, text="Start Bulk Backtest", command=self.start_bulk_backtest)
        self.bulk_start_button.pack(side=tk.LEFT, padx=2)

        self.bulk_stop_button = ttk.Button(buttons_frame, text="Stop", command=self.stop_bulk_backtest, state=tk.DISABLED)
        self.bulk_stop_button.pack(side=tk.LEFT, padx=2)

        ttk.Button(buttons_frame, text="Export Results", command=self.export_bulk_results).pack(side=tk.LEFT, padx=2)

        # Initialize bulk data storage
        self.bulk_data_files = []
        self.bulk_strategy_files = []
        self.bulk_results = []
        self.bulk_running = False

    def create_bulk_results_panel(self, parent):
        """Create the bulk backtest results panel"""
        # Results table
        results_frame = ttk.Frame(parent)
        results_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Define columns for bulk results
        bulk_columns = [
            'Data File', 'Strategy', 'Total Profit', 'Total Trades', 'Win Rate %',
            'Profit Factor', 'Max Drawdown', 'Sharpe Ratio', 'Status'
        ]

        self.bulk_results_tree = ttk.Treeview(results_frame, columns=bulk_columns, show='headings', height=15)

        # Configure column widths and headings
        column_widths = {
            'Data File': 120,
            'Strategy': 120,
            'Total Profit': 100,
            'Total Trades': 80,
            'Win Rate %': 80,
            'Profit Factor': 80,
            'Max Drawdown': 100,
            'Sharpe Ratio': 80,
            'Status': 80
        }

        for col in bulk_columns:
            self.bulk_results_tree.heading(col, text=col)
            self.bulk_results_tree.column(col, width=column_widths.get(col, 100), minwidth=50)

        # Scrollbars for results table
        bulk_v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.bulk_results_tree.yview)
        bulk_h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.bulk_results_tree.xview)
        self.bulk_results_tree.configure(yscrollcommand=bulk_v_scrollbar.set, xscrollcommand=bulk_h_scrollbar.set)

        # Pack results table and scrollbars
        self.bulk_results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        bulk_v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        bulk_h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # Apply theme if available
        if self.theme_manager:
            self.theme_manager.apply_dark_theme_to_widget(self.bulk_results_tree)

    # Bulk backtest functionality methods
    def add_bulk_data_files(self):
        """Add data files for bulk backtesting"""
        file_paths = filedialog.askopenfilenames(
            title="Select Data Files",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        for file_path in file_paths:
            if file_path not in self.bulk_data_files:
                self.bulk_data_files.append(file_path)
                filename = os.path.basename(file_path)
                self.bulk_data_listbox.insert(tk.END, filename)

        self.log_message(f"Added {len(file_paths)} data files to bulk backtest")

    def remove_bulk_data_files(self):
        """Remove selected data files"""
        selected_indices = self.bulk_data_listbox.curselection()
        for index in reversed(selected_indices):
            self.bulk_data_listbox.delete(index)
            del self.bulk_data_files[index]

    def clear_bulk_data_files(self):
        """Clear all data files"""
        self.bulk_data_listbox.delete(0, tk.END)
        self.bulk_data_files.clear()

    def add_bulk_strategy_files(self):
        """Add strategy files for bulk backtesting"""
        file_paths = filedialog.askopenfilenames(
            title="Select Strategy Files",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )

        for file_path in file_paths:
            if file_path not in self.bulk_strategy_files:
                self.bulk_strategy_files.append(file_path)
                filename = os.path.basename(file_path)
                self.bulk_strategy_listbox.insert(tk.END, filename)

        self.log_message(f"Added {len(file_paths)} strategy files to bulk backtest")

    def remove_bulk_strategy_files(self):
        """Remove selected strategy files"""
        selected_indices = self.bulk_strategy_listbox.curselection()
        for index in reversed(selected_indices):
            self.bulk_strategy_listbox.delete(index)
            del self.bulk_strategy_files[index]

    def clear_bulk_strategy_files(self):
        """Clear all strategy files"""
        self.bulk_strategy_listbox.delete(0, tk.END)
        self.bulk_strategy_files.clear()

    def start_bulk_backtest(self):
        """Start bulk backtesting process"""
        if not self.bulk_data_files:
            messagebox.showwarning("No Data Files", "Please add at least one data file.")
            return

        if not self.bulk_strategy_files:
            messagebox.showwarning("No Strategy Files", "Please add at least one strategy file.")
            return

        # Clear previous results
        for item in self.bulk_results_tree.get_children():
            self.bulk_results_tree.delete(item)
        self.bulk_results.clear()

        # Set up progress tracking
        total_combinations = len(self.bulk_data_files) * len(self.bulk_strategy_files)
        self.bulk_progress['maximum'] = total_combinations
        self.bulk_progress['value'] = 0

        # Update UI state
        self.bulk_running = True
        self.bulk_start_button.config(state=tk.DISABLED)
        self.bulk_stop_button.config(state=tk.NORMAL)

        # Start bulk backtest in separate thread
        import threading
        self.bulk_thread = threading.Thread(target=self.run_bulk_backtest, daemon=True)
        self.bulk_thread.start()

    def stop_bulk_backtest(self):
        """Stop bulk backtesting process"""
        self.bulk_running = False
        self.bulk_start_button.config(state=tk.NORMAL)
        self.bulk_stop_button.config(state=tk.DISABLED)
        self.bulk_status_label.config(text="Stopped")

    def export_bulk_results(self):
        """Export bulk backtest results to CSV"""
        if not self.bulk_results:
            messagebox.showinfo("No Results", "No bulk backtest results to export.")
            return

        file_path = filedialog.asksaveasfilename(
            title="Export Bulk Results",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                import pandas as pd
                df = pd.DataFrame(self.bulk_results)
                df.to_csv(file_path, index=False)
                messagebox.showinfo("Export Complete", f"Results exported to {file_path}")
                self.log_message(f"Bulk results exported to: {file_path}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export results: {str(e)}")

    def run_bulk_backtest(self):
        """Run bulk backtest in background thread"""
        try:
            current_combination = 0
            total_combinations = len(self.bulk_data_files) * len(self.bulk_strategy_files)

            for data_file in self.bulk_data_files:
                if not self.bulk_running:
                    break

                data_filename = os.path.basename(data_file)

                for strategy_file in self.bulk_strategy_files:
                    if not self.bulk_running:
                        break

                    current_combination += 1
                    strategy_filename = os.path.basename(strategy_file)

                    # Update status
                    status_text = f"Testing {data_filename} with {strategy_filename} ({current_combination}/{total_combinations})"
                    self.root.after(0, lambda text=status_text: self.bulk_status_label.config(text=text))

                    # Run single backtest
                    result = self.run_single_bulk_backtest(data_file, strategy_file)

                    # Add result to table
                    if result:
                        self.root.after(0, lambda r=result: self.add_bulk_result_to_table(r))
                        self.bulk_results.append(result)

                    # Update progress
                    self.root.after(0, lambda val=current_combination: self.bulk_progress.config(value=val))

            # Completed
            self.root.after(0, self.bulk_backtest_completed)

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"Bulk backtest error: {str(e)}"))
            self.root.after(0, self.bulk_backtest_completed)

    def run_single_bulk_backtest(self, data_file, strategy_file):
        """Run a single backtest combination"""
        try:
            # Load data
            temp_data_manager = DataManager()
            temp_data_manager.load_data(data_file)

            if not temp_data_manager.has_data:
                return {
                    'Data File': os.path.basename(data_file),
                    'Strategy': os.path.basename(strategy_file),
                    'Total Profit': 0,
                    'Total Trades': 0,
                    'Win Rate %': 0,
                    'Profit Factor': 0,
                    'Max Drawdown': 0,
                    'Sharpe Ratio': 0,
                    'Status': 'Data Load Failed'
                }

            # Create temporary strategy manager for this backtest
            temp_strategy_manager = StrategyManager()
            temp_strategy_manager.load_strategy_from_file(strategy_file)

            # Create trading system instance using the strategy
            strategy_module = temp_strategy_manager.strategy_module
            trading_system = strategy_module.MyTradingSystem(temp_data_manager.data)

            # Run backtest
            trading_system.run_backtest()

            # Get metrics using metrics calculator
            temp_metrics_calculator = MetricsCalculator()
            metrics = temp_metrics_calculator.calculate_metrics(trading_system)

            return {
                'Data File': os.path.basename(data_file),
                'Strategy': os.path.basename(strategy_file),
                'Total Profit': f"${metrics.get('total_profit', 0):.2f}",
                'Total Trades': metrics.get('total_trades', 0),
                'Win Rate %': f"{metrics.get('win_rate', 0):.1f}%",
                'Profit Factor': f"{metrics.get('profit_factor', 0):.2f}",
                'Max Drawdown': f"${metrics.get('max_drawdown', 0):.2f}",
                'Sharpe Ratio': f"{metrics.get('sharpe_ratio', 0):.2f}",
                'Status': 'Completed'
            }

        except Exception as e:
            return {
                'Data File': os.path.basename(data_file),
                'Strategy': os.path.basename(strategy_file),
                'Total Profit': '$0.00',
                'Total Trades': 0,
                'Win Rate %': '0.0%',
                'Profit Factor': '0.00',
                'Max Drawdown': '$0.00',
                'Sharpe Ratio': '0.00',
                'Status': f'Error: {str(e)[:30]}...'
            }

    def add_bulk_result_to_table(self, result):
        """Add a result to the bulk results table"""
        values = [
            result['Data File'],
            result['Strategy'],
            result['Total Profit'],
            result['Total Trades'],
            result['Win Rate %'],
            result['Profit Factor'],
            result['Max Drawdown'],
            result['Sharpe Ratio'],
            result['Status']
        ]

        # Color code based on status
        if result['Status'] == 'Completed':
            tag = 'completed'
        elif 'Error' in result['Status']:
            tag = 'error'
        else:
            tag = 'failed'

        self.bulk_results_tree.insert('', 'end', values=values, tags=(tag,))

        # Configure row colors for dark theme
        self.bulk_results_tree.tag_configure('completed', background='#2d4a2d', foreground='#90ee90')  # Dark green bg, light green text
        self.bulk_results_tree.tag_configure('error', background='#4a2d2d', foreground='#ff9090')      # Dark red bg, light red text
        self.bulk_results_tree.tag_configure('failed', background='#4a4a2d', foreground='#ffff90')     # Dark yellow bg, light yellow text

    def bulk_backtest_completed(self):
        """Handle bulk backtest completion"""
        self.bulk_running = False
        self.bulk_start_button.config(state=tk.NORMAL)
        self.bulk_stop_button.config(state=tk.DISABLED)
        self.bulk_status_label.config(text=f"Completed - {len(self.bulk_results)} results")
        self.log_message(f"Bulk backtest completed with {len(self.bulk_results)} results")


def main():
    """Main entry point"""
    try:
        app = PyTSGUI()
        app.run()
    except Exception as e:
        logger.error(f"Failed to start application: {str(e)}")
        print(f"Error: {str(e)}")


if __name__ == "__main__":
    main()
