2025-06-30 17:20:06,190 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 17:20:06,207 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 17:20:06,239 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 17:20:06,272 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 17:20:06,292 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 17:20:06,300 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 17:20:06,307 - __main__ - INFO - Found 3 strategies
2025-06-30 17:20:06,329 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 17:20:12,211 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-06-30 17:20:12,763 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-06-30 17:20:12,779 - __main__ - INFO - Successfully loaded 6617 bars
2025-06-30 17:20:21,535 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-06-30 17:20:21,543 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-06-30 17:20:41,229 - __main__ - INFO - Starting optimization...
2025-06-30 17:20:41,247 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-06-30 17:20:41,248 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 17:20:41,249 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-06-30 17:20:41,250 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-06-30 17:20:41,250 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-06-30 17:20:41,251 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-06-30 17:20:55,227 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: 3.1127
2025-06-30 17:20:55,228 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 3.1127
2025-06-30 17:20:55,241 - __main__ - INFO - Optimization completed. Best performance: 3.1127
2025-06-30 17:20:55,249 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 1, 'LX': 1, 'SX': 10}
2025-06-30 17:20:55,255 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 17:20:55,361 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 1.0, 'LX': 1.0, 'SX': 10.0}
2025-06-30 17:20:55,369 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 1.0, 'LX': 1.0, 'SX': 10.0}
2025-06-30 17:20:55,385 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-06-30 17:20:55,399 - gui.metrics_calculator - INFO - Calculated metrics for 100 trades
2025-06-30 17:20:55,417 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-06-30 17:20:55,436 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-06-30 17:20:55,444 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-06-30 17:20:55,459 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 17:21:05,025 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-06-30 17:21:05,043 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-06-30 17:21:05,052 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-06-30 17:22:47,444 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 17:23:47,034 - gui.data_manager - INFO - Successfully loaded 727258 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 17:23:47,638 - __main__ - INFO - Successfully loaded 727258 bars
2025-06-30 17:24:13,430 - __main__ - INFO - Changing bar size to: 30 Minutes
2025-06-30 17:24:19,669 - __main__ - INFO - Data resampled to 30 Minutes: 24568 bars
2025-06-30 17:26:20,957 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 17:26:20,959 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 17:26:20,971 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 17:26:20,971 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 17:26:53,470 - __main__ - INFO - Starting optimization...
2025-06-30 17:26:53,508 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:26:53,509 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=1 days
2025-06-30 17:26:53,509 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 17:26:53,510 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 17:26:53,512 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 17:26:53,513 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 17:26:53,513 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 17:26:53,514 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 17:26:53,514 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 17:27:31,164 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 16040.9438
2025-06-30 17:27:31,165 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 16040.9438
2025-06-30 17:27:31,173 - __main__ - INFO - Optimization completed. Best performance: 16040.9438
2025-06-30 17:27:31,175 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 8, 'RollMinPeriod': 3, 'LXlimPct': 9, 'SXlimPct': 9}
2025-06-30 17:27:31,177 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 17:27:31,287 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 8.0, 'RollMinPeriod': 3.0, 'LXlimPct': 9.0, 'SXlimPct': 9.0}
2025-06-30 17:27:31,289 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 8.0, 'RollMinPeriod': 3.0, 'LXlimPct': 9.0, 'SXlimPct': 9.0}
2025-06-30 17:27:31,324 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:31:08,226 - gui.metrics_calculator - INFO - Calculated metrics for 1089 trades
2025-06-30 17:31:08,291 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:31:08,344 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:31:08,369 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 17:31:08,384 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 17:32:18,718 - __main__ - INFO - Starting optimization...
2025-06-30 17:32:18,756 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:32:18,757 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 17:32:18,757 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 17:32:18,758 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 17:32:18,758 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 17:32:18,759 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 17:32:18,759 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 17:32:18,760 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 17:32:18,761 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 17:32:50,482 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 16040.9438
2025-06-30 17:32:50,483 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 16040.9438
2025-06-30 17:32:50,486 - __main__ - INFO - Optimization completed. Best performance: 16040.9438
2025-06-30 17:32:50,488 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 8, 'RollMinPeriod': 3, 'LXlimPct': 9, 'SXlimPct': 9}
2025-06-30 17:32:50,490 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 17:32:50,597 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 8.0, 'RollMinPeriod': 3.0, 'LXlimPct': 9.0, 'SXlimPct': 9.0}
2025-06-30 17:32:50,599 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 8.0, 'RollMinPeriod': 3.0, 'LXlimPct': 9.0, 'SXlimPct': 9.0}
2025-06-30 17:32:50,635 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:32:51,865 - gui.metrics_calculator - INFO - Calculated metrics for 1089 trades
2025-06-30 17:32:51,927 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:32:51,982 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:32:52,008 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 17:32:52,021 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 17:33:41,617 - __main__ - INFO - Starting optimization...
2025-06-30 17:33:41,657 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:33:41,658 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=30 days
2025-06-30 17:33:41,658 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 17:33:41,659 - gui.strategy_manager - INFO - Window 1: Train 3692 bars (2018-11-30 09:30:00 to 2019-11-25 09:30:00), Test 1228 bars (2019-11-25 10:00:00 to 2020-03-25 15:30:00)
2025-06-30 17:33:41,660 - gui.strategy_manager - INFO - Window 2: Train 3708 bars (2020-03-25 16:00:00 to 2021-03-19 16:30:00), Test 1228 bars (2021-03-22 09:30:00 to 2021-07-16 15:30:00)
2025-06-30 17:33:41,661 - gui.strategy_manager - INFO - Window 3: Train 3684 bars (2021-07-16 16:00:00 to 2022-07-11 16:00:00), Test 1228 bars (2022-07-11 16:30:00 to 2022-11-03 15:00:00)
2025-06-30 17:33:41,661 - gui.strategy_manager - INFO - Window 4: Train 3678 bars (2022-11-03 15:30:00 to 2023-10-27 16:30:00), Test 1228 bars (2023-10-30 09:30:00 to 2024-02-28 11:30:00)
2025-06-30 17:33:41,662 - gui.strategy_manager - INFO - Walk-forward optimization: Created 4 windows
2025-06-30 17:34:45,354 - gui.strategy_manager - INFO - Walk-forward completed: 4 windows, avg performance: 54050.6358
2025-06-30 17:34:45,363 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 54050.6358
2025-06-30 17:34:45,370 - __main__ - INFO - Optimization completed. Best performance: 54050.6358
2025-06-30 17:34:45,372 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 9, 'RollMinPeriod': 4, 'LXlimPct': 10, 'SXlimPct': 9}
2025-06-30 17:34:45,375 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 17:34:45,488 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 9.0, 'RollMinPeriod': 4.0, 'LXlimPct': 10.0, 'SXlimPct': 9.0}
2025-06-30 17:34:45,491 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 9.0, 'RollMinPeriod': 4.0, 'LXlimPct': 10.0, 'SXlimPct': 9.0}
2025-06-30 17:34:45,525 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:35:42,345 - gui.strategy_manager - INFO - Saved strategy code: NQ30min1
2025-06-30 17:35:42,407 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 17:36:01,729 - __main__ - INFO - Starting optimization...
2025-06-30 17:36:02,167 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:36:02,437 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=30 days
2025-06-30 17:36:02,469 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 17:36:02,496 - gui.strategy_manager - INFO - Window 1: Train 3692 bars (2018-11-30 09:30:00 to 2019-11-25 09:30:00), Test 1228 bars (2019-11-25 10:00:00 to 2020-03-25 15:30:00)
2025-06-30 17:36:02,530 - gui.strategy_manager - INFO - Window 2: Train 3708 bars (2020-03-25 16:00:00 to 2021-03-19 16:30:00), Test 1228 bars (2021-03-22 09:30:00 to 2021-07-16 15:30:00)
2025-06-30 17:36:02,581 - gui.strategy_manager - INFO - Window 3: Train 3684 bars (2021-07-16 16:00:00 to 2022-07-11 16:00:00), Test 1228 bars (2022-07-11 16:30:00 to 2022-11-03 15:00:00)
2025-06-30 17:36:02,611 - gui.strategy_manager - INFO - Window 4: Train 3678 bars (2022-11-03 15:30:00 to 2023-10-27 16:30:00), Test 1228 bars (2023-10-30 09:30:00 to 2024-02-28 11:30:00)
2025-06-30 17:36:02,646 - gui.strategy_manager - INFO - Walk-forward optimization: Created 4 windows
2025-06-30 17:37:35,624 - gui.metrics_calculator - INFO - Calculated metrics for 1066 trades
2025-06-30 17:37:35,887 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:37:35,991 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:37:36,015 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 17:37:36,100 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 17:39:06,319 - __main__ - INFO - Starting backtest...
2025-06-30 17:39:06,467 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 9.0, 'RollMinPeriod': 4.0, 'LXlimPct': 10.0, 'SXlimPct': 9.0}
2025-06-30 17:39:06,535 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 9.0, 'RollMinPeriod': 4.0, 'LXlimPct': 10.0, 'SXlimPct': 9.0}
2025-06-30 17:39:06,667 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:40:34,027 - __main__ - INFO - Found 3 strategies
2025-06-30 17:40:35,348 - __main__ - INFO - Found 3 strategies
2025-06-30 17:40:36,590 - __main__ - INFO - Found 3 strategies
2025-06-30 17:41:01,475 - __main__ - INFO - Starting optimization...
2025-06-30 17:41:01,988 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:41:02,308 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=30 days
2025-06-30 17:41:02,338 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 17:41:02,371 - gui.strategy_manager - INFO - Window 1: Train 3692 bars (2018-11-30 09:30:00 to 2019-11-25 09:30:00), Test 1228 bars (2019-11-25 10:00:00 to 2020-03-25 15:30:00)
2025-06-30 17:41:02,473 - gui.strategy_manager - INFO - Window 2: Train 3708 bars (2020-03-25 16:00:00 to 2021-03-19 16:30:00), Test 1228 bars (2021-03-22 09:30:00 to 2021-07-16 15:30:00)
2025-06-30 17:41:02,503 - gui.strategy_manager - INFO - Window 3: Train 3684 bars (2021-07-16 16:00:00 to 2022-07-11 16:00:00), Test 1228 bars (2022-07-11 16:30:00 to 2022-11-03 15:00:00)
2025-06-30 17:41:02,538 - gui.strategy_manager - INFO - Window 4: Train 3678 bars (2022-11-03 15:30:00 to 2023-10-27 16:30:00), Test 1228 bars (2023-10-30 09:30:00 to 2024-02-28 11:30:00)
2025-06-30 17:41:02,571 - gui.strategy_manager - INFO - Walk-forward optimization: Created 4 windows
2025-06-30 17:41:03,053 - gui.metrics_calculator - INFO - Calculated metrics for 1072 trades
2025-06-30 17:41:03,688 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:41:04,133 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:41:04,161 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 17:41:04,508 - __main__ - INFO - Backtest completed successfully
2025-06-30 17:41:22,567 - gui.strategy_manager - INFO - Walk-forward completed: 4 windows, avg performance: 67451.2710
2025-06-30 17:41:22,570 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 67451.2710
2025-06-30 17:41:22,584 - __main__ - INFO - Optimization completed. Best performance: 67451.2710
2025-06-30 17:41:22,586 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 5, 'RollMinPeriod': 3, 'LXlimPct': 15, 'SXlimPct': 13}
2025-06-30 17:41:22,590 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 17:41:22,692 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 5.0, 'RollMinPeriod': 3.0, 'LXlimPct': 15.0, 'SXlimPct': 13.0}
2025-06-30 17:41:22,725 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 5.0, 'RollMinPeriod': 3.0, 'LXlimPct': 15.0, 'SXlimPct': 13.0}
2025-06-30 17:41:22,764 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:43:24,616 - gui.metrics_calculator - INFO - Calculated metrics for 1055 trades
2025-06-30 17:43:24,900 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:43:24,967 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:43:24,993 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 17:43:25,071 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 17:43:52,997 - gui.strategy_manager - INFO - Walk-forward completed: 4 windows, avg performance: 55689.1060
2025-06-30 17:43:52,998 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 55689.1060
2025-06-30 17:43:53,005 - __main__ - INFO - Optimization completed. Best performance: 55689.1060
2025-06-30 17:43:53,007 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 9, 'RollMinPeriod': 9, 'LXlimPct': 18, 'SXlimPct': 10}
2025-06-30 17:43:53,009 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 17:43:53,123 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 9.0, 'RollMinPeriod': 9.0, 'LXlimPct': 18.0, 'SXlimPct': 10.0}
2025-06-30 17:43:53,125 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 9.0, 'RollMinPeriod': 9.0, 'LXlimPct': 18.0, 'SXlimPct': 10.0}
2025-06-30 17:43:53,160 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:45:40,356 - gui.metrics_calculator - INFO - Calculated metrics for 964 trades
2025-06-30 17:45:40,419 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:45:40,479 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 17:45:40,501 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 17:45:40,514 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 17:48:15,314 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 17:57:17,511 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 17:57:17,566 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 17:57:17,612 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 17:57:17,642 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 17:57:17,660 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 17:57:17,670 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 17:57:17,673 - __main__ - INFO - Found 3 strategies
2025-06-30 17:57:17,681 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 17:57:26,171 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 17:58:56,549 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 17:58:56,557 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 17:58:56,564 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 17:58:56,570 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 17:58:56,587 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 17:58:56,632 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 17:58:56,668 - __main__ - INFO - Found 3 strategies
2025-06-30 17:58:56,676 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 17:59:02,229 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 17:59:12,898 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 17:59:13,145 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 17:59:14,063 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 17:59:14,131 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 17:59:20,983 - __main__ - INFO - Found 3 strategies
2025-06-30 18:00:03,863 - gui.data_manager - INFO - Successfully loaded 727258 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 18:00:04,477 - __main__ - INFO - Successfully loaded 727258 bars
2025-06-30 18:01:25,278 - __main__ - INFO - Changing bar size to: 30 Minutes
2025-06-30 18:01:31,658 - __main__ - INFO - Data resampled to 30 Minutes: 24568 bars
2025-06-30 18:02:15,002 - __main__ - INFO - Starting optimization...
2025-06-30 18:02:15,006 - __main__ - INFO - Starting optimization with metric: ddr_ratio
2025-06-30 18:02:15,006 - gui.strategy_manager - INFO - Optimization will use metric: ddr_ratio
2025-06-30 18:02:15,007 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 18:02:15,007 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 18:02:15,008 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 18:02:15,009 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 18:02:15,010 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 18:02:15,010 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 18:02:15,011 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 18:02:15,011 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 18:03:25,371 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 115.4323
2025-06-30 18:03:25,373 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 115.4323
2025-06-30 18:03:25,378 - __main__ - INFO - Optimization completed. Best performance: 115.4323
2025-06-30 18:03:25,378 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 2, 'LXlimPct': 19, 'SXlimPct': 10}
2025-06-30 18:03:25,379 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 18:03:25,484 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 2.0, 'LXlimPct': 19.0, 'SXlimPct': 10.0}
2025-06-30 18:03:25,485 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 2.0, 'LXlimPct': 19.0, 'SXlimPct': 10.0}
2025-06-30 18:06:04,629 - __main__ - INFO - Starting optimization...
2025-06-30 18:06:05,186 - __main__ - INFO - Starting optimization with metric: ddr_ratio
2025-06-30 18:06:05,218 - gui.strategy_manager - INFO - Optimization will use metric: ddr_ratio
2025-06-30 18:06:05,352 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 18:06:05,384 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 18:06:05,434 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 18:06:05,479 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 18:06:05,501 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 18:06:05,550 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 18:06:05,587 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 18:06:05,634 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 18:07:07,624 - gui.metrics_calculator - INFO - Calculated metrics for 1088 trades
2025-06-30 18:07:07,719 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:07:07,802 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 18:07:46,825 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 2722.8021
2025-06-30 18:07:46,826 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 2722.8021
2025-06-30 18:07:46,831 - __main__ - INFO - Optimization completed. Best performance: 2722.8021
2025-06-30 18:07:46,832 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 7, 'RollMinPeriod': 2, 'LXlimPct': 20, 'SXlimPct': 13}
2025-06-30 18:07:46,833 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 18:07:46,947 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 7.0, 'RollMinPeriod': 2.0, 'LXlimPct': 20.0, 'SXlimPct': 13.0}
2025-06-30 18:07:46,947 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 7.0, 'RollMinPeriod': 2.0, 'LXlimPct': 20.0, 'SXlimPct': 13.0}
2025-06-30 18:09:36,174 - gui.metrics_calculator - INFO - Calculated metrics for 1093 trades
2025-06-30 18:09:36,242 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:09:36,254 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 18:10:48,405 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:10:53,614 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:11:54,273 - __main__ - INFO - Starting optimization...
2025-06-30 18:11:54,278 - __main__ - INFO - Starting optimization with metric: sortino_ratio
2025-06-30 18:11:54,279 - gui.strategy_manager - INFO - Optimization will use metric: sortino_ratio
2025-06-30 18:11:54,279 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 18:11:54,280 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 18:11:54,280 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 18:11:54,283 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 18:11:54,283 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 18:11:54,285 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 18:11:54,286 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 18:11:54,287 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 18:12:33,877 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 53.6649
2025-06-30 18:12:33,878 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 53.6649
2025-06-30 18:12:33,885 - __main__ - INFO - Optimization completed. Best performance: 53.6649
2025-06-30 18:12:33,887 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 9, 'RollMinPeriod': 6, 'LXlimPct': 11, 'SXlimPct': 11}
2025-06-30 18:12:33,889 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 18:12:33,993 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 9.0, 'RollMinPeriod': 6.0, 'LXlimPct': 11.0, 'SXlimPct': 11.0}
2025-06-30 18:12:33,995 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 9.0, 'RollMinPeriod': 6.0, 'LXlimPct': 11.0, 'SXlimPct': 11.0}
2025-06-30 18:16:08,726 - gui.metrics_calculator - INFO - Calculated metrics for 1039 trades
2025-06-30 18:16:08,797 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:16:08,811 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 18:16:44,248 - __main__ - INFO - Starting optimization...
2025-06-30 18:16:44,253 - __main__ - INFO - Starting optimization with metric: calmar_ratio
2025-06-30 18:16:44,253 - gui.strategy_manager - INFO - Optimization will use metric: calmar_ratio
2025-06-30 18:16:44,255 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 18:16:44,255 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 18:16:44,256 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 18:16:44,257 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 18:16:44,257 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 18:16:44,258 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 18:16:44,258 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 18:16:44,259 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 18:17:28,287 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 481.6811
2025-06-30 18:17:28,288 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 481.6811
2025-06-30 18:17:28,290 - __main__ - INFO - Optimization completed. Best performance: 481.6811
2025-06-30 18:17:28,292 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 6, 'LXlimPct': 12, 'SXlimPct': 10}
2025-06-30 18:17:28,294 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 18:17:28,396 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 6.0, 'LXlimPct': 12.0, 'SXlimPct': 10.0}
2025-06-30 18:17:28,399 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 6.0, 'LXlimPct': 12.0, 'SXlimPct': 10.0}
2025-06-30 18:17:57,482 - gui.metrics_calculator - INFO - Calculated metrics for 1038 trades
2025-06-30 18:17:57,550 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:17:57,564 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 18:21:15,455 - gui.strategy_manager - ERROR - Error saving strategy code: inconsistent use of tabs and spaces in indentation (NQ30min1.py, line 70)
2025-06-30 18:21:36,186 - gui.strategy_manager - ERROR - Error saving strategy code: inconsistent use of tabs and spaces in indentation (NQ30min1.py, line 70)
2025-06-30 18:21:41,211 - gui.strategy_manager - ERROR - Error saving strategy code: inconsistent use of tabs and spaces in indentation (NQ30min1.py, line 70)
2025-06-30 18:21:44,279 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 18:24:33,915 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 18:24:33,971 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 18:24:33,987 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 18:24:34,001 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 18:24:34,017 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 18:24:34,024 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 18:24:34,028 - __main__ - INFO - Found 3 strategies
2025-06-30 18:24:34,034 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 18:24:38,809 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 18:25:03,967 - gui.strategy_manager - INFO - Saved strategy code: 4BarCompare
2025-06-30 18:25:04,016 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 18:25:54,429 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 18:25:54,438 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 18:25:54,444 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 18:25:54,450 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 18:25:54,468 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 18:25:54,475 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 18:25:54,480 - __main__ - INFO - Found 3 strategies
2025-06-30 18:25:54,487 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 18:27:30,811 - __main__ - INFO - Application interrupted by user
2025-06-30 18:27:30,816 - __main__ - INFO - Application cleanup completed
2025-06-30 18:30:20,517 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 18:30:20,526 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 18:30:20,531 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 18:30:20,538 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 18:30:20,555 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 18:30:20,562 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 18:30:20,565 - __main__ - INFO - Found 3 strategies
2025-06-30 18:30:20,572 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 18:30:29,679 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 18:31:30,514 - gui.data_manager - INFO - Successfully loaded 727258 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 18:31:31,124 - __main__ - INFO - Successfully loaded 727258 bars
2025-06-30 18:31:35,268 - __main__ - INFO - Changing bar size to: 30 Minutes
2025-06-30 18:31:41,628 - __main__ - INFO - Data resampled to 30 Minutes: 24568 bars
2025-06-30 18:31:58,546 - __main__ - INFO - Found 3 strategies
2025-06-30 18:32:02,428 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 18:32:02,430 - gui.strategy_manager - ERROR - Error loading strategy NQ30min1: inconsistent use of tabs and spaces in indentation (NQ30min1.py, line 70)
2025-06-30 18:32:02,445 - __main__ - INFO - Error loading strategy: Failed to load strategy: inconsistent use of tabs and spaces in indentation (NQ30min1.py, line 70)
2025-06-30 18:32:20,027 - __main__ - INFO - Found 3 strategies
2025-06-30 18:33:13,334 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 18:33:13,335 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 18:33:13,354 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 18:33:13,360 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 18:33:15,209 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 18:33:15,211 - gui.strategy_manager - ERROR - Error loading strategy NQ30min1: inconsistent use of tabs and spaces in indentation (NQ30min1.py, line 70)
2025-06-30 18:33:15,228 - __main__ - INFO - Error loading strategy: Failed to load strategy: inconsistent use of tabs and spaces in indentation (NQ30min1.py, line 70)
2025-06-30 18:38:47,969 - __main__ - INFO - Found 3 strategies
2025-06-30 18:38:49,121 - __main__ - INFO - Found 3 strategies
2025-06-30 18:38:51,299 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 18:38:51,300 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 18:38:51,320 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 18:38:51,326 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 18:38:53,451 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 18:38:53,456 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:38:53,481 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 18:38:53,488 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 18:39:04,423 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:39:29,347 - __main__ - INFO - Starting optimization...
2025-06-30 18:39:29,365 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 18:39:29,365 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 18:39:29,366 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=1 days
2025-06-30 18:39:29,366 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 18:39:29,366 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 18:39:29,367 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 18:39:29,367 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 18:39:29,368 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 18:39:29,368 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 18:39:29,368 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 18:40:56,382 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 77541.7500
2025-06-30 18:40:56,383 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 77541.7500
2025-06-30 18:40:56,395 - __main__ - INFO - Optimization completed. Best performance: 77541.7500
2025-06-30 18:40:56,411 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 8, 'LXlim': 11, 'SXlim': 14, 'HighThreshold': 20, 'LowThreshold': 16}
2025-06-30 18:40:56,419 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 18:40:56,528 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 11.0, 'SXlim': 14.0, 'HighThreshold': 20.0, 'LowThreshold': 16.0}
2025-06-30 18:40:56,544 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 11.0, 'SXlim': 14.0, 'HighThreshold': 20.0, 'LowThreshold': 16.0}
2025-06-30 18:44:40,046 - gui.metrics_calculator - INFO - Calculated metrics for 981 trades
2025-06-30 18:44:40,124 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:44:40,139 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 18:44:49,850 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:46:35,366 - __main__ - INFO - Starting backtest...
2025-06-30 18:46:35,369 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 11.0, 'SXlim': 14.0, 'HighThreshold': 20.0, 'LowThreshold': 16.0}
2025-06-30 18:46:35,371 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 11.0, 'SXlim': 14.0, 'HighThreshold': 20.0, 'LowThreshold': 16.0}
2025-06-30 18:46:39,010 - gui.metrics_calculator - INFO - Calculated metrics for 981 trades
2025-06-30 18:46:39,078 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 18:46:39,155 - __main__ - INFO - Backtest completed successfully
2025-06-30 18:50:39,175 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 18:54:42,450 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 18:54:42,504 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 18:54:42,513 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 18:54:42,519 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 18:54:42,568 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 18:54:42,574 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 18:54:42,578 - __main__ - INFO - Found 3 strategies
2025-06-30 18:54:42,586 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 18:54:48,784 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 18:54:48,785 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:54:48,850 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 18:54:48,856 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 18:55:04,444 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:55:08,436 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:55:12,615 - gui.strategy_manager - INFO - Saved strategy code with syntax errors: NQ30min1
2025-06-30 18:55:12,619 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 18:55:16,150 - gui.strategy_manager - INFO - Saved strategy code with syntax errors: NQ30min1
2025-06-30 18:55:16,154 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 18:55:19,996 - gui.strategy_manager - ERROR - Error loading strategy NQ30min1: invalid syntax (NQ30min1.py, line 9)
2025-06-30 18:55:24,034 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 18:55:24,038 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 18:55:26,156 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:55:28,687 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 18:56:36,368 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 18:56:36,377 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 18:56:36,383 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 18:56:36,388 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 18:56:36,437 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 18:56:36,445 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 18:56:36,448 - __main__ - INFO - Found 3 strategies
2025-06-30 18:56:36,454 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 18:56:44,369 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 18:56:44,370 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:56:44,433 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 18:56:44,439 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 18:57:00,842 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 18:57:00,847 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 18:57:21,719 - __main__ - INFO - Found 3 strategies
2025-06-30 18:57:24,376 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 18:57:24,380 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 18:57:26,830 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:57:37,878 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:57:40,472 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 18:57:40,477 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 18:58:06,024 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 18:58:06,028 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 18:58:07,526 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 18:58:11,746 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 18:58:36,553 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 18:58:36,601 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 18:58:36,606 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 18:58:36,612 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 18:58:36,657 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 18:58:36,664 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 18:58:36,668 - __main__ - INFO - Found 4 strategies
2025-06-30 18:58:36,674 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 18:58:43,563 - __main__ - INFO - Loading strategy: SyntaxDemo
2025-06-30 18:58:43,569 - gui.strategy_manager - INFO - Successfully loaded strategy: SyntaxDemo
2025-06-30 18:58:43,663 - __main__ - INFO - Strategy 'SyntaxDemo' loaded successfully
2025-06-30 18:58:59,271 - gui.strategy_manager - INFO - Successfully loaded strategy: SyntaxDemo
2025-06-30 18:59:10,692 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/^DJI.csv
2025-06-30 18:59:11,417 - gui.data_manager - INFO - Successfully loaded 8432 bars from C:/Users/<USER>/Desktop/Data1/ETF/^DJI.csv
2025-06-30 18:59:11,436 - __main__ - INFO - Successfully loaded 8432 bars
2025-06-30 18:59:19,161 - __main__ - INFO - Starting optimization...
2025-06-30 18:59:19,171 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 18:59:19,172 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 18:59:20,617 - __main__ - INFO - Starting backtest...
2025-06-30 18:59:28,970 - __main__ - INFO - Starting backtest...
2025-06-30 19:00:59,160 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 19:06:01,744 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 19:06:01,755 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 19:06:01,761 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 19:06:01,767 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:06:01,812 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 19:06:01,819 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 19:06:01,822 - __main__ - INFO - Found 4 strategies
2025-06-30 19:06:01,829 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 19:06:11,759 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-06-30 19:06:12,308 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-06-30 19:06:12,324 - __main__ - INFO - Successfully loaded 6617 bars
2025-06-30 19:06:30,438 - __main__ - INFO - Starting optimization...
2025-06-30 19:06:30,448 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:06:30,448 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:06:30,449 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:06:30,449 - gui.strategy_manager - INFO - Minimum bars: train=827, test=330
2025-06-30 19:06:30,450 - gui.strategy_manager - INFO - Window 1: Train 827 bars (1999-03-10 00:00:00 to 2002-06-24 00:00:00), Test 330 bars (2002-06-25 00:00:00 to 2003-10-14 00:00:00)
2025-06-30 19:06:30,451 - gui.strategy_manager - INFO - Window 2: Train 827 bars (2003-10-15 00:00:00 to 2007-01-29 00:00:00), Test 330 bars (2007-01-30 00:00:00 to 2008-05-20 00:00:00)
2025-06-30 19:06:30,451 - gui.strategy_manager - INFO - Window 3: Train 827 bars (2008-05-21 00:00:00 to 2011-08-30 00:00:00), Test 330 bars (2011-08-31 00:00:00 to 2012-12-21 00:00:00)
2025-06-30 19:06:30,452 - gui.strategy_manager - INFO - Window 4: Train 827 bars (2012-12-24 00:00:00 to 2016-04-07 00:00:00), Test 330 bars (2016-04-08 00:00:00 to 2017-07-28 00:00:00)
2025-06-30 19:06:30,453 - gui.strategy_manager - INFO - Window 5: Train 827 bars (2017-07-31 00:00:00 to 2020-11-09 00:00:00), Test 330 bars (2020-11-10 00:00:00 to 2022-03-03 00:00:00)
2025-06-30 19:06:30,453 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:07:01,014 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 20.0520
2025-06-30 19:07:01,015 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 20.0520
2025-06-30 19:07:01,027 - __main__ - INFO - Optimization completed. Best performance: 20.0520
2025-06-30 19:07:01,034 - __main__ - INFO - Optimized parameters: {'LE': 4, 'SE': 5, 'LX': 1, 'SX': 1}
2025-06-30 19:07:01,042 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:07:01,150 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 4.0, 'SE': 5.0, 'LX': 1.0, 'SX': 1.0}
2025-06-30 19:07:01,158 - __main__ - INFO - Updated parameters: {'LE': 4.0, 'SE': 5.0, 'LX': 1.0, 'SX': 1.0}
2025-06-30 19:07:01,258 - gui.metrics_calculator - INFO - Calculated metrics for 2102 trades
2025-06-30 19:07:01,466 - __main__ - INFO - Calculated returns for 316 months from 1999-03 to 2025-06
2025-06-30 19:07:01,508 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:07:10,903 - __main__ - INFO - Calculated returns for 316 months from 1999-03 to 2025-06
2025-06-30 19:07:36,047 - __main__ - INFO - Starting optimization...
2025-06-30 19:07:36,056 - __main__ - INFO - Starting optimization with metric: recovery_factor
2025-06-30 19:07:36,057 - gui.strategy_manager - INFO - Optimization will use metric: recovery_factor
2025-06-30 19:07:36,058 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:07:36,059 - gui.strategy_manager - INFO - Minimum bars: train=827, test=330
2025-06-30 19:07:36,059 - gui.strategy_manager - INFO - Window 1: Train 827 bars (1999-03-10 00:00:00 to 2002-06-24 00:00:00), Test 330 bars (2002-06-25 00:00:00 to 2003-10-14 00:00:00)
2025-06-30 19:07:36,060 - gui.strategy_manager - INFO - Window 2: Train 827 bars (2003-10-15 00:00:00 to 2007-01-29 00:00:00), Test 330 bars (2007-01-30 00:00:00 to 2008-05-20 00:00:00)
2025-06-30 19:07:36,061 - gui.strategy_manager - INFO - Window 3: Train 827 bars (2008-05-21 00:00:00 to 2011-08-30 00:00:00), Test 330 bars (2011-08-31 00:00:00 to 2012-12-21 00:00:00)
2025-06-30 19:07:36,061 - gui.strategy_manager - INFO - Window 4: Train 827 bars (2012-12-24 00:00:00 to 2016-04-07 00:00:00), Test 330 bars (2016-04-08 00:00:00 to 2017-07-28 00:00:00)
2025-06-30 19:07:36,062 - gui.strategy_manager - INFO - Window 5: Train 827 bars (2017-07-31 00:00:00 to 2020-11-09 00:00:00), Test 330 bars (2020-11-10 00:00:00 to 2022-03-03 00:00:00)
2025-06-30 19:07:36,062 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:08:05,030 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 2.4367
2025-06-30 19:08:05,031 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 2.4367
2025-06-30 19:08:05,042 - __main__ - INFO - Optimization completed. Best performance: 2.4367
2025-06-30 19:08:05,050 - __main__ - INFO - Optimized parameters: {'LE': 3, 'SE': 2, 'LX': 4, 'SX': 2}
2025-06-30 19:08:05,057 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:08:05,168 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 3.0, 'SE': 2.0, 'LX': 4.0, 'SX': 2.0}
2025-06-30 19:08:05,176 - __main__ - INFO - Updated parameters: {'LE': 3.0, 'SE': 2.0, 'LX': 4.0, 'SX': 2.0}
2025-06-30 19:08:05,242 - gui.metrics_calculator - INFO - Calculated metrics for 1395 trades
2025-06-30 19:08:05,376 - __main__ - INFO - Calculated returns for 316 months from 1999-03 to 2025-06
2025-06-30 19:08:05,418 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:08:30,217 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-06-30 19:08:30,225 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-06-30 19:08:42,487 - __main__ - INFO - Starting optimization...
2025-06-30 19:08:42,506 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:08:42,514 - __main__ - INFO - Starting optimization with metric: recovery_factor
2025-06-30 19:08:42,514 - gui.strategy_manager - INFO - Optimization will use metric: recovery_factor
2025-06-30 19:08:42,514 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:08:42,515 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:08:42,516 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:08:42,516 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:08:42,517 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:08:42,517 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:08:42,518 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:08:42,518 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:09:11,316 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 4.0833
2025-06-30 19:09:11,317 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 4.0833
2025-06-30 19:09:11,330 - __main__ - INFO - Optimization completed. Best performance: 4.0833
2025-06-30 19:09:11,338 - __main__ - INFO - Optimized parameters: {'LE': 4, 'SE': 1, 'LX': 1, 'SX': 1}
2025-06-30 19:09:11,344 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:09:11,448 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 4.0, 'SE': 1.0, 'LX': 1.0, 'SX': 1.0}
2025-06-30 19:09:11,456 - __main__ - INFO - Updated parameters: {'LE': 4.0, 'SE': 1.0, 'LX': 1.0, 'SX': 1.0}
2025-06-30 19:09:11,473 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:09:11,500 - gui.metrics_calculator - INFO - Calculated metrics for 546 trades
2025-06-30 19:09:11,519 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:09:11,545 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:09:11,564 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:09:11,583 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:09:27,433 - __main__ - INFO - Starting optimization...
2025-06-30 19:09:27,534 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:09:27,542 - __main__ - INFO - Starting optimization with metric: profit_factor
2025-06-30 19:09:27,552 - gui.strategy_manager - INFO - Optimization will use metric: profit_factor
2025-06-30 19:09:27,553 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:09:27,608 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:09:27,608 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:09:27,632 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:09:27,656 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:09:27,657 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:09:27,692 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:09:27,728 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:09:56,010 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 6.5706
2025-06-30 19:09:56,011 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 6.5706
2025-06-30 19:09:56,023 - __main__ - INFO - Optimization completed. Best performance: 6.5706
2025-06-30 19:09:56,030 - __main__ - INFO - Optimized parameters: {'LE': 5, 'SE': 2, 'LX': 5, 'SX': 4}
2025-06-30 19:09:56,037 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:09:56,146 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:09:56,154 - __main__ - INFO - Updated parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:09:56,170 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:09:56,186 - gui.metrics_calculator - INFO - Calculated metrics for 291 trades
2025-06-30 19:09:56,205 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:09:56,227 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:09:56,241 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:09:56,261 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:10:06,584 - __main__ - INFO - Starting optimization...
2025-06-30 19:10:06,602 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:10:06,610 - __main__ - INFO - Starting optimization with metric: sharpe_ratio
2025-06-30 19:10:06,611 - gui.strategy_manager - INFO - Optimization will use metric: sharpe_ratio
2025-06-30 19:10:06,611 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:10:06,612 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:10:06,612 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:10:06,613 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:10:06,613 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:10:06,614 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:10:06,614 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:10:06,615 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:10:35,008 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 0.6006
2025-06-30 19:10:35,009 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 0.6006
2025-06-30 19:10:35,020 - __main__ - INFO - Optimization completed. Best performance: 0.6006
2025-06-30 19:10:35,028 - __main__ - INFO - Optimized parameters: {'LE': 5, 'SE': 2, 'LX': 5, 'SX': 4}
2025-06-30 19:10:35,035 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:10:35,142 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:10:35,150 - __main__ - INFO - Updated parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:10:35,166 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:10:35,183 - gui.metrics_calculator - INFO - Calculated metrics for 291 trades
2025-06-30 19:10:35,202 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:10:35,224 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:10:35,238 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:10:35,258 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:10:48,361 - __main__ - INFO - Starting optimization...
2025-06-30 19:10:48,380 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:10:48,387 - __main__ - INFO - Starting optimization with metric: calmar_ratio
2025-06-30 19:10:48,387 - gui.strategy_manager - INFO - Optimization will use metric: calmar_ratio
2025-06-30 19:10:48,388 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:10:48,388 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:10:48,389 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:10:48,389 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:10:48,390 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:10:48,390 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:10:48,391 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:10:48,391 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:11:18,446 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 7.3420
2025-06-30 19:11:18,447 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 7.3420
2025-06-30 19:11:18,455 - __main__ - INFO - Optimization completed. Best performance: 7.3420
2025-06-30 19:11:18,463 - __main__ - INFO - Optimized parameters: {'LE': 5, 'SE': 2, 'LX': 5, 'SX': 4}
2025-06-30 19:11:18,469 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:11:18,570 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:11:18,578 - __main__ - INFO - Updated parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:11:18,594 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:11:18,612 - gui.metrics_calculator - INFO - Calculated metrics for 291 trades
2025-06-30 19:11:18,631 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:11:18,655 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:11:18,669 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:11:18,688 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:11:31,809 - __main__ - INFO - Starting optimization...
2025-06-30 19:11:31,828 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:11:31,835 - __main__ - INFO - Starting optimization with metric: sortino_ratio
2025-06-30 19:11:31,836 - gui.strategy_manager - INFO - Optimization will use metric: sortino_ratio
2025-06-30 19:11:31,836 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:11:31,836 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:11:31,837 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:11:31,837 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:11:31,838 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:11:31,839 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:11:31,841 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:11:31,842 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:12:00,233 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 1.3977
2025-06-30 19:12:00,234 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 1.3977
2025-06-30 19:12:00,245 - __main__ - INFO - Optimization completed. Best performance: 1.3977
2025-06-30 19:12:00,253 - __main__ - INFO - Optimized parameters: {'LE': 5, 'SE': 2, 'LX': 5, 'SX': 4}
2025-06-30 19:12:00,260 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:12:00,364 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:12:00,372 - __main__ - INFO - Updated parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:12:00,388 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:12:00,404 - gui.metrics_calculator - INFO - Calculated metrics for 291 trades
2025-06-30 19:12:00,423 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:12:00,445 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:12:00,459 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:12:00,479 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:12:07,810 - __main__ - INFO - Starting optimization...
2025-06-30 19:12:07,829 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:12:07,837 - __main__ - INFO - Starting optimization with metric: sortino_ratio
2025-06-30 19:12:07,837 - gui.strategy_manager - INFO - Optimization will use metric: sortino_ratio
2025-06-30 19:12:07,838 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:12:07,838 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:12:07,839 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:12:07,839 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:12:07,840 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:12:07,841 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:12:07,842 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:12:07,842 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:12:14,458 - __main__ - INFO - Starting optimization...
2025-06-30 19:12:14,480 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:12:14,504 - __main__ - INFO - Starting optimization with metric: ddr_ratio
2025-06-30 19:12:14,506 - gui.strategy_manager - INFO - Optimization will use metric: ddr_ratio
2025-06-30 19:12:14,509 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:12:14,513 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:12:14,516 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:12:14,520 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:12:14,522 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:12:14,524 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:12:14,527 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:12:14,528 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:13:01,189 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 17.2253
2025-06-30 19:13:01,191 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 17.2253
2025-06-30 19:13:01,209 - __main__ - INFO - Optimization completed. Best performance: 17.2253
2025-06-30 19:13:01,218 - __main__ - INFO - Optimized parameters: {'LE': 9, 'SE': 1, 'LX': 10, 'SX': 7}
2025-06-30 19:13:01,225 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:13:01,433 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 9.0, 'SE': 1.0, 'LX': 10.0, 'SX': 7.0}
2025-06-30 19:13:01,443 - __main__ - INFO - Updated parameters: {'LE': 9.0, 'SE': 1.0, 'LX': 10.0, 'SX': 7.0}
2025-06-30 19:13:01,482 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:01,506 - gui.metrics_calculator - INFO - Calculated metrics for 420 trades
2025-06-30 19:13:01,540 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:01,591 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:01,608 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:13:01,683 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:13:07,117 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 75.2261
2025-06-30 19:13:07,118 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 75.2261
2025-06-30 19:13:07,168 - __main__ - INFO - Optimization completed. Best performance: 75.2261
2025-06-30 19:13:07,175 - __main__ - INFO - Optimized parameters: {'LE': 5, 'SE': 2, 'LX': 5, 'SX': 4}
2025-06-30 19:13:07,182 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:13:07,282 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:13:07,290 - __main__ - INFO - Updated parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:13:07,306 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:07,322 - gui.metrics_calculator - INFO - Calculated metrics for 291 trades
2025-06-30 19:13:07,341 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:07,364 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:07,377 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:13:07,397 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:13:24,234 - __main__ - INFO - Starting optimization...
2025-06-30 19:13:24,254 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:24,261 - __main__ - INFO - Starting optimization with metric: win_rate
2025-06-30 19:13:24,261 - gui.strategy_manager - INFO - Optimization will use metric: win_rate
2025-06-30 19:13:24,262 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:13:24,262 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:13:24,263 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:13:24,263 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:13:24,264 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:13:24,265 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:13:24,265 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:13:24,266 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:13:53,984 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 62.1667
2025-06-30 19:13:53,984 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 62.1667
2025-06-30 19:13:53,996 - __main__ - INFO - Optimization completed. Best performance: 62.1667
2025-06-30 19:13:54,004 - __main__ - INFO - Optimized parameters: {'LE': 5, 'SE': 2, 'LX': 5, 'SX': 4}
2025-06-30 19:13:54,010 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:13:54,111 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:13:54,120 - __main__ - INFO - Updated parameters: {'LE': 5.0, 'SE': 2.0, 'LX': 5.0, 'SX': 4.0}
2025-06-30 19:13:54,136 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:54,154 - gui.metrics_calculator - INFO - Calculated metrics for 291 trades
2025-06-30 19:13:54,173 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:54,195 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:13:54,210 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:13:54,229 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:14:14,179 - __main__ - INFO - Starting optimization...
2025-06-30 19:14:14,197 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:14:14,204 - __main__ - INFO - Starting optimization with metric: payoff_ratio
2025-06-30 19:14:14,206 - gui.strategy_manager - INFO - Optimization will use metric: payoff_ratio
2025-06-30 19:14:14,207 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:14:14,207 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:14:14,208 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:14:14,209 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:14:14,209 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:14:14,210 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:14:14,210 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:14:14,211 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:14:41,472 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 1.1353
2025-06-30 19:14:41,472 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 1.1353
2025-06-30 19:14:41,475 - __main__ - INFO - Optimization completed. Best performance: 1.1353
2025-06-30 19:14:41,477 - __main__ - INFO - Optimized parameters: {'LE': 10, 'SE': 1, 'LX': 8, 'SX': 1}
2025-06-30 19:14:41,479 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:14:41,591 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 10.0, 'SE': 1.0, 'LX': 8.0, 'SX': 1.0}
2025-06-30 19:14:41,594 - __main__ - INFO - Updated parameters: {'LE': 10.0, 'SE': 1.0, 'LX': 8.0, 'SX': 1.0}
2025-06-30 19:14:41,604 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:14:41,624 - gui.metrics_calculator - INFO - Calculated metrics for 374 trades
2025-06-30 19:14:41,640 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:14:41,662 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:14:41,671 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:14:41,685 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:15:14,036 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:15:14,038 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:15:16,593 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:15:24,545 - __main__ - INFO - Starting optimization...
2025-06-30 19:15:24,558 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:15:24,561 - __main__ - INFO - Starting optimization with metric: payoff_ratio
2025-06-30 19:15:24,561 - gui.strategy_manager - INFO - Optimization will use metric: payoff_ratio
2025-06-30 19:15:24,562 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:15:24,562 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:15:24,563 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:15:24,563 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:15:24,565 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:15:24,565 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:15:24,566 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:15:24,567 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:15:51,808 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 1.4646
2025-06-30 19:15:51,809 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 1.4646
2025-06-30 19:15:51,816 - __main__ - INFO - Optimization completed. Best performance: 1.4646
2025-06-30 19:15:51,818 - __main__ - INFO - Optimized parameters: {'LE': 30, 'SE': 19, 'LX': 30, 'SX': 1}
2025-06-30 19:15:51,820 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:15:51,927 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 30.0, 'SE': 19.0, 'LX': 30.0, 'SX': 1.0}
2025-06-30 19:15:51,930 - __main__ - INFO - Updated parameters: {'LE': 30.0, 'SE': 19.0, 'LX': 30.0, 'SX': 1.0}
2025-06-30 19:15:51,942 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:15:51,963 - gui.metrics_calculator - INFO - Calculated metrics for 354 trades
2025-06-30 19:15:51,976 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:15:51,995 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:15:52,005 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:15:52,019 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:16:09,105 - __main__ - INFO - Starting optimization...
2025-06-30 19:16:09,119 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:16:09,123 - __main__ - INFO - Starting optimization with metric: win_rate
2025-06-30 19:16:09,123 - gui.strategy_manager - INFO - Optimization will use metric: win_rate
2025-06-30 19:16:09,123 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:16:09,124 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:16:09,124 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:16:09,125 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:16:09,125 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:16:09,126 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:16:09,127 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:16:09,127 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:16:39,925 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 72.6970
2025-06-30 19:16:39,925 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 72.6970
2025-06-30 19:16:39,932 - __main__ - INFO - Optimization completed. Best performance: 72.6970
2025-06-30 19:16:39,934 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 28, 'LX': 19, 'SX': 30}
2025-06-30 19:16:39,936 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:16:40,041 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 28.0, 'LX': 19.0, 'SX': 30.0}
2025-06-30 19:16:40,043 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 28.0, 'LX': 19.0, 'SX': 30.0}
2025-06-30 19:16:40,054 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:16:40,064 - gui.metrics_calculator - INFO - Calculated metrics for 115 trades
2025-06-30 19:16:40,078 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:16:40,093 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:16:40,098 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:16:40,112 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:16:56,778 - __main__ - INFO - Starting optimization...
2025-06-30 19:16:56,791 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:16:56,794 - __main__ - INFO - Starting optimization with metric: recovery_factor
2025-06-30 19:16:56,794 - gui.strategy_manager - INFO - Optimization will use metric: recovery_factor
2025-06-30 19:16:56,795 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:16:56,795 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:16:56,796 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:16:56,800 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:16:56,801 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:16:56,801 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:16:56,802 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:16:56,803 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:17:26,154 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 2.3540
2025-06-30 19:17:26,154 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 2.3540
2025-06-30 19:17:26,161 - __main__ - INFO - Optimization completed. Best performance: 2.3540
2025-06-30 19:17:26,163 - __main__ - INFO - Optimized parameters: {'LE': 14, 'SE': 9, 'LX': 14, 'SX': 8}
2025-06-30 19:17:26,166 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:17:26,270 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 14.0, 'SE': 9.0, 'LX': 14.0, 'SX': 8.0}
2025-06-30 19:17:26,272 - __main__ - INFO - Updated parameters: {'LE': 14.0, 'SE': 9.0, 'LX': 14.0, 'SX': 8.0}
2025-06-30 19:17:26,283 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:17:26,295 - gui.metrics_calculator - INFO - Calculated metrics for 158 trades
2025-06-30 19:17:26,308 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:17:26,324 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:17:26,329 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:17:26,344 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:17:36,226 - __main__ - INFO - Starting optimization...
2025-06-30 19:17:36,239 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:17:36,242 - __main__ - INFO - Starting optimization with metric: ddr_ratio
2025-06-30 19:17:36,243 - gui.strategy_manager - INFO - Optimization will use metric: ddr_ratio
2025-06-30 19:17:36,244 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:17:36,245 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:17:36,246 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:17:36,247 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:17:36,248 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:17:36,248 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:17:36,249 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:17:36,249 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:18:05,927 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 22.5916
2025-06-30 19:18:05,928 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 22.5916
2025-06-30 19:18:05,935 - __main__ - INFO - Optimization completed. Best performance: 22.5916
2025-06-30 19:18:05,937 - __main__ - INFO - Optimized parameters: {'LE': 11, 'SE': 26, 'LX': 24, 'SX': 25}
2025-06-30 19:18:05,939 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:18:06,048 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 11.0, 'SE': 26.0, 'LX': 24.0, 'SX': 25.0}
2025-06-30 19:18:06,050 - __main__ - INFO - Updated parameters: {'LE': 11.0, 'SE': 26.0, 'LX': 24.0, 'SX': 25.0}
2025-06-30 19:18:06,063 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:18:06,070 - gui.metrics_calculator - INFO - Calculated metrics for 88 trades
2025-06-30 19:18:06,084 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:18:06,099 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:18:06,103 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:18:06,117 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:18:17,291 - __main__ - INFO - Starting optimization...
2025-06-30 19:18:17,304 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:18:17,306 - __main__ - INFO - Starting optimization with metric: sortino_ratio
2025-06-30 19:18:17,307 - gui.strategy_manager - INFO - Optimization will use metric: sortino_ratio
2025-06-30 19:18:17,307 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:18:17,308 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:18:17,308 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:18:17,309 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:18:17,312 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:18:17,313 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:18:17,314 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:18:17,314 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:18:48,107 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 0.3559
2025-06-30 19:18:48,108 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 0.3559
2025-06-30 19:18:48,115 - __main__ - INFO - Optimization completed. Best performance: 0.3559
2025-06-30 19:18:48,117 - __main__ - INFO - Optimized parameters: {'LE': 3, 'SE': 30, 'LX': 1, 'SX': 5}
2025-06-30 19:18:48,119 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:18:48,226 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 3.0, 'SE': 30.0, 'LX': 1.0, 'SX': 5.0}
2025-06-30 19:18:48,228 - __main__ - INFO - Updated parameters: {'LE': 3.0, 'SE': 30.0, 'LX': 1.0, 'SX': 5.0}
2025-06-30 19:18:48,239 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:18:48,258 - gui.metrics_calculator - INFO - Calculated metrics for 354 trades
2025-06-30 19:18:48,272 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:18:48,290 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:18:48,300 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:18:48,314 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:18:58,363 - __main__ - INFO - Starting optimization...
2025-06-30 19:18:58,376 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:18:58,380 - __main__ - INFO - Starting optimization with metric: calmar_ratio
2025-06-30 19:18:58,380 - gui.strategy_manager - INFO - Optimization will use metric: calmar_ratio
2025-06-30 19:18:58,381 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:18:58,383 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:18:58,384 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:18:58,385 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:18:58,386 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:18:58,386 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:18:58,387 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:18:58,387 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:19:26,777 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 4.2072
2025-06-30 19:19:26,778 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 4.2072
2025-06-30 19:19:26,785 - __main__ - INFO - Optimization completed. Best performance: 4.2072
2025-06-30 19:19:26,787 - __main__ - INFO - Optimized parameters: {'LE': 25, 'SE': 7, 'LX': 30, 'SX': 3}
2025-06-30 19:19:26,789 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:19:26,903 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 25.0, 'SE': 7.0, 'LX': 30.0, 'SX': 3.0}
2025-06-30 19:19:26,905 - __main__ - INFO - Updated parameters: {'LE': 25.0, 'SE': 7.0, 'LX': 30.0, 'SX': 3.0}
2025-06-30 19:19:26,916 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:19:26,931 - gui.metrics_calculator - INFO - Calculated metrics for 252 trades
2025-06-30 19:19:26,945 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:19:26,963 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:19:26,970 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:19:26,984 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:19:50,708 - __main__ - INFO - Starting optimization...
2025-06-30 19:19:50,721 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:19:50,723 - __main__ - INFO - Starting optimization with metric: sharpe_ratio
2025-06-30 19:19:50,724 - gui.strategy_manager - INFO - Optimization will use metric: sharpe_ratio
2025-06-30 19:19:50,725 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:19:50,731 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:19:50,731 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:19:50,732 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:19:50,732 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:19:50,733 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:19:50,733 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:19:50,734 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:20:20,147 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 0.2383
2025-06-30 19:20:20,148 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 0.2383
2025-06-30 19:20:20,155 - __main__ - INFO - Optimization completed. Best performance: 0.2383
2025-06-30 19:20:20,157 - __main__ - INFO - Optimized parameters: {'LE': 30, 'SE': 11, 'LX': 25, 'SX': 30}
2025-06-30 19:20:20,159 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:20:20,268 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 30.0, 'SE': 11.0, 'LX': 25.0, 'SX': 30.0}
2025-06-30 19:20:20,270 - __main__ - INFO - Updated parameters: {'LE': 30.0, 'SE': 11.0, 'LX': 25.0, 'SX': 30.0}
2025-06-30 19:20:20,281 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:20:20,290 - gui.metrics_calculator - INFO - Calculated metrics for 99 trades
2025-06-30 19:20:20,304 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:20:20,318 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:20:20,323 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:20:20,336 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:20:33,604 - __main__ - INFO - Starting optimization...
2025-06-30 19:20:33,617 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:20:33,620 - __main__ - INFO - Starting optimization with metric: profit_factor
2025-06-30 19:20:33,621 - gui.strategy_manager - INFO - Optimization will use metric: profit_factor
2025-06-30 19:20:33,622 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:20:33,624 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:20:33,625 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:20:33,626 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:20:33,626 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:20:33,627 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:20:33,627 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:20:33,628 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:21:03,318 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 4.3017
2025-06-30 19:21:03,319 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 4.3017
2025-06-30 19:21:03,326 - __main__ - INFO - Optimization completed. Best performance: 4.3017
2025-06-30 19:21:03,328 - __main__ - INFO - Optimized parameters: {'LE': 13, 'SE': 2, 'LX': 13, 'SX': 11}
2025-06-30 19:21:03,330 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:21:03,434 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 13.0, 'SE': 2.0, 'LX': 13.0, 'SX': 11.0}
2025-06-30 19:21:03,436 - __main__ - INFO - Updated parameters: {'LE': 13.0, 'SE': 2.0, 'LX': 13.0, 'SX': 11.0}
2025-06-30 19:21:03,447 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:21:03,459 - gui.metrics_calculator - INFO - Calculated metrics for 141 trades
2025-06-30 19:21:03,473 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:21:03,488 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:21:03,493 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:21:03,508 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:21:12,620 - __main__ - INFO - Starting optimization...
2025-06-30 19:21:12,633 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:21:12,637 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:21:12,637 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:21:12,638 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:21:12,638 - gui.strategy_manager - INFO - Minimum bars: train=219, test=87
2025-06-30 19:21:12,639 - gui.strategy_manager - INFO - Window 1: Train 247 bars (2018-06-28 00:00:00 to 2019-06-21 00:00:00), Test 87 bars (2019-06-24 00:00:00 to 2019-10-24 00:00:00)
2025-06-30 19:21:12,640 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2019-10-25 00:00:00 to 2020-10-19 00:00:00), Test 87 bars (2020-10-20 00:00:00 to 2021-02-24 00:00:00)
2025-06-30 19:21:12,640 - gui.strategy_manager - INFO - Window 3: Train 250 bars (2021-02-25 00:00:00 to 2022-02-18 00:00:00), Test 87 bars (2022-02-22 00:00:00 to 2022-06-27 00:00:00)
2025-06-30 19:21:12,641 - gui.strategy_manager - INFO - Window 4: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 87 bars (2023-06-26 00:00:00 to 2023-10-26 00:00:00)
2025-06-30 19:21:12,641 - gui.strategy_manager - INFO - Window 5: Train 247 bars (2023-10-27 00:00:00 to 2024-10-21 00:00:00), Test 87 bars (2024-10-22 00:00:00 to 2025-02-27 00:00:00)
2025-06-30 19:21:12,642 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:21:42,379 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 18.9636
2025-06-30 19:21:42,379 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 18.9636
2025-06-30 19:21:42,386 - __main__ - INFO - Optimization completed. Best performance: 18.9636
2025-06-30 19:21:42,388 - __main__ - INFO - Optimized parameters: {'LE': 14, 'SE': 1, 'LX': 30, 'SX': 1}
2025-06-30 19:21:42,390 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:21:42,491 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 14.0, 'SE': 1.0, 'LX': 30.0, 'SX': 1.0}
2025-06-30 19:21:42,493 - __main__ - INFO - Updated parameters: {'LE': 14.0, 'SE': 1.0, 'LX': 30.0, 'SX': 1.0}
2025-06-30 19:21:42,504 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:21:42,523 - gui.metrics_calculator - INFO - Calculated metrics for 353 trades
2025-06-30 19:21:42,537 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:21:42,556 - __main__ - INFO - Using date range 2018-06-28 to 2025-06-27: 1759 bars
2025-06-30 19:21:42,566 - __main__ - INFO - Calculated returns for 85 months from 2018-06 to 2025-06
2025-06-30 19:21:42,580 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:23:32,097 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 19:26:11,937 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 19:26:11,946 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 19:26:11,989 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 19:26:12,007 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:26:12,065 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 19:26:12,076 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 19:26:12,081 - __main__ - INFO - Found 4 strategies
2025-06-30 19:26:12,089 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 19:26:28,359 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-06-30 19:26:29,213 - gui.data_manager - INFO - Successfully loaded 9499 bars from C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-06-30 19:26:29,234 - __main__ - INFO - Successfully loaded 9499 bars
2025-06-30 19:26:34,740 - __main__ - INFO - Timeframe changed to: Last 10 Years
2025-06-30 19:26:34,748 - __main__ - INFO - Applied timeframe 'Last 10 Years': 2015-06-30 to 2025-06-27
2025-06-30 19:26:37,938 - __main__ - INFO - Starting optimization...
2025-06-30 19:26:37,962 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:26:37,970 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:26:37,970 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:26:39,924 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 2459 bars
2025-06-30 19:26:58,433 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-06-30 19:26:58,434 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 22931.8770
2025-06-30 19:26:58,448 - __main__ - INFO - Optimization completed. Best performance: 22931.8770
2025-06-30 19:26:58,455 - __main__ - INFO - Optimized parameters: {'LE': 2, 'SE': 19, 'LX': 22, 'SX': 8}
2025-06-30 19:26:58,462 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:26:58,577 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 2.0, 'SE': 19.0, 'LX': 22.0, 'SX': 8.0}
2025-06-30 19:26:58,586 - __main__ - INFO - Updated parameters: {'LE': 2.0, 'SE': 19.0, 'LX': 22.0, 'SX': 8.0}
2025-06-30 19:26:58,606 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:26:58,629 - gui.metrics_calculator - INFO - Calculated metrics for 322 trades
2025-06-30 19:26:58,654 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:26:58,682 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:26:58,700 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:26:58,723 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:27:05,808 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:27:05,839 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:27:05,857 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:27:50,468 - __main__ - INFO - Loading strategy: UpDownATR
2025-06-30 19:27:50,469 - gui.strategy_manager - INFO - Successfully loaded strategy: UpDownATR
2025-06-30 19:27:50,524 - __main__ - INFO - Loaded periods: 12 months train, 6 months test
2025-06-30 19:27:50,530 - __main__ - INFO - Strategy 'UpDownATR' loaded successfully
2025-06-30 19:28:07,452 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 19:28:07,453 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:28:07,494 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 19:28:07,501 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 19:28:14,164 - __main__ - INFO - Loading strategy: UpDownATR
2025-06-30 19:28:14,165 - gui.strategy_manager - INFO - Successfully loaded strategy: UpDownATR
2025-06-30 19:28:14,222 - __main__ - INFO - Loaded periods: 12 months train, 6 months test
2025-06-30 19:28:14,228 - __main__ - INFO - Strategy 'UpDownATR' loaded successfully
2025-06-30 19:28:24,548 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 19:28:24,549 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:28:24,592 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 19:28:24,598 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 19:29:33,880 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:29:33,884 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:30:31,986 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:30:31,990 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:30:35,889 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:30:43,340 - __main__ - INFO - Starting optimization...
2025-06-30 19:30:43,361 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:30:43,369 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:30:43,370 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:30:43,375 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 2459 bars
2025-06-30 19:31:09,914 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-06-30 19:31:09,915 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -1000000.0000
2025-06-30 19:31:09,927 - __main__ - INFO - Optimization completed. Best performance: -1000000.0000
2025-06-30 19:31:09,937 - __main__ - INFO - Optimized parameters: {'LE': 24, 'SE': 6, 'LX': 24, 'SX': 18, 'ADXPeriod': 6}
2025-06-30 19:31:09,943 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:31:10,046 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 24.0, 'SE': 6.0, 'LX': 24.0, 'SX': 18.0}
2025-06-30 19:31:10,054 - __main__ - INFO - Updated parameters: {'LE': 24.0, 'SE': 6.0, 'LX': 24.0, 'SX': 18.0}
2025-06-30 19:31:10,074 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:31:10,454 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:31:31,028 - __main__ - INFO - Found 4 strategies
2025-06-30 19:31:36,188 - __main__ - INFO - Found 4 strategies
2025-06-30 19:31:36,811 - __main__ - INFO - Found 4 strategies
2025-06-30 19:31:37,100 - __main__ - INFO - Found 4 strategies
2025-06-30 19:31:37,315 - __main__ - INFO - Found 4 strategies
2025-06-30 19:31:37,564 - __main__ - INFO - Found 4 strategies
2025-06-30 19:31:38,353 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:31:40,852 - __main__ - INFO - Found 4 strategies
2025-06-30 19:31:43,668 - __main__ - INFO - Starting optimization...
2025-06-30 19:31:43,690 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:31:43,698 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:31:43,698 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:31:43,703 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 2459 bars
2025-06-30 19:32:10,662 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-06-30 19:32:10,663 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -1000000.0000
2025-06-30 19:32:10,675 - __main__ - INFO - Optimization completed. Best performance: -1000000.0000
2025-06-30 19:32:10,685 - __main__ - INFO - Optimized parameters: {'LE': 24, 'SE': 6, 'LX': 24, 'SX': 18, 'ADXPeriod': 6}
2025-06-30 19:32:10,691 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:32:10,797 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 24.0, 'SE': 6.0, 'LX': 24.0, 'SX': 18.0}
2025-06-30 19:32:10,805 - __main__ - INFO - Updated parameters: {'LE': 24.0, 'SE': 6.0, 'LX': 24.0, 'SX': 18.0}
2025-06-30 19:32:10,824 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:32:11,199 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:32:21,707 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:32:21,712 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:32:23,786 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:32:26,556 - __main__ - INFO - Found 4 strategies
2025-06-30 19:32:28,278 - __main__ - INFO - Starting optimization...
2025-06-30 19:32:28,300 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:32:28,308 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:32:28,308 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:32:28,313 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 2459 bars
2025-06-30 19:32:50,397 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-06-30 19:32:50,398 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 19998.7822
2025-06-30 19:32:50,410 - __main__ - INFO - Optimization completed. Best performance: 19998.7822
2025-06-30 19:32:50,420 - __main__ - INFO - Optimized parameters: {'LE': 25, 'SE': 1, 'LX': 9, 'SX': 30, 'ADXPeriod': 8}
2025-06-30 19:32:50,428 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:32:50,540 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 25.0, 'SE': 1.0, 'LX': 9.0, 'SX': 30.0}
2025-06-30 19:32:50,548 - __main__ - INFO - Updated parameters: {'LE': 25.0, 'SE': 1.0, 'LX': 9.0, 'SX': 30.0}
2025-06-30 19:32:50,569 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:32:50,584 - gui.metrics_calculator - INFO - Calculated metrics for 318 trades
2025-06-30 19:32:50,608 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:32:50,635 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:32:50,652 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:32:50,675 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:34:32,637 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:34:32,642 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:34:35,069 - __main__ - INFO - Found 4 strategies
2025-06-30 19:34:36,941 - __main__ - INFO - Starting optimization...
2025-06-30 19:34:37,001 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:34:37,011 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:34:37,012 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:34:37,017 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 2459 bars
2025-06-30 19:35:03,961 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-06-30 19:35:03,962 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -1000000.0000
2025-06-30 19:35:03,974 - __main__ - INFO - Optimization completed. Best performance: -1000000.0000
2025-06-30 19:35:03,984 - __main__ - INFO - Optimized parameters: {'LE': 24, 'SE': 6, 'LX': 24, 'SX': 18, 'ADXPeriod': 6}
2025-06-30 19:35:03,991 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:35:04,092 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 24.0, 'SE': 6.0, 'LX': 24.0, 'SX': 18.0}
2025-06-30 19:35:04,101 - __main__ - INFO - Updated parameters: {'LE': 24.0, 'SE': 6.0, 'LX': 24.0, 'SX': 18.0}
2025-06-30 19:35:04,120 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:35:04,589 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:35:31,787 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:35:31,791 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:35:34,709 - __main__ - INFO - Found 4 strategies
2025-06-30 19:35:36,830 - __main__ - INFO - Starting optimization...
2025-06-30 19:35:36,852 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:35:36,861 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:35:36,862 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:35:36,867 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 2459 bars
2025-06-30 19:36:03,704 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-06-30 19:36:03,705 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -1000000.0000
2025-06-30 19:36:03,718 - __main__ - INFO - Optimization completed. Best performance: -1000000.0000
2025-06-30 19:36:03,728 - __main__ - INFO - Optimized parameters: {'LE': 24, 'SE': 6, 'LX': 24, 'SX': 18, 'ADXPeriod': 6}
2025-06-30 19:36:03,735 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:36:03,843 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 24.0, 'SE': 6.0, 'LX': 24.0, 'SX': 18.0}
2025-06-30 19:36:03,851 - __main__ - INFO - Updated parameters: {'LE': 24.0, 'SE': 6.0, 'LX': 24.0, 'SX': 18.0}
2025-06-30 19:36:03,872 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:36:04,318 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:36:31,603 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:36:31,607 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:36:35,729 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 19:41:43,348 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 19:41:43,414 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 19:41:43,454 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 19:41:43,465 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:41:43,523 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 19:41:43,529 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 19:41:43,532 - __main__ - INFO - Found 4 strategies
2025-06-30 19:41:43,540 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 19:41:47,945 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-06-30 19:41:48,829 - gui.data_manager - INFO - Successfully loaded 9499 bars from C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-06-30 19:41:48,851 - __main__ - INFO - Successfully loaded 9499 bars
2025-06-30 19:41:51,720 - __main__ - INFO - Found 4 strategies
2025-06-30 19:42:03,795 - __main__ - INFO - Timeframe changed to: Last 10 Years
2025-06-30 19:42:03,803 - __main__ - INFO - Applied timeframe 'Last 10 Years': 2015-06-30 to 2025-06-27
2025-06-30 19:42:10,009 - __main__ - INFO - Starting optimization...
2025-06-30 19:42:10,031 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:42:10,039 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:42:10,039 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:42:10,040 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:42:10,040 - gui.strategy_manager - INFO - Minimum bars: train=307, test=122
2025-06-30 19:42:10,041 - gui.strategy_manager - INFO - Window 1: Train 307 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 122 bars (2016-09-27 00:00:00 to 2017-03-24 00:00:00)
2025-06-30 19:42:10,041 - gui.strategy_manager - INFO - Window 2: Train 307 bars (2017-03-27 00:00:00 to 2018-06-26 00:00:00), Test 122 bars (2018-06-27 00:00:00 to 2018-12-19 00:00:00)
2025-06-30 19:42:10,042 - gui.strategy_manager - INFO - Window 3: Train 307 bars (2018-12-20 00:00:00 to 2020-03-20 00:00:00), Test 122 bars (2020-03-23 00:00:00 to 2020-09-16 00:00:00)
2025-06-30 19:42:10,042 - gui.strategy_manager - INFO - Window 4: Train 307 bars (2020-09-17 00:00:00 to 2021-12-15 00:00:00), Test 122 bars (2021-12-16 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 19:42:10,044 - gui.strategy_manager - INFO - Window 5: Train 307 bars (2022-06-20 00:00:00 to 2023-09-15 00:00:00), Test 122 bars (2023-09-18 00:00:00 to 2024-03-14 00:00:00)
2025-06-30 19:42:10,046 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:43:27,072 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 2396.1936
2025-06-30 19:43:27,073 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 2396.1936
2025-06-30 19:43:27,086 - __main__ - INFO - Optimization completed. Best performance: 2396.1936
2025-06-30 19:43:27,096 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 11, 'LX': 9, 'SX': 8, 'ADXPeriod': 18}
2025-06-30 19:43:27,103 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:43:27,206 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 11.0, 'LX': 9.0, 'SX': 8.0, 'ADXPeriod': 18.0}
2025-06-30 19:43:27,219 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 11.0, 'LX': 9.0, 'SX': 8.0, 'ADXPeriod': 18.0}
2025-06-30 19:43:27,238 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:43:55,150 - gui.metrics_calculator - INFO - Calculated metrics for 322 trades
2025-06-30 19:43:55,174 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:43:55,202 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:43:55,219 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:43:55,243 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:44:00,263 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:44:00,290 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:44:00,309 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:45:10,625 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:45:10,630 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:45:13,263 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:45:17,442 - __main__ - INFO - Found 4 strategies
2025-06-30 19:45:19,363 - __main__ - INFO - Starting optimization...
2025-06-30 19:45:19,386 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:45:19,395 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:45:19,398 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:45:19,399 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:45:19,399 - gui.strategy_manager - INFO - Minimum bars: train=307, test=122
2025-06-30 19:45:19,400 - gui.strategy_manager - INFO - Window 1: Train 307 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 122 bars (2016-09-27 00:00:00 to 2017-03-24 00:00:00)
2025-06-30 19:45:19,401 - gui.strategy_manager - INFO - Window 2: Train 307 bars (2017-03-27 00:00:00 to 2018-06-26 00:00:00), Test 122 bars (2018-06-27 00:00:00 to 2018-12-19 00:00:00)
2025-06-30 19:45:19,401 - gui.strategy_manager - INFO - Window 3: Train 307 bars (2018-12-20 00:00:00 to 2020-03-20 00:00:00), Test 122 bars (2020-03-23 00:00:00 to 2020-09-16 00:00:00)
2025-06-30 19:45:19,402 - gui.strategy_manager - INFO - Window 4: Train 307 bars (2020-09-17 00:00:00 to 2021-12-15 00:00:00), Test 122 bars (2021-12-16 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 19:45:19,403 - gui.strategy_manager - INFO - Window 5: Train 307 bars (2022-06-20 00:00:00 to 2023-09-15 00:00:00), Test 122 bars (2023-09-18 00:00:00 to 2024-03-14 00:00:00)
2025-06-30 19:45:19,404 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:46:34,403 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 1994.1877
2025-06-30 19:46:34,403 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 1994.1877
2025-06-30 19:46:34,417 - __main__ - INFO - Optimization completed. Best performance: 1994.1877
2025-06-30 19:46:34,427 - __main__ - INFO - Optimized parameters: {'LE': 30, 'SE': 30, 'LX': 9, 'SX': 30, 'ADXPeriod': 3}
2025-06-30 19:46:34,434 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:46:34,535 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 30.0, 'SE': 30.0, 'LX': 9.0, 'SX': 30.0, 'ADXPeriod': 3.0}
2025-06-30 19:46:34,547 - __main__ - INFO - Updated parameters: {'LE': 30.0, 'SE': 30.0, 'LX': 9.0, 'SX': 30.0, 'ADXPeriod': 3.0}
2025-06-30 19:46:34,568 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:47:03,187 - gui.metrics_calculator - INFO - Calculated metrics for 325 trades
2025-06-30 19:47:03,211 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:47:03,238 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:47:03,254 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:47:03,278 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:48:09,059 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:48:09,063 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:48:14,619 - __main__ - INFO - Found 4 strategies
2025-06-30 19:48:18,492 - __main__ - INFO - Starting optimization...
2025-06-30 19:48:18,515 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:48:18,523 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:48:18,523 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:48:18,523 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:48:18,524 - gui.strategy_manager - INFO - Minimum bars: train=307, test=122
2025-06-30 19:48:18,524 - gui.strategy_manager - INFO - Window 1: Train 307 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 122 bars (2016-09-27 00:00:00 to 2017-03-24 00:00:00)
2025-06-30 19:48:18,525 - gui.strategy_manager - INFO - Window 2: Train 307 bars (2017-03-27 00:00:00 to 2018-06-26 00:00:00), Test 122 bars (2018-06-27 00:00:00 to 2018-12-19 00:00:00)
2025-06-30 19:48:18,525 - gui.strategy_manager - INFO - Window 3: Train 307 bars (2018-12-20 00:00:00 to 2020-03-20 00:00:00), Test 122 bars (2020-03-23 00:00:00 to 2020-09-16 00:00:00)
2025-06-30 19:48:18,526 - gui.strategy_manager - INFO - Window 4: Train 307 bars (2020-09-17 00:00:00 to 2021-12-15 00:00:00), Test 122 bars (2021-12-16 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 19:48:18,526 - gui.strategy_manager - INFO - Window 5: Train 307 bars (2022-06-20 00:00:00 to 2023-09-15 00:00:00), Test 122 bars (2023-09-18 00:00:00 to 2024-03-14 00:00:00)
2025-06-30 19:48:18,527 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:49:25,030 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 1994.1877
2025-06-30 19:49:25,031 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 1994.1877
2025-06-30 19:49:25,043 - __main__ - INFO - Optimization completed. Best performance: 1994.1877
2025-06-30 19:49:25,053 - __main__ - INFO - Optimized parameters: {'LE': 30, 'SE': 30, 'LX': 9, 'SX': 30, 'ADXPeriod': 3}
2025-06-30 19:49:25,060 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:49:25,162 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 30.0, 'SE': 30.0, 'LX': 9.0, 'SX': 30.0, 'ADXPeriod': 3.0}
2025-06-30 19:49:25,173 - __main__ - INFO - Updated parameters: {'LE': 30.0, 'SE': 30.0, 'LX': 9.0, 'SX': 30.0, 'ADXPeriod': 3.0}
2025-06-30 19:49:25,191 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:49:52,725 - gui.metrics_calculator - INFO - Calculated metrics for 325 trades
2025-06-30 19:49:52,748 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:49:52,776 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:49:52,793 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:49:52,815 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:50:35,827 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:50:35,831 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:50:38,129 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:50:40,740 - __main__ - INFO - Found 4 strategies
2025-06-30 19:50:43,180 - __main__ - INFO - Starting optimization...
2025-06-30 19:50:43,203 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:50:43,212 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:50:43,213 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:50:43,213 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:50:43,214 - gui.strategy_manager - INFO - Minimum bars: train=307, test=122
2025-06-30 19:50:43,214 - gui.strategy_manager - INFO - Window 1: Train 307 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 122 bars (2016-09-27 00:00:00 to 2017-03-24 00:00:00)
2025-06-30 19:50:43,215 - gui.strategy_manager - INFO - Window 2: Train 307 bars (2017-03-27 00:00:00 to 2018-06-26 00:00:00), Test 122 bars (2018-06-27 00:00:00 to 2018-12-19 00:00:00)
2025-06-30 19:50:43,215 - gui.strategy_manager - INFO - Window 3: Train 307 bars (2018-12-20 00:00:00 to 2020-03-20 00:00:00), Test 122 bars (2020-03-23 00:00:00 to 2020-09-16 00:00:00)
2025-06-30 19:50:43,216 - gui.strategy_manager - INFO - Window 4: Train 307 bars (2020-09-17 00:00:00 to 2021-12-15 00:00:00), Test 122 bars (2021-12-16 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 19:50:43,220 - gui.strategy_manager - INFO - Window 5: Train 307 bars (2022-06-20 00:00:00 to 2023-09-15 00:00:00), Test 122 bars (2023-09-18 00:00:00 to 2024-03-14 00:00:00)
2025-06-30 19:50:43,221 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:51:54,864 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 2396.1936
2025-06-30 19:51:54,865 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 2396.1936
2025-06-30 19:51:54,877 - __main__ - INFO - Optimization completed. Best performance: 2396.1936
2025-06-30 19:51:54,887 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 11, 'LX': 9, 'SX': 8, 'ADXPeriod': 18}
2025-06-30 19:51:54,894 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:51:55,005 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 11.0, 'LX': 9.0, 'SX': 8.0, 'ADXPeriod': 18.0}
2025-06-30 19:51:55,015 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 11.0, 'LX': 9.0, 'SX': 8.0, 'ADXPeriod': 18.0}
2025-06-30 19:51:55,034 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:52:22,596 - gui.metrics_calculator - INFO - Calculated metrics for 322 trades
2025-06-30 19:52:22,619 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:52:22,648 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:52:22,665 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:52:22,689 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:53:59,249 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:53:59,254 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:54:01,379 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 19:54:02,877 - __main__ - INFO - Found 4 strategies
2025-06-30 19:54:05,222 - __main__ - INFO - Starting optimization...
2025-06-30 19:54:05,244 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:54:05,251 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:54:05,252 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:54:05,252 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:54:05,253 - gui.strategy_manager - INFO - Minimum bars: train=307, test=122
2025-06-30 19:54:05,254 - gui.strategy_manager - INFO - Window 1: Train 307 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 122 bars (2016-09-27 00:00:00 to 2017-03-24 00:00:00)
2025-06-30 19:54:05,254 - gui.strategy_manager - INFO - Window 2: Train 307 bars (2017-03-27 00:00:00 to 2018-06-26 00:00:00), Test 122 bars (2018-06-27 00:00:00 to 2018-12-19 00:00:00)
2025-06-30 19:54:05,255 - gui.strategy_manager - INFO - Window 3: Train 307 bars (2018-12-20 00:00:00 to 2020-03-20 00:00:00), Test 122 bars (2020-03-23 00:00:00 to 2020-09-16 00:00:00)
2025-06-30 19:54:05,256 - gui.strategy_manager - INFO - Window 4: Train 307 bars (2020-09-17 00:00:00 to 2021-12-15 00:00:00), Test 122 bars (2021-12-16 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 19:54:05,257 - gui.strategy_manager - INFO - Window 5: Train 307 bars (2022-06-20 00:00:00 to 2023-09-15 00:00:00), Test 122 bars (2023-09-18 00:00:00 to 2024-03-14 00:00:00)
2025-06-30 19:54:05,257 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:55:19,478 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 2396.1936
2025-06-30 19:55:19,479 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 2396.1936
2025-06-30 19:55:19,491 - __main__ - INFO - Optimization completed. Best performance: 2396.1936
2025-06-30 19:55:19,501 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 11, 'LX': 9, 'SX': 8, 'ADXPeriod': 18}
2025-06-30 19:55:19,507 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:55:19,608 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 11.0, 'LX': 9.0, 'SX': 8.0, 'ADXPeriod': 18.0}
2025-06-30 19:55:19,619 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 11.0, 'LX': 9.0, 'SX': 8.0, 'ADXPeriod': 18.0}
2025-06-30 19:55:19,638 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:55:47,309 - gui.metrics_calculator - INFO - Calculated metrics for 322 trades
2025-06-30 19:55:47,332 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:55:47,359 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:55:47,376 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:55:47,399 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:57:08,054 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:57:08,059 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:57:09,944 - __main__ - INFO - Found 4 strategies
2025-06-30 19:57:11,910 - __main__ - INFO - Starting optimization...
2025-06-30 19:57:11,932 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:57:11,940 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:57:11,941 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:57:11,941 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:57:11,942 - gui.strategy_manager - INFO - Minimum bars: train=307, test=122
2025-06-30 19:57:11,942 - gui.strategy_manager - INFO - Window 1: Train 307 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 122 bars (2016-09-27 00:00:00 to 2017-03-24 00:00:00)
2025-06-30 19:57:11,943 - gui.strategy_manager - INFO - Window 2: Train 307 bars (2017-03-27 00:00:00 to 2018-06-26 00:00:00), Test 122 bars (2018-06-27 00:00:00 to 2018-12-19 00:00:00)
2025-06-30 19:57:11,943 - gui.strategy_manager - INFO - Window 3: Train 307 bars (2018-12-20 00:00:00 to 2020-03-20 00:00:00), Test 122 bars (2020-03-23 00:00:00 to 2020-09-16 00:00:00)
2025-06-30 19:57:11,944 - gui.strategy_manager - INFO - Window 4: Train 307 bars (2020-09-17 00:00:00 to 2021-12-15 00:00:00), Test 122 bars (2021-12-16 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 19:57:11,944 - gui.strategy_manager - INFO - Window 5: Train 307 bars (2022-06-20 00:00:00 to 2023-09-15 00:00:00), Test 122 bars (2023-09-18 00:00:00 to 2024-03-14 00:00:00)
2025-06-30 19:57:11,945 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 19:58:22,771 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 3031.5697
2025-06-30 19:58:22,772 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 3031.5697
2025-06-30 19:58:22,785 - __main__ - INFO - Optimization completed. Best performance: 3031.5697
2025-06-30 19:58:22,795 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 10, 'LX': 9, 'SX': 11, 'ADXPeriod': 17}
2025-06-30 19:58:22,801 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 19:58:22,909 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 10.0, 'LX': 9.0, 'SX': 11.0, 'ADXPeriod': 17.0}
2025-06-30 19:58:22,919 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 10.0, 'LX': 9.0, 'SX': 11.0, 'ADXPeriod': 17.0}
2025-06-30 19:58:22,939 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:58:50,419 - gui.metrics_calculator - INFO - Calculated metrics for 287 trades
2025-06-30 19:58:50,442 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:58:50,468 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:58:50,483 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 19:58:50,508 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 19:59:32,224 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 19:59:32,228 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 19:59:34,559 - __main__ - INFO - Found 4 strategies
2025-06-30 19:59:36,241 - __main__ - INFO - Starting optimization...
2025-06-30 19:59:36,263 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 19:59:36,270 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 19:59:36,271 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 19:59:36,271 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 19:59:36,272 - gui.strategy_manager - INFO - Minimum bars: train=307, test=122
2025-06-30 19:59:36,272 - gui.strategy_manager - INFO - Window 1: Train 307 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 122 bars (2016-09-27 00:00:00 to 2017-03-24 00:00:00)
2025-06-30 19:59:36,273 - gui.strategy_manager - INFO - Window 2: Train 307 bars (2017-03-27 00:00:00 to 2018-06-26 00:00:00), Test 122 bars (2018-06-27 00:00:00 to 2018-12-19 00:00:00)
2025-06-30 19:59:36,273 - gui.strategy_manager - INFO - Window 3: Train 307 bars (2018-12-20 00:00:00 to 2020-03-20 00:00:00), Test 122 bars (2020-03-23 00:00:00 to 2020-09-16 00:00:00)
2025-06-30 19:59:36,274 - gui.strategy_manager - INFO - Window 4: Train 307 bars (2020-09-17 00:00:00 to 2021-12-15 00:00:00), Test 122 bars (2021-12-16 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 19:59:36,275 - gui.strategy_manager - INFO - Window 5: Train 307 bars (2022-06-20 00:00:00 to 2023-09-15 00:00:00), Test 122 bars (2023-09-18 00:00:00 to 2024-03-14 00:00:00)
2025-06-30 19:59:36,275 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 20:00:48,378 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 3719.9285
2025-06-30 20:00:48,379 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 3719.9285
2025-06-30 20:00:48,392 - __main__ - INFO - Optimization completed. Best performance: 3719.9285
2025-06-30 20:00:48,401 - __main__ - INFO - Optimized parameters: {'LE': 11, 'SE': 1, 'LX': 14, 'SX': 1, 'ADXPeriod': 10}
2025-06-30 20:00:48,408 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 20:00:48,515 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 11.0, 'SE': 1.0, 'LX': 14.0, 'SX': 1.0, 'ADXPeriod': 10.0}
2025-06-30 20:00:48,525 - __main__ - INFO - Updated parameters: {'LE': 11.0, 'SE': 1.0, 'LX': 14.0, 'SX': 1.0, 'ADXPeriod': 10.0}
2025-06-30 20:00:48,545 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:01:16,051 - gui.metrics_calculator - INFO - Calculated metrics for 457 trades
2025-06-30 20:01:16,074 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:01:16,102 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:01:16,123 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:01:16,146 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 20:01:42,108 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 20:01:42,113 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 20:01:44,507 - __main__ - INFO - Found 4 strategies
2025-06-30 20:01:46,593 - __main__ - INFO - Starting optimization...
2025-06-30 20:01:46,613 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:01:46,621 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:01:46,621 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:01:46,622 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 20:01:46,622 - gui.strategy_manager - INFO - Minimum bars: train=307, test=122
2025-06-30 20:01:46,623 - gui.strategy_manager - INFO - Window 1: Train 307 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 122 bars (2016-09-27 00:00:00 to 2017-03-24 00:00:00)
2025-06-30 20:01:46,623 - gui.strategy_manager - INFO - Window 2: Train 307 bars (2017-03-27 00:00:00 to 2018-06-26 00:00:00), Test 122 bars (2018-06-27 00:00:00 to 2018-12-19 00:00:00)
2025-06-30 20:01:46,624 - gui.strategy_manager - INFO - Window 3: Train 307 bars (2018-12-20 00:00:00 to 2020-03-20 00:00:00), Test 122 bars (2020-03-23 00:00:00 to 2020-09-16 00:00:00)
2025-06-30 20:01:46,624 - gui.strategy_manager - INFO - Window 4: Train 307 bars (2020-09-17 00:00:00 to 2021-12-15 00:00:00), Test 122 bars (2021-12-16 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 20:01:46,625 - gui.strategy_manager - INFO - Window 5: Train 307 bars (2022-06-20 00:00:00 to 2023-09-15 00:00:00), Test 122 bars (2023-09-18 00:00:00 to 2024-03-14 00:00:00)
2025-06-30 20:01:46,625 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 20:02:56,531 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 1890.9322
2025-06-30 20:02:56,532 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 1890.9322
2025-06-30 20:02:56,544 - __main__ - INFO - Optimization completed. Best performance: 1890.9322
2025-06-30 20:02:56,553 - __main__ - INFO - Optimized parameters: {'LE': 5, 'SE': 6, 'LX': 8, 'SX': 1, 'ADXPeriod': 30}
2025-06-30 20:02:56,560 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 20:02:56,667 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 5.0, 'SE': 6.0, 'LX': 8.0, 'SX': 1.0, 'ADXPeriod': 30.0}
2025-06-30 20:02:56,676 - __main__ - INFO - Updated parameters: {'LE': 5.0, 'SE': 6.0, 'LX': 8.0, 'SX': 1.0, 'ADXPeriod': 30.0}
2025-06-30 20:02:56,696 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:03:24,406 - gui.metrics_calculator - INFO - Calculated metrics for 555 trades
2025-06-30 20:03:24,429 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:03:24,459 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:03:24,483 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:03:24,506 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 20:04:03,447 - __main__ - INFO - Starting backtest...
2025-06-30 20:04:03,450 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 5.0, 'SE': 6.0, 'LX': 8.0, 'SX': 1.0, 'ADXPeriod': 30.0}
2025-06-30 20:04:03,452 - __main__ - INFO - Updated parameters: {'LE': 5.0, 'SE': 6.0, 'LX': 8.0, 'SX': 1.0, 'ADXPeriod': 30.0}
2025-06-30 20:04:03,466 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:04:30,868 - gui.metrics_calculator - INFO - Calculated metrics for 555 trades
2025-06-30 20:04:30,885 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:04:30,910 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:04:30,928 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:04:30,994 - __main__ - INFO - Backtest completed successfully
2025-06-30 20:07:16,944 - __main__ - INFO - Starting optimization...
2025-06-30 20:07:16,961 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:07:16,963 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:07:16,963 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:07:16,968 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 2459 bars
2025-06-30 20:09:00,480 - __main__ - INFO - Starting optimization...
2025-06-30 20:09:00,582 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2459 bars
2025-06-30 20:09:00,716 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:09:00,722 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:09:00,754 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 2459 bars
2025-06-30 20:13:17,301 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-06-30 20:13:18,272 - gui.data_manager - INFO - Successfully loaded 5184 bars from C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-06-30 20:13:18,498 - __main__ - INFO - Successfully loaded 5184 bars
2025-06-30 20:13:25,618 - __main__ - INFO - Starting optimization...
2025-06-30 20:13:25,774 - __main__ - INFO - Using date range 2004-11-18 to 2025-06-27: 5184 bars
2025-06-30 20:13:25,914 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:13:25,921 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:13:25,978 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 5184 bars
2025-06-30 20:13:45,348 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 20:13:50,492 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 20:13:50,504 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 20:13:50,509 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 20:13:50,515 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 20:13:50,571 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 20:13:50,578 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 20:13:50,582 - __main__ - INFO - Found 4 strategies
2025-06-30 20:13:50,589 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 20:13:58,049 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-06-30 20:13:58,490 - gui.data_manager - INFO - Successfully loaded 5184 bars from C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-06-30 20:13:58,508 - __main__ - INFO - Successfully loaded 5184 bars
2025-06-30 20:14:03,909 - __main__ - INFO - Found 4 strategies
2025-06-30 20:14:15,336 - __main__ - INFO - Timeframe changed to: Last 10 Years
2025-06-30 20:14:15,345 - __main__ - INFO - Applied timeframe 'Last 10 Years': 2015-06-30 to 2025-06-27
2025-06-30 20:14:18,261 - __main__ - INFO - Starting optimization...
2025-06-30 20:14:18,277 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:14:18,286 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:14:18,286 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:14:18,286 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 20:14:18,287 - gui.strategy_manager - INFO - Minimum bars: train=314, test=125
2025-06-30 20:14:18,287 - gui.strategy_manager - INFO - Window 1: Train 314 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 125 bars (2016-09-27 00:00:00 to 2017-03-27 00:00:00)
2025-06-30 20:14:18,288 - gui.strategy_manager - INFO - Window 2: Train 314 bars (2017-03-28 00:00:00 to 2018-06-25 00:00:00), Test 125 bars (2018-06-26 00:00:00 to 2018-12-21 00:00:00)
2025-06-30 20:14:18,288 - gui.strategy_manager - INFO - Window 3: Train 314 bars (2018-12-24 00:00:00 to 2020-03-24 00:00:00), Test 125 bars (2020-03-25 00:00:00 to 2020-09-21 00:00:00)
2025-06-30 20:14:18,288 - gui.strategy_manager - INFO - Window 4: Train 314 bars (2020-09-22 00:00:00 to 2021-12-17 00:00:00), Test 125 bars (2021-12-20 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 20:14:18,289 - gui.strategy_manager - INFO - Window 5: Train 314 bars (2022-06-21 00:00:00 to 2023-09-19 00:00:00), Test 125 bars (2023-09-20 00:00:00 to 2024-03-19 00:00:00)
2025-06-30 20:14:18,289 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 20:15:31,073 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 0.8640
2025-06-30 20:15:31,074 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 0.8640
2025-06-30 20:15:31,086 - __main__ - INFO - Optimization completed. Best performance: 0.8640
2025-06-30 20:15:31,096 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 20, 'LX': 20, 'SX': 1, 'ADXPeriod': 12}
2025-06-30 20:15:31,103 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 20:15:31,205 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 20.0, 'LX': 20.0, 'SX': 1.0, 'ADXPeriod': 12.0}
2025-06-30 20:15:31,215 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 20.0, 'LX': 20.0, 'SX': 1.0, 'ADXPeriod': 12.0}
2025-06-30 20:15:31,229 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:16:00,172 - gui.metrics_calculator - INFO - Calculated metrics for 604 trades
2025-06-30 20:16:00,192 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:16:00,219 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:16:00,243 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:16:00,266 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 20:16:38,517 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 20:16:38,521 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 20:16:42,182 - __main__ - INFO - Found 4 strategies
2025-06-30 20:16:44,518 - __main__ - INFO - Starting optimization...
2025-06-30 20:16:44,535 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:16:44,542 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:16:44,542 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:16:44,543 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 20:16:44,543 - gui.strategy_manager - INFO - Minimum bars: train=314, test=125
2025-06-30 20:16:44,543 - gui.strategy_manager - INFO - Window 1: Train 314 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 125 bars (2016-09-27 00:00:00 to 2017-03-27 00:00:00)
2025-06-30 20:16:44,544 - gui.strategy_manager - INFO - Window 2: Train 314 bars (2017-03-28 00:00:00 to 2018-06-25 00:00:00), Test 125 bars (2018-06-26 00:00:00 to 2018-12-21 00:00:00)
2025-06-30 20:16:44,544 - gui.strategy_manager - INFO - Window 3: Train 314 bars (2018-12-24 00:00:00 to 2020-03-24 00:00:00), Test 125 bars (2020-03-25 00:00:00 to 2020-09-21 00:00:00)
2025-06-30 20:16:44,544 - gui.strategy_manager - INFO - Window 4: Train 314 bars (2020-09-22 00:00:00 to 2021-12-17 00:00:00), Test 125 bars (2021-12-20 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 20:16:44,544 - gui.strategy_manager - INFO - Window 5: Train 314 bars (2022-06-21 00:00:00 to 2023-09-19 00:00:00), Test 125 bars (2023-09-20 00:00:00 to 2024-03-19 00:00:00)
2025-06-30 20:16:44,545 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 20:17:56,545 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: -0.9060
2025-06-30 20:17:56,546 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -0.9060
2025-06-30 20:17:56,558 - __main__ - INFO - Optimization completed. Best performance: -0.9060
2025-06-30 20:17:56,568 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 20, 'LX': 20, 'SX': 20, 'ADXPeriod': 2}
2025-06-30 20:17:56,574 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 20:17:56,679 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 20.0, 'LX': 20.0, 'SX': 20.0, 'ADXPeriod': 2.0}
2025-06-30 20:17:56,688 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 20.0, 'LX': 20.0, 'SX': 20.0, 'ADXPeriod': 2.0}
2025-06-30 20:17:56,703 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:18:25,047 - gui.metrics_calculator - INFO - Calculated metrics for 216 trades
2025-06-30 20:18:25,065 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:18:25,086 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:18:25,099 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:18:25,123 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 20:18:54,247 - gui.strategy_manager - INFO - Saved and reloaded strategy code: 4BarCompare
2025-06-30 20:18:54,251 - __main__ - INFO - Saved strategy: 4BarCompare
2025-06-30 20:18:57,031 - __main__ - INFO - Found 4 strategies
2025-06-30 20:18:59,471 - __main__ - INFO - Starting optimization...
2025-06-30 20:18:59,487 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:18:59,494 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:18:59,495 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:18:59,496 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-06-30 20:18:59,496 - gui.strategy_manager - INFO - Minimum bars: train=314, test=125
2025-06-30 20:18:59,496 - gui.strategy_manager - INFO - Window 1: Train 314 bars (2015-06-30 00:00:00 to 2016-09-26 00:00:00), Test 125 bars (2016-09-27 00:00:00 to 2017-03-27 00:00:00)
2025-06-30 20:18:59,497 - gui.strategy_manager - INFO - Window 2: Train 314 bars (2017-03-28 00:00:00 to 2018-06-25 00:00:00), Test 125 bars (2018-06-26 00:00:00 to 2018-12-21 00:00:00)
2025-06-30 20:18:59,497 - gui.strategy_manager - INFO - Window 3: Train 314 bars (2018-12-24 00:00:00 to 2020-03-24 00:00:00), Test 125 bars (2020-03-25 00:00:00 to 2020-09-21 00:00:00)
2025-06-30 20:18:59,497 - gui.strategy_manager - INFO - Window 4: Train 314 bars (2020-09-22 00:00:00 to 2021-12-17 00:00:00), Test 125 bars (2021-12-20 00:00:00 to 2022-06-17 00:00:00)
2025-06-30 20:18:59,497 - gui.strategy_manager - INFO - Window 5: Train 314 bars (2022-06-21 00:00:00 to 2023-09-19 00:00:00), Test 125 bars (2023-09-20 00:00:00 to 2024-03-19 00:00:00)
2025-06-30 20:18:59,498 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 20:20:11,181 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 7.5160
2025-06-30 20:20:11,181 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 7.5160
2025-06-30 20:20:11,193 - __main__ - INFO - Optimization completed. Best performance: 7.5160
2025-06-30 20:20:11,203 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 9, 'LX': 5, 'SX': 8, 'ADXPeriod': 15}
2025-06-30 20:20:11,210 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 20:20:11,315 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 9.0, 'LX': 5.0, 'SX': 8.0, 'ADXPeriod': 15.0}
2025-06-30 20:20:11,325 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 9.0, 'LX': 5.0, 'SX': 8.0, 'ADXPeriod': 15.0}
2025-06-30 20:20:11,340 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:20:39,177 - gui.metrics_calculator - INFO - Calculated metrics for 49 trades
2025-06-30 20:20:39,195 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:20:39,212 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:20:39,220 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:20:39,239 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 20:23:31,864 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 20:23:40,260 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 20:23:40,268 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 20:23:40,275 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 20:23:40,281 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 20:23:40,337 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 20:23:40,344 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 20:23:40,347 - __main__ - INFO - Found 4 strategies
2025-06-30 20:23:40,354 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 20:23:53,816 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-06-30 20:23:54,349 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-06-30 20:23:54,366 - __main__ - INFO - Successfully loaded 6617 bars
2025-06-30 20:24:00,192 - __main__ - INFO - Starting backtest...
2025-06-30 20:24:00,195 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 2.0, 'SE': 2.0, 'LX': 2.0, 'SX': 2.0, 'ADXPeriod': 14.0}
2025-06-30 20:24:00,205 - __main__ - INFO - Updated parameters: {'LE': 2.0, 'SE': 2.0, 'LX': 2.0, 'SX': 2.0, 'ADXPeriod': 14.0}
2025-06-30 20:24:32,538 - __main__ - INFO - Timeframe changed to: Last 10 Years
2025-06-30 20:24:32,578 - __main__ - INFO - Applied timeframe 'Last 10 Years': 2015-06-30 to 2025-06-27
2025-06-30 20:24:35,708 - __main__ - INFO - Starting backtest...
2025-06-30 20:24:35,745 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 2.0, 'SE': 2.0, 'LX': 2.0, 'SX': 2.0, 'ADXPeriod': 14.0}
2025-06-30 20:24:35,765 - __main__ - INFO - Updated parameters: {'LE': 2.0, 'SE': 2.0, 'LX': 2.0, 'SX': 2.0, 'ADXPeriod': 14.0}
2025-06-30 20:24:35,837 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:25:44,906 - gui.metrics_calculator - INFO - Calculated metrics for 193 trades
2025-06-30 20:25:45,088 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:25:45,466 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:25:45,487 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:25:47,242 - __main__ - INFO - Backtest completed successfully
2025-06-30 20:25:56,436 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:25:56,799 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:25:56,832 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:25:58,714 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:25:59,098 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:25:59,119 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:00,995 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:01,390 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:01,423 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:03,327 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:03,747 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:03,763 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:11,607 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:11,985 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:12,018 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:14,069 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:14,465 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:14,490 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:20,004 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:20,395 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:20,424 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:22,534 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:22,812 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:22,849 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:25,032 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:25,449 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:25,469 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:27,709 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:28,054 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:28,088 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:26:32,715 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:33,109 - __main__ - INFO - Using date range 2015-06-30 to 2025-06-27: 2514 bars
2025-06-30 20:26:33,134 - __main__ - INFO - Calculated returns for 121 months from 2015-06 to 2025-06
2025-06-30 20:27:01,764 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/ES.csv
2025-06-30 20:27:13,728 - __main__ - INFO - Changing bar size to: 30 Minutes
2025-06-30 20:27:18,575 - __main__ - INFO - Data resampled to 30 Minutes: 6617 bars
2025-06-30 20:28:23,620 - gui.data_manager - INFO - Successfully loaded 727709 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/ES.csv
2025-06-30 20:28:31,980 - __main__ - INFO - Data resampled to 30 Minutes
2025-06-30 20:28:32,118 - __main__ - INFO - Successfully loaded 24583 bars
2025-06-30 20:28:39,246 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 20:28:39,343 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 20:28:41,205 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 20:28:41,227 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 20:28:51,918 - __main__ - INFO - Timeframe changed to: Last 5 Years
2025-06-30 20:28:52,006 - __main__ - INFO - Applied timeframe 'Last 5 Years': 2020-06-19 to 2025-06-18
2025-06-30 20:28:57,534 - gui.metrics_calculator - INFO - Calculated metrics for 193 trades
2025-06-30 20:28:57,578 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:28:57,618 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:28:57,624 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 20:28:57,636 - __main__ - INFO - Backtest completed successfully
2025-06-30 20:29:01,920 - __main__ - INFO - Starting optimization...
2025-06-30 20:29:01,956 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:29:01,959 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:29:01,959 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:29:01,959 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 20:29:01,960 - gui.strategy_manager - INFO - Minimum bars: train=2347, test=938
2025-06-30 20:29:01,960 - gui.strategy_manager - INFO - Window 1: Train 2347 bars (2020-06-19 09:30:00 to 2021-02-03 12:00:00), Test 938 bars (2021-02-03 12:30:00 to 2021-05-04 16:00:00)
2025-06-30 20:29:01,960 - gui.strategy_manager - INFO - Window 2: Train 2347 bars (2021-05-04 16:30:00 to 2021-12-15 15:30:00), Test 938 bars (2021-12-15 16:00:00 to 2022-03-17 12:00:00)
2025-06-30 20:29:01,961 - gui.strategy_manager - INFO - Window 3: Train 2347 bars (2022-03-17 12:30:00 to 2022-10-28 15:30:00), Test 938 bars (2022-10-28 16:00:00 to 2023-01-31 15:30:00)
2025-06-30 20:29:01,961 - gui.strategy_manager - INFO - Window 4: Train 2347 bars (2023-01-31 16:00:00 to 2023-09-15 15:30:00), Test 938 bars (2023-09-15 16:00:00 to 2023-12-14 15:30:00)
2025-06-30 20:29:01,961 - gui.strategy_manager - INFO - Window 5: Train 2347 bars (2023-12-14 16:00:00 to 2024-08-01 15:00:00), Test 938 bars (2024-08-01 15:30:00 to 2024-10-30 11:30:00)
2025-06-30 20:29:01,961 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 20:30:17,377 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 3934.4500
2025-06-30 20:30:17,378 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 3934.4500
2025-06-30 20:30:17,386 - __main__ - INFO - Optimization completed. Best performance: 3934.4500
2025-06-30 20:30:17,388 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 6, 'LXlim': 18, 'SXlim': 11, 'HighThreshold': 20, 'LowThreshold': 8}
2025-06-30 20:30:17,391 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 20:30:17,494 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 6.0, 'LXlim': 18.0, 'SXlim': 11.0, 'HighThreshold': 20.0, 'LowThreshold': 8.0}
2025-06-30 20:30:17,497 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 6.0, 'LXlim': 18.0, 'SXlim': 11.0, 'HighThreshold': 20.0, 'LowThreshold': 8.0}
2025-06-30 20:30:17,532 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:32:26,533 - gui.metrics_calculator - INFO - Calculated metrics for 655 trades
2025-06-30 20:32:26,588 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:32:26,635 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:32:26,650 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 20:32:26,662 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 20:34:12,906 - __main__ - INFO - Starting optimization...
2025-06-30 20:34:12,944 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:34:12,947 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:34:12,948 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:34:12,948 - gui.strategy_manager - INFO - Creating walk-forward windows: train=10 days, test=5 days
2025-06-30 20:34:12,949 - gui.strategy_manager - INFO - Minimum bars: train=2347, test=938
2025-06-30 20:34:12,949 - gui.strategy_manager - INFO - Window 1: Train 2347 bars (2020-06-19 09:30:00 to 2021-02-03 12:00:00), Test 938 bars (2021-02-03 12:30:00 to 2021-05-04 16:00:00)
2025-06-30 20:34:12,949 - gui.strategy_manager - INFO - Window 2: Train 2347 bars (2021-05-04 16:30:00 to 2021-12-15 15:30:00), Test 938 bars (2021-12-15 16:00:00 to 2022-03-17 12:00:00)
2025-06-30 20:34:12,950 - gui.strategy_manager - INFO - Window 3: Train 2347 bars (2022-03-17 12:30:00 to 2022-10-28 15:30:00), Test 938 bars (2022-10-28 16:00:00 to 2023-01-31 15:30:00)
2025-06-30 20:34:12,950 - gui.strategy_manager - INFO - Window 4: Train 2347 bars (2023-01-31 16:00:00 to 2023-09-15 15:30:00), Test 938 bars (2023-09-15 16:00:00 to 2023-12-14 15:30:00)
2025-06-30 20:34:12,950 - gui.strategy_manager - INFO - Window 5: Train 2347 bars (2023-12-14 16:00:00 to 2024-08-01 15:00:00), Test 938 bars (2024-08-01 15:30:00 to 2024-10-30 11:30:00)
2025-06-30 20:34:12,950 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 20:35:09,663 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 3934.4500
2025-06-30 20:35:09,663 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 3934.4500
2025-06-30 20:35:09,674 - __main__ - INFO - Optimization completed. Best performance: 3934.4500
2025-06-30 20:35:09,676 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 6, 'LXlim': 18, 'SXlim': 11, 'HighThreshold': 20, 'LowThreshold': 8}
2025-06-30 20:35:09,679 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 20:35:09,789 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 6.0, 'LXlim': 18.0, 'SXlim': 11.0, 'HighThreshold': 20.0, 'LowThreshold': 8.0}
2025-06-30 20:35:09,792 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 6.0, 'LXlim': 18.0, 'SXlim': 11.0, 'HighThreshold': 20.0, 'LowThreshold': 8.0}
2025-06-30 20:35:09,827 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:35:12,483 - gui.metrics_calculator - INFO - Calculated metrics for 655 trades
2025-06-30 20:35:12,539 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:35:12,586 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18776 bars
2025-06-30 20:35:12,601 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 20:35:12,613 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 20:37:47,198 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 20:38:21,132 - gui.data_manager - INFO - Successfully loaded 727258 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 20:38:24,513 - __main__ - INFO - Data resampled to 30 Minutes
2025-06-30 20:38:24,524 - __main__ - INFO - Successfully loaded 24568 bars
2025-06-30 20:40:16,164 - __main__ - INFO - Starting optimization...
2025-06-30 20:40:16,191 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 20:40:16,194 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 20:40:16,194 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 20:40:16,194 - gui.strategy_manager - INFO - Creating walk-forward windows: train=10 days, test=5 days
2025-06-30 20:40:16,194 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 20:40:16,195 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 20:40:16,195 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 20:40:16,195 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 20:40:16,196 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 20:40:16,196 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 20:40:16,196 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 20:41:04,921 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 77541.7500
2025-06-30 20:41:04,921 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 77541.7500
2025-06-30 20:41:04,928 - __main__ - INFO - Optimization completed. Best performance: 77541.7500
2025-06-30 20:41:04,930 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 8, 'LXlim': 11, 'SXlim': 14, 'HighThreshold': 20, 'LowThreshold': 16}
2025-06-30 20:41:04,931 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 20:41:05,037 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 11.0, 'SXlim': 14.0, 'HighThreshold': 20.0, 'LowThreshold': 16.0}
2025-06-30 20:41:05,040 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 11.0, 'SXlim': 14.0, 'HighThreshold': 20.0, 'LowThreshold': 16.0}
2025-06-30 20:41:05,059 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 20:43:21,850 - gui.metrics_calculator - INFO - Calculated metrics for 981 trades
2025-06-30 20:43:21,886 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 20:43:21,912 - __main__ - INFO - Using date range 2018-11-30 to 2025-06-18: 24568 bars
2025-06-30 20:43:21,919 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 20:43:21,932 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 20:43:47,929 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 20:55:19,964 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 20:55:19,970 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 20:55:19,974 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 20:55:19,978 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 20:55:20,012 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 20:55:20,016 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 20:55:20,018 - __main__ - INFO - Found 4 strategies
2025-06-30 20:55:20,025 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 20:55:22,398 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 20:55:41,208 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 20:55:41,214 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 20:55:41,218 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 20:55:41,221 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 20:55:41,250 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 20:55:41,255 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 20:55:41,258 - __main__ - INFO - Found 4 strategies
2025-06-30 20:55:41,264 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 20:55:49,056 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 20:56:21,089 - gui.data_manager - INFO - Successfully loaded 727258 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 20:56:21,440 - __main__ - INFO - Successfully loaded 727258 bars
2025-06-30 20:56:44,749 - __main__ - INFO - Changing bar size to: 30 Minutes
2025-06-30 20:56:48,232 - __main__ - INFO - Data resampled to 30 Minutes: 24568 bars
2025-06-30 20:56:53,341 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 20:56:53,342 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 20:56:53,381 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 20:56:53,387 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 21:05:02,596 - gui.strategy_manager - INFO - Saved strategy code with syntax errors: NQ30min1
2025-06-30 21:05:02,602 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 21:05:59,219 - gui.strategy_manager - INFO - Saved strategy code with syntax errors: NQ30min1
2025-06-30 21:05:59,233 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 21:07:04,893 - gui.strategy_manager - INFO - Saved strategy code with syntax errors: NQ30min1
2025-06-30 21:07:04,899 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 21:07:36,914 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 21:07:36,922 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 21:07:39,284 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 21:07:40,760 - __main__ - INFO - Found 2 strategies
2025-06-30 21:07:53,694 - __main__ - INFO - Starting optimization...
2025-06-30 21:07:53,707 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 21:07:53,707 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 21:07:53,709 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 21:07:53,710 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 21:07:53,711 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 21:07:53,713 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 21:07:53,715 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 21:07:53,715 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 21:07:53,718 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 21:07:53,719 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 21:08:58,285 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 77541.7500
2025-06-30 21:08:58,286 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 77541.7500
2025-06-30 21:08:58,295 - __main__ - INFO - Optimization completed. Best performance: 77541.7500
2025-06-30 21:08:58,306 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 8, 'LXlim': 11, 'SXlim': 14, 'HighThreshold': 20, 'LowThreshold': 16}
2025-06-30 21:08:58,311 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 21:08:58,412 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 11.0, 'SXlim': 14.0, 'HighThreshold': 20.0, 'LowThreshold': 16.0}
2025-06-30 21:08:58,455 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 11.0, 'SXlim': 14.0, 'HighThreshold': 20.0, 'LowThreshold': 16.0}
2025-06-30 21:09:12,544 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 21:09:12,570 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 21:09:15,758 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 21:09:36,756 - __main__ - INFO - Found 2 strategies
2025-06-30 21:09:38,600 - __main__ - INFO - Starting optimization...
2025-06-30 21:09:38,677 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 21:09:38,684 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 21:09:38,700 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 21:09:38,708 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 21:09:38,718 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 21:09:38,742 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 21:09:38,759 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 21:09:38,780 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 21:09:38,786 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 21:09:38,797 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 21:10:05,787 - __main__ - INFO - Timeframe changed to: Last 5 Years
2025-06-30 21:10:05,800 - __main__ - INFO - Applied timeframe 'Last 5 Years': 2020-06-19 to 2025-06-18
2025-06-30 21:10:08,984 - __main__ - INFO - Starting optimization...
2025-06-30 21:10:09,041 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:10:09,055 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 21:10:09,055 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 21:10:09,062 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 21:10:09,063 - gui.strategy_manager - INFO - Minimum bars: train=2345, test=938
2025-06-30 21:10:09,070 - gui.strategy_manager - INFO - Window 1: Train 2345 bars (2020-06-19 09:30:00 to 2021-02-03 11:00:00), Test 938 bars (2021-02-03 11:30:00 to 2021-05-04 15:00:00)
2025-06-30 21:10:09,070 - gui.strategy_manager - INFO - Window 2: Train 2345 bars (2021-05-04 15:30:00 to 2021-12-16 13:30:00), Test 938 bars (2021-12-16 14:00:00 to 2022-03-18 10:00:00)
2025-06-30 21:10:09,071 - gui.strategy_manager - INFO - Window 3: Train 2345 bars (2022-03-18 10:30:00 to 2022-10-31 12:30:00), Test 938 bars (2022-10-31 13:00:00 to 2023-02-01 12:30:00)
2025-06-30 21:10:09,077 - gui.strategy_manager - INFO - Window 4: Train 2345 bars (2023-02-01 13:00:00 to 2023-09-18 11:30:00), Test 938 bars (2023-09-18 12:00:00 to 2023-12-15 11:30:00)
2025-06-30 21:10:09,085 - gui.strategy_manager - INFO - Window 5: Train 2345 bars (2023-12-15 12:00:00 to 2024-08-02 10:00:00), Test 938 bars (2024-08-02 10:30:00 to 2024-10-30 14:00:00)
2025-06-30 21:10:09,090 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 21:11:47,090 - gui.metrics_calculator - INFO - Calculated metrics for 964 trades
2025-06-30 21:11:47,127 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:11:47,184 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:11:47,199 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 21:11:47,335 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 21:12:43,779 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 21504.5000
2025-06-30 21:12:43,781 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 21504.5000
2025-06-30 21:12:43,796 - __main__ - INFO - Optimization completed. Best performance: 21504.5000
2025-06-30 21:12:43,810 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 9, 'LXlim': 10, 'SXlim': 12, 'HighThreshold': 20, 'LowThreshold': 4, 'BigBar': 30}
2025-06-30 21:12:43,815 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 21:12:43,948 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 12.0, 'HighThreshold': 20.0, 'LowThreshold': 4.0}
2025-06-30 21:12:43,962 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 12.0, 'HighThreshold': 20.0, 'LowThreshold': 4.0}
2025-06-30 21:12:43,985 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:14:02,145 - gui.metrics_calculator - INFO - Calculated metrics for 720 trades
2025-06-30 21:14:02,194 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:14:02,241 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:14:02,251 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 21:14:02,283 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 21:14:10,073 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 72436.1000
2025-06-30 21:14:10,074 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 72436.1000
2025-06-30 21:14:10,083 - __main__ - INFO - Optimization completed. Best performance: 72436.1000
2025-06-30 21:14:10,098 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 9, 'LXlim': 10, 'SXlim': 30, 'HighThreshold': 4, 'LowThreshold': 20, 'BigBar': 30}
2025-06-30 21:14:10,104 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 21:14:10,210 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 30.0, 'HighThreshold': 4.0, 'LowThreshold': 20.0}
2025-06-30 21:14:10,246 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 30.0, 'HighThreshold': 4.0, 'LowThreshold': 20.0}
2025-06-30 21:14:10,271 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:15:19,987 - gui.metrics_calculator - INFO - Calculated metrics for 709 trades
2025-06-30 21:15:20,037 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:15:20,063 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:15:20,072 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 21:15:20,086 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 21:20:49,764 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:20:49,793 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:20:49,802 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 21:21:16,106 - __main__ - INFO - Starting backtest...
2025-06-30 21:21:16,110 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 30.0, 'HighThreshold': 4.0, 'LowThreshold': 20.0}
2025-06-30 21:21:16,125 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 30.0, 'HighThreshold': 4.0, 'LowThreshold': 20.0}
2025-06-30 21:21:16,144 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:21:18,239 - gui.metrics_calculator - INFO - Calculated metrics for 709 trades
2025-06-30 21:21:18,267 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:21:18,293 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:21:18,306 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 21:21:18,355 - __main__ - INFO - Backtest completed successfully
2025-06-30 21:23:37,459 - __main__ - INFO - Starting optimization...
2025-06-30 21:23:37,487 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:23:37,493 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 21:23:37,493 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 21:23:37,494 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 21:23:37,495 - gui.strategy_manager - INFO - Minimum bars: train=2345, test=938
2025-06-30 21:23:37,496 - gui.strategy_manager - INFO - Window 1: Train 2345 bars (2020-06-19 09:30:00 to 2021-02-03 11:00:00), Test 938 bars (2021-02-03 11:30:00 to 2021-05-04 15:00:00)
2025-06-30 21:23:37,496 - gui.strategy_manager - INFO - Window 2: Train 2345 bars (2021-05-04 15:30:00 to 2021-12-16 13:30:00), Test 938 bars (2021-12-16 14:00:00 to 2022-03-18 10:00:00)
2025-06-30 21:23:37,497 - gui.strategy_manager - INFO - Window 3: Train 2345 bars (2022-03-18 10:30:00 to 2022-10-31 12:30:00), Test 938 bars (2022-10-31 13:00:00 to 2023-02-01 12:30:00)
2025-06-30 21:23:37,498 - gui.strategy_manager - INFO - Window 4: Train 2345 bars (2023-02-01 13:00:00 to 2023-09-18 11:30:00), Test 938 bars (2023-09-18 12:00:00 to 2023-12-15 11:30:00)
2025-06-30 21:23:37,499 - gui.strategy_manager - INFO - Window 5: Train 2345 bars (2023-12-15 12:00:00 to 2024-08-02 10:00:00), Test 938 bars (2024-08-02 10:30:00 to 2024-10-30 14:00:00)
2025-06-30 21:23:37,499 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 21:24:16,609 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 26844.9000
2025-06-30 21:24:16,610 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 26844.9000
2025-06-30 21:24:16,620 - __main__ - INFO - Optimization completed. Best performance: 26844.9000
2025-06-30 21:24:16,632 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 9, 'LXlim': 10, 'SXlim': 10, 'HighThreshold': 4, 'LowThreshold': 20, 'BigBar': 30}
2025-06-30 21:24:16,637 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 21:24:16,741 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 10.0, 'HighThreshold': 4.0, 'LowThreshold': 20.0}
2025-06-30 21:24:16,762 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 10.0, 'HighThreshold': 4.0, 'LowThreshold': 20.0}
2025-06-30 21:24:16,783 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:24:19,775 - gui.metrics_calculator - INFO - Calculated metrics for 803 trades
2025-06-30 21:24:19,805 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:24:19,830 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:24:19,842 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 21:24:19,856 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 21:27:29,396 - __main__ - INFO - Starting optimization...
2025-06-30 21:27:29,427 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:27:29,432 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 21:27:29,432 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 21:27:29,433 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 21:27:29,434 - gui.strategy_manager - INFO - Minimum bars: train=2345, test=938
2025-06-30 21:27:29,435 - gui.strategy_manager - INFO - Window 1: Train 2345 bars (2020-06-19 09:30:00 to 2021-02-03 11:00:00), Test 938 bars (2021-02-03 11:30:00 to 2021-05-04 15:00:00)
2025-06-30 21:27:29,436 - gui.strategy_manager - INFO - Window 2: Train 2345 bars (2021-05-04 15:30:00 to 2021-12-16 13:30:00), Test 938 bars (2021-12-16 14:00:00 to 2022-03-18 10:00:00)
2025-06-30 21:27:29,437 - gui.strategy_manager - INFO - Window 3: Train 2345 bars (2022-03-18 10:30:00 to 2022-10-31 12:30:00), Test 938 bars (2022-10-31 13:00:00 to 2023-02-01 12:30:00)
2025-06-30 21:27:29,437 - gui.strategy_manager - INFO - Window 4: Train 2345 bars (2023-02-01 13:00:00 to 2023-09-18 11:30:00), Test 938 bars (2023-09-18 12:00:00 to 2023-12-15 11:30:00)
2025-06-30 21:27:29,438 - gui.strategy_manager - INFO - Window 5: Train 2345 bars (2023-12-15 12:00:00 to 2024-08-02 10:00:00), Test 938 bars (2024-08-02 10:30:00 to 2024-10-30 14:00:00)
2025-06-30 21:27:29,438 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 21:28:07,802 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 26844.9000
2025-06-30 21:28:07,803 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 26844.9000
2025-06-30 21:28:07,812 - __main__ - INFO - Optimization completed. Best performance: 26844.9000
2025-06-30 21:28:07,825 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 9, 'LXlim': 10, 'SXlim': 10, 'HighThreshold': 4, 'LowThreshold': 20, 'BigBar': 30}
2025-06-30 21:28:07,830 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 21:28:07,939 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 10.0, 'HighThreshold': 4.0, 'LowThreshold': 20.0}
2025-06-30 21:28:07,977 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 10.0, 'SXlim': 10.0, 'HighThreshold': 4.0, 'LowThreshold': 20.0}
2025-06-30 21:28:08,006 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:28:10,657 - gui.metrics_calculator - INFO - Calculated metrics for 803 trades
2025-06-30 21:28:10,683 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:28:10,708 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:28:10,717 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 21:28:10,731 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 21:37:25,797 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 21:37:25,800 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 21:37:28,636 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 21:37:33,407 - __main__ - INFO - Found 2 strategies
2025-06-30 21:37:41,551 - __main__ - INFO - Starting optimization...
2025-06-30 21:37:41,576 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:37:41,582 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 21:37:41,582 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 21:37:41,583 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 21:37:41,584 - gui.strategy_manager - INFO - Minimum bars: train=2345, test=938
2025-06-30 21:37:41,584 - gui.strategy_manager - INFO - Window 1: Train 2345 bars (2020-06-19 09:30:00 to 2021-02-03 11:00:00), Test 938 bars (2021-02-03 11:30:00 to 2021-05-04 15:00:00)
2025-06-30 21:37:41,585 - gui.strategy_manager - INFO - Window 2: Train 2345 bars (2021-05-04 15:30:00 to 2021-12-16 13:30:00), Test 938 bars (2021-12-16 14:00:00 to 2022-03-18 10:00:00)
2025-06-30 21:37:41,585 - gui.strategy_manager - INFO - Window 3: Train 2345 bars (2022-03-18 10:30:00 to 2022-10-31 12:30:00), Test 938 bars (2022-10-31 13:00:00 to 2023-02-01 12:30:00)
2025-06-30 21:37:41,586 - gui.strategy_manager - INFO - Window 4: Train 2345 bars (2023-02-01 13:00:00 to 2023-09-18 11:30:00), Test 938 bars (2023-09-18 12:00:00 to 2023-12-15 11:30:00)
2025-06-30 21:37:41,587 - gui.strategy_manager - INFO - Window 5: Train 2345 bars (2023-12-15 12:00:00 to 2024-08-02 10:00:00), Test 938 bars (2024-08-02 10:30:00 to 2024-10-30 14:00:00)
2025-06-30 21:37:41,587 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 21:38:28,214 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 31456.6500
2025-06-30 21:38:28,215 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 31456.6500
2025-06-30 21:38:28,224 - __main__ - INFO - Optimization completed. Best performance: 31456.6500
2025-06-30 21:38:28,239 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 5, 'RollMinPeriod': 4, 'LXlim': 12, 'LXlim_BigBar': 24, 'SXlim': 13, 'HighThreshold': 7, 'LowThreshold': 18, 'BigBar': 14}
2025-06-30 21:38:28,244 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 21:38:28,352 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 5.0, 'RollMinPeriod': 4.0, 'LXlim': 12.0, 'SXlim': 13.0, 'HighThreshold': 7.0, 'LowThreshold': 18.0}
2025-06-30 21:38:28,392 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 5.0, 'RollMinPeriod': 4.0, 'LXlim': 12.0, 'SXlim': 13.0, 'HighThreshold': 7.0, 'LowThreshold': 18.0}
2025-06-30 21:38:28,417 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:39:43,695 - gui.metrics_calculator - INFO - Calculated metrics for 781 trades
2025-06-30 21:39:43,728 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:39:43,758 - __main__ - INFO - Using date range 2020-06-19 to 2025-06-18: 18761 bars
2025-06-30 21:39:43,774 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-06-30 21:39:43,788 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 21:40:36,472 - __main__ - INFO - Starting optimization...
2025-06-30 21:40:36,482 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 21:40:36,483 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 21:40:36,484 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 21:40:36,485 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 21:40:36,485 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 21:40:36,486 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 21:40:36,486 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 21:40:36,487 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 21:40:36,487 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 21:40:36,491 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 21:41:32,078 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 95509.9000
2025-06-30 21:41:32,079 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 95509.9000
2025-06-30 21:41:32,090 - __main__ - INFO - Optimization completed. Best performance: 95509.9000
2025-06-30 21:41:32,104 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 8, 'LXlim': 10, 'LXlim_BigBar': 10, 'SXlim': 10, 'HighThreshold': 20, 'LowThreshold': 20, 'BigBar': 30}
2025-06-30 21:41:32,109 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 21:41:32,212 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 10.0, 'SXlim': 10.0, 'HighThreshold': 20.0, 'LowThreshold': 20.0}
2025-06-30 21:41:32,247 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 10.0, 'SXlim': 10.0, 'HighThreshold': 20.0, 'LowThreshold': 20.0}
2025-06-30 21:43:10,801 - gui.metrics_calculator - INFO - Calculated metrics for 913 trades
2025-06-30 21:43:10,830 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 21:43:10,843 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 21:43:58,168 - __main__ - INFO - Starting optimization...
2025-06-30 21:43:58,180 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 21:43:58,180 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 21:43:58,181 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 21:43:58,182 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 21:43:58,183 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 21:43:58,183 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 21:43:58,183 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 21:43:58,184 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 21:43:58,185 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 21:43:58,185 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 21:44:01,040 - __main__ - INFO - Starting backtest...
2025-06-30 21:44:01,044 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 10.0, 'SXlim': 10.0, 'HighThreshold': 20.0, 'LowThreshold': 20.0}
2025-06-30 21:44:01,058 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 10.0, 'SXlim': 10.0, 'HighThreshold': 20.0, 'LowThreshold': 20.0}
2025-06-30 21:45:38,845 - gui.metrics_calculator - INFO - Calculated metrics for 884 trades
2025-06-30 21:45:38,900 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 21:45:38,983 - __main__ - INFO - Backtest completed successfully
2025-06-30 21:46:35,008 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 95509.9000
2025-06-30 21:46:35,008 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 95509.9000
2025-06-30 21:46:35,021 - __main__ - INFO - Optimization completed. Best performance: 95509.9000
2025-06-30 21:46:35,038 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 8, 'LXlim': 30, 'LXlim_BigBar': 30, 'SXlim': 10, 'HighThreshold': 20, 'LowThreshold': 4, 'BigBar': 10}
2025-06-30 21:46:35,045 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 21:46:35,160 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 30.0, 'SXlim': 10.0, 'HighThreshold': 20.0, 'LowThreshold': 4.0}
2025-06-30 21:46:35,173 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 30.0, 'SXlim': 10.0, 'HighThreshold': 20.0, 'LowThreshold': 4.0}
2025-06-30 21:46:38,168 - gui.metrics_calculator - INFO - Calculated metrics for 913 trades
2025-06-30 21:46:38,222 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 21:46:38,239 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 21:46:52,969 - __main__ - INFO - Starting backtest...
2025-06-30 21:46:52,973 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 30.0, 'SXlim': 10.0, 'HighThreshold': 20.0, 'LowThreshold': 4.0}
2025-06-30 21:46:52,988 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 8.0, 'LXlim': 30.0, 'SXlim': 10.0, 'HighThreshold': 20.0, 'LowThreshold': 4.0}
2025-06-30 21:46:55,946 - gui.metrics_calculator - INFO - Calculated metrics for 913 trades
2025-06-30 21:46:55,975 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 21:46:56,031 - __main__ - INFO - Backtest completed successfully
2025-06-30 22:00:22,137 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 22:04:27,951 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 22:04:27,957 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 22:04:27,961 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 22:04:27,974 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 22:04:28,009 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 22:04:28,014 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 22:04:28,017 - __main__ - INFO - Found 2 strategies
2025-06-30 22:04:28,021 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 22:04:35,842 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 22:05:03,238 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 22:05:03,441 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 22:05:05,714 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 22:05:05,776 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 22:05:09,279 - gui.data_manager - INFO - Successfully loaded 727258 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 22:05:09,656 - __main__ - INFO - Successfully loaded 727258 bars
2025-06-30 22:06:42,322 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 22:06:42,329 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 22:06:45,510 - __main__ - INFO - Found 2 strategies
2025-06-30 22:06:51,520 - __main__ - INFO - Changing bar size to: 30 Minutes
2025-06-30 22:06:55,098 - __main__ - INFO - Data resampled to 30 Minutes: 24568 bars
2025-06-30 22:06:59,854 - __main__ - INFO - Starting optimization...
2025-06-30 22:06:59,864 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:06:59,864 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:07:04,028 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 24568 bars
2025-06-30 22:07:16,918 - __main__ - INFO - Starting optimization...
2025-06-30 22:07:16,963 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:07:16,970 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:07:16,973 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 22:07:17,000 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:07:17,032 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:07:17,064 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:07:17,079 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:07:17,089 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:07:17,096 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:07:17,102 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:10:39,960 - __main__ - INFO - Found 2 strategies
2025-06-30 22:10:47,499 - __main__ - INFO - Starting optimization...
2025-06-30 22:10:47,562 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:10:47,563 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:10:47,573 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 22:10:47,580 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:10:47,581 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:10:47,586 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:10:47,587 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:10:47,593 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:10:47,598 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:10:47,599 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:13:18,716 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 22:13:18,717 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 22:13:22,038 - __main__ - INFO - Found 2 strategies
2025-06-30 22:13:27,620 - __main__ - INFO - Starting optimization...
2025-06-30 22:13:27,629 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:13:27,630 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:13:27,631 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 22:13:27,634 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:13:27,634 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:13:27,635 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:13:27,635 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:13:27,636 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:13:27,637 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:13:27,638 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:21:05,086 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 30842.8500
2025-06-30 22:21:05,087 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 30842.8500
2025-06-30 22:21:05,096 - __main__ - INFO - Optimization completed. Best performance: 30842.8500
2025-06-30 22:21:05,097 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 9, 'LXlim': 21, 'LXlim_BigBar': 18, 'SXlim': 10, 'HighThreshold': 8, 'LowThreshold': 8, 'BigBar': 24}
2025-06-30 22:21:05,098 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:21:05,211 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 21.0, 'LXlim_BigBar': 18.0, 'SXlim': 10.0, 'HighThreshold': 8.0, 'LowThreshold': 8.0, 'BigBar': 24.0}
2025-06-30 22:21:05,213 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 21.0, 'LXlim_BigBar': 18.0, 'SXlim': 10.0, 'HighThreshold': 8.0, 'LowThreshold': 8.0, 'BigBar': 24.0}
2025-06-30 22:22:20,802 - gui.metrics_calculator - INFO - Calculated metrics for 892 trades
2025-06-30 22:22:20,839 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:22:20,857 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 22:22:30,571 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 26914.3000
2025-06-30 22:22:30,575 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 26914.3000
2025-06-30 22:22:30,647 - __main__ - INFO - Optimization completed. Best performance: 26914.3000
2025-06-30 22:22:30,653 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 6, 'RollMinPeriod': 2, 'LXlim': 10, 'LXlim_BigBar': 20, 'SXlim': 18, 'HighThreshold': 5, 'LowThreshold': 20, 'BigBar': 15}
2025-06-30 22:22:30,657 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:22:30,760 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 6.0, 'RollMinPeriod': 2.0, 'LXlim': 10.0, 'LXlim_BigBar': 20.0, 'SXlim': 18.0, 'HighThreshold': 5.0, 'LowThreshold': 20.0, 'BigBar': 15.0}
2025-06-30 22:22:30,762 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 6.0, 'RollMinPeriod': 2.0, 'LXlim': 10.0, 'LXlim_BigBar': 20.0, 'SXlim': 18.0, 'HighThreshold': 5.0, 'LowThreshold': 20.0, 'BigBar': 15.0}
2025-06-30 22:23:06,693 - gui.metrics_calculator - INFO - Calculated metrics for 874 trades
2025-06-30 22:23:06,768 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:23:06,869 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 22:25:39,285 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 26888.0000
2025-06-30 22:25:39,288 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 26888.0000
2025-06-30 22:25:39,296 - __main__ - INFO - Optimization completed. Best performance: 26888.0000
2025-06-30 22:25:39,299 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 9, 'LXlim': 21, 'LXlim_BigBar': 18, 'SXlim': 10, 'HighThreshold': 8, 'LowThreshold': 8, 'BigBar': 15}
2025-06-30 22:25:39,302 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:25:39,411 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 21.0, 'LXlim_BigBar': 18.0, 'SXlim': 10.0, 'HighThreshold': 8.0, 'LowThreshold': 8.0, 'BigBar': 15.0}
2025-06-30 22:25:39,416 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 21.0, 'LXlim_BigBar': 18.0, 'SXlim': 10.0, 'HighThreshold': 8.0, 'LowThreshold': 8.0, 'BigBar': 15.0}
2025-06-30 22:25:48,813 - gui.metrics_calculator - INFO - Calculated metrics for 813 trades
2025-06-30 22:25:48,844 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:25:48,868 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 22:26:24,586 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 22:26:24,589 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 22:26:27,816 - __main__ - INFO - Found 2 strategies
2025-06-30 22:26:30,907 - __main__ - INFO - Starting optimization...
2025-06-30 22:26:30,940 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:26:30,943 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:26:30,948 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 22:26:30,955 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:26:30,958 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:26:30,961 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:26:30,964 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:26:30,970 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:26:30,986 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:26:30,991 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:30:00,042 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 71848.2000
2025-06-30 22:30:00,044 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 71848.2000
2025-06-30 22:30:00,054 - __main__ - INFO - Optimization completed. Best performance: 71848.2000
2025-06-30 22:30:00,056 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 9, 'LXlim': 21, 'LXlim_BigBar': 18, 'SXlim': 10, 'HighThreshold': 8, 'LowThreshold': 8, 'BigBar': 21}
2025-06-30 22:30:00,060 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:30:00,161 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 21.0, 'LXlim_BigBar': 18.0, 'SXlim': 10.0, 'HighThreshold': 8.0, 'LowThreshold': 8.0, 'BigBar': 21.0}
2025-06-30 22:30:00,166 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 9.0, 'LXlim': 21.0, 'LXlim_BigBar': 18.0, 'SXlim': 10.0, 'HighThreshold': 8.0, 'LowThreshold': 8.0, 'BigBar': 21.0}
2025-06-30 22:30:26,921 - gui.metrics_calculator - INFO - Calculated metrics for 889 trades
2025-06-30 22:30:26,975 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:30:26,995 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 22:31:50,449 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 22:31:50,452 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 22:31:53,454 - __main__ - INFO - Found 2 strategies
2025-06-30 22:31:54,016 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-06-30 22:31:54,017 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 213819.5000
2025-06-30 22:31:54,020 - __main__ - INFO - Optimization completed. Best performance: 213819.5000
2025-06-30 22:31:54,022 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 8, 'RollMinPeriod': 3, 'LXlim': 26, 'LXlim_BigBar': 22, 'SXlim': 19, 'HighThreshold': 6, 'LowThreshold': 11, 'BigBar': 17}
2025-06-30 22:31:54,024 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:31:54,136 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 8.0, 'RollMinPeriod': 3.0, 'LXlim': 26.0, 'LXlim_BigBar': 22.0, 'SXlim': 19.0, 'HighThreshold': 6.0, 'LowThreshold': 11.0, 'BigBar': 17.0}
2025-06-30 22:31:54,148 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 8.0, 'RollMinPeriod': 3.0, 'LXlim': 26.0, 'LXlim_BigBar': 22.0, 'SXlim': 19.0, 'HighThreshold': 6.0, 'LowThreshold': 11.0, 'BigBar': 17.0}
2025-06-30 22:31:56,955 - gui.metrics_calculator - INFO - Calculated metrics for 943 trades
2025-06-30 22:31:56,982 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:31:56,994 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 22:32:02,547 - __main__ - INFO - Starting optimization...
2025-06-30 22:32:02,552 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:32:02,553 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:32:02,553 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 22:32:02,554 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:32:02,555 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:32:02,555 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:32:02,556 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:32:02,557 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:32:02,558 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:32:02,560 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:33:10,316 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 88146.8500
2025-06-30 22:33:10,317 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 88146.8500
2025-06-30 22:33:10,325 - __main__ - INFO - Optimization completed. Best performance: 88146.8500
2025-06-30 22:33:10,327 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 4, 'LXlim': 30, 'LXlim_BigBar': 30, 'SXlim': 5, 'HighThreshold': 19, 'LowThreshold': 15, 'BigBar': 23}
2025-06-30 22:33:10,329 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:33:10,443 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 4.0, 'LXlim': 30.0, 'LXlim_BigBar': 30.0, 'SXlim': 5.0, 'HighThreshold': 19.0, 'LowThreshold': 15.0, 'BigBar': 23.0}
2025-06-30 22:33:10,456 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 4.0, 'LXlim': 30.0, 'LXlim_BigBar': 30.0, 'SXlim': 5.0, 'HighThreshold': 19.0, 'LowThreshold': 15.0, 'BigBar': 23.0}
2025-06-30 22:33:20,362 - gui.metrics_calculator - INFO - Calculated metrics for 1050 trades
2025-06-30 22:33:20,394 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:33:20,405 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 22:34:02,980 - __main__ - INFO - Starting backtest...
2025-06-30 22:34:02,984 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 4.0, 'LXlim': 30.0, 'LXlim_BigBar': 30.0, 'SXlim': 5.0, 'HighThreshold': 19.0, 'LowThreshold': 15.0, 'BigBar': 23.0}
2025-06-30 22:34:02,987 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 4.0, 'LXlim': 30.0, 'LXlim_BigBar': 30.0, 'SXlim': 5.0, 'HighThreshold': 19.0, 'LowThreshold': 15.0, 'BigBar': 23.0}
2025-06-30 22:34:06,496 - gui.metrics_calculator - INFO - Calculated metrics for 1050 trades
2025-06-30 22:34:06,524 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:34:06,587 - __main__ - INFO - Backtest completed successfully
2025-06-30 22:37:17,813 - __main__ - INFO - Starting optimization...
2025-06-30 22:37:17,821 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:37:17,821 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:37:17,828 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 24568 bars
2025-06-30 22:46:02,996 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-06-30 22:46:02,997 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 14977.7500
2025-06-30 22:46:03,006 - __main__ - INFO - Optimization completed. Best performance: 14977.7500
2025-06-30 22:46:03,010 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 14, 'LXlim': 21, 'LXlim_BigBar': 18, 'SXlim': 5, 'HighThreshold': 8, 'LowThreshold': 8, 'BigBar': 21}
2025-06-30 22:46:03,013 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:46:03,117 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 14.0, 'LXlim': 21.0, 'LXlim_BigBar': 18.0, 'SXlim': 5.0, 'HighThreshold': 8.0, 'LowThreshold': 8.0, 'BigBar': 21.0}
2025-06-30 22:46:03,120 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 14.0, 'LXlim': 21.0, 'LXlim_BigBar': 18.0, 'SXlim': 5.0, 'HighThreshold': 8.0, 'LowThreshold': 8.0, 'BigBar': 21.0}
2025-06-30 22:46:06,710 - gui.metrics_calculator - INFO - Calculated metrics for 871 trades
2025-06-30 22:46:06,750 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:46:06,761 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 22:51:08,406 - gui.strategy_manager - INFO - Saved and reloaded strategy code: NQ30min1
2025-06-30 22:51:08,411 - __main__ - INFO - Saved strategy: NQ30min1
2025-06-30 22:51:11,089 - __main__ - INFO - Found 2 strategies
2025-06-30 22:51:18,072 - __main__ - INFO - Starting optimization...
2025-06-30 22:51:18,080 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:51:18,081 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:51:18,089 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 24568 bars
2025-06-30 22:51:25,439 - __main__ - INFO - Starting optimization...
2025-06-30 22:51:25,495 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:51:25,498 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:51:25,504 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 22:51:25,508 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:51:25,515 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:51:25,527 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:51:25,532 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:51:25,538 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:51:25,542 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:51:25,547 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:51:26,705 - __main__ - INFO - Starting optimization...
2025-06-30 22:51:26,722 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:51:26,725 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:51:26,728 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 22:51:26,729 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:51:26,732 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:51:26,733 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:51:26,738 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:51:26,749 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:51:26,754 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:51:26,758 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:52:33,101 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-06-30 22:52:41,355 - gui.theme_manager - INFO - Dark theme configured successfully
2025-06-30 22:52:41,363 - __main__ - INFO - PyTS GUI initialized successfully
2025-06-30 22:52:41,367 - __main__ - INFO - Loading strategy: 4BarCompare
2025-06-30 22:52:41,370 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-06-30 22:52:41,402 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-06-30 22:52:41,407 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-06-30 22:52:41,411 - __main__ - INFO - Found 2 strategies
2025-06-30 22:52:41,415 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-06-30 22:52:47,773 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 22:52:53,672 - __main__ - INFO - Loading strategy: NQ30min1
2025-06-30 22:52:53,890 - gui.strategy_manager - INFO - Successfully loaded strategy: NQ30min1
2025-06-30 22:52:56,661 - __main__ - INFO - Loaded periods: 3 months train, 1 months test
2025-06-30 22:52:56,708 - __main__ - INFO - Strategy 'NQ30min1' loaded successfully
2025-06-30 22:53:23,136 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 22:53:23,424 - gui.data_manager - INFO - Successfully loaded 727258 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 22:53:23,742 - __main__ - INFO - Successfully loaded 727258 bars
2025-06-30 22:53:29,863 - __main__ - INFO - Changing bar size to: 30 Minutes
2025-06-30 22:53:37,537 - __main__ - INFO - Data resampled to 30 Minutes: 24568 bars
2025-06-30 22:53:44,429 - __main__ - INFO - Found 2 strategies
2025-06-30 22:53:59,431 - __main__ - INFO - Starting optimization...
2025-06-30 22:54:00,399 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:54:00,446 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:54:00,610 - gui.strategy_manager - INFO - Creating walk-forward windows: train=90 days, test=30 days
2025-06-30 22:54:00,673 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:54:00,704 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:54:00,720 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:54:00,822 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:54:00,845 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:54:00,860 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:54:00,891 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:54:01,083 - gui.data_manager - INFO - Successfully loaded 727258 bars from C:/Users/<USER>/Desktop/Data1/Futures/1min/NQ.csv
2025-06-30 22:54:04,362 - __main__ - INFO - Data resampled to 30 Minutes
2025-06-30 22:54:04,376 - __main__ - INFO - Successfully loaded 24568 bars
2025-06-30 22:55:28,162 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 16117.4000
2025-06-30 22:55:28,163 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 16117.4000
2025-06-30 22:55:28,172 - __main__ - INFO - Optimization completed. Best performance: 16117.4000
2025-06-30 22:55:28,172 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 11, 'LXlim': 29, 'LXlim_BigBar': 10, 'SXlim': 30, 'HighThreshold': 14, 'LowThreshold': 14, 'HL_Long': 10, 'HL_Short': 10, 'BigBar': 17}
2025-06-30 22:55:28,173 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:55:28,279 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 11.0, 'LXlim': 29.0, 'LXlim_BigBar': 10.0, 'SXlim': 30.0, 'HighThreshold': 14.0, 'LowThreshold': 14.0, 'HL_Long': 10.0, 'HL_Short': 10.0, 'BigBar': 17.0}
2025-06-30 22:55:28,281 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 11.0, 'LXlim': 29.0, 'LXlim_BigBar': 10.0, 'SXlim': 30.0, 'HighThreshold': 14.0, 'LowThreshold': 14.0, 'HL_Long': 10.0, 'HL_Short': 10.0, 'BigBar': 17.0}
2025-06-30 22:57:48,883 - gui.metrics_calculator - INFO - Calculated metrics for 789 trades
2025-06-30 22:57:48,912 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:57:48,921 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 22:57:58,174 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:58:27,649 - __main__ - INFO - Starting optimization...
2025-06-30 22:58:27,654 - __main__ - INFO - Starting optimization with metric: total_profit
2025-06-30 22:58:27,655 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-06-30 22:58:27,655 - gui.strategy_manager - INFO - Creating walk-forward windows: train=5 days, test=5 days
2025-06-30 22:58:27,656 - gui.strategy_manager - INFO - Minimum bars: train=3071, test=1228
2025-06-30 22:58:27,656 - gui.strategy_manager - INFO - Window 1: Train 3071 bars (2018-11-30 09:30:00 to 2019-09-26 14:00:00), Test 1228 bars (2019-09-26 14:30:00 to 2020-01-27 12:30:00)
2025-06-30 22:58:27,656 - gui.strategy_manager - INFO - Window 2: Train 3071 bars (2020-01-27 13:00:00 to 2020-11-16 10:30:00), Test 1228 bars (2020-11-16 11:00:00 to 2021-03-17 16:30:00)
2025-06-30 22:58:27,657 - gui.strategy_manager - INFO - Window 3: Train 3071 bars (2021-03-18 09:30:00 to 2022-01-10 10:30:00), Test 1228 bars (2022-01-10 11:00:00 to 2022-05-09 09:30:00)
2025-06-30 22:58:27,659 - gui.strategy_manager - INFO - Window 4: Train 3071 bars (2022-05-09 10:00:00 to 2023-03-03 11:00:00), Test 1228 bars (2023-03-03 11:30:00 to 2023-06-30 10:00:00)
2025-06-30 22:58:27,660 - gui.strategy_manager - INFO - Window 5: Train 3071 bars (2023-06-30 10:30:00 to 2024-04-24 15:30:00), Test 1228 bars (2024-04-24 16:00:00 to 2024-08-22 10:30:00)
2025-06-30 22:58:27,660 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-06-30 22:59:26,906 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 16117.4000
2025-06-30 22:59:26,906 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 16117.4000
2025-06-30 22:59:26,912 - __main__ - INFO - Optimization completed. Best performance: 16117.4000
2025-06-30 22:59:26,913 - __main__ - INFO - Optimized parameters: {'RollMaxPeriod': 2, 'RollMinPeriod': 11, 'LXlim': 29, 'LXlim_BigBar': 10, 'SXlim': 30, 'HighThreshold': 14, 'LowThreshold': 14, 'HL_Long': 10, 'HL_Short': 10, 'BigBar': 17}
2025-06-30 22:59:26,915 - __main__ - INFO - Running backtest with optimized parameters...
2025-06-30 22:59:27,018 - gui.strategy_manager - INFO - Updated strategy parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 11.0, 'LXlim': 29.0, 'LXlim_BigBar': 10.0, 'SXlim': 30.0, 'HighThreshold': 14.0, 'LowThreshold': 14.0, 'HL_Long': 10.0, 'HL_Short': 10.0, 'BigBar': 17.0}
2025-06-30 22:59:27,027 - __main__ - INFO - Updated parameters: {'RollMaxPeriod': 2.0, 'RollMinPeriod': 11.0, 'LXlim': 29.0, 'LXlim_BigBar': 10.0, 'SXlim': 30.0, 'HighThreshold': 14.0, 'LowThreshold': 14.0, 'HL_Long': 10.0, 'HL_Short': 10.0, 'BigBar': 17.0}
2025-06-30 22:59:30,353 - gui.metrics_calculator - INFO - Calculated metrics for 789 trades
2025-06-30 22:59:30,381 - __main__ - INFO - Calculated returns for 80 months from 2018-11 to 2025-06
2025-06-30 22:59:30,392 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-06-30 23:25:05,908 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-07-01 01:46:06,640 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 01:46:06,692 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 01:46:06,726 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 01:46:06,736 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 01:46:06,797 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 01:46:06,802 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 01:46:06,805 - __main__ - INFO - Found 2 strategies
2025-07-01 01:46:06,813 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 01:46:26,120 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 01:46:26,593 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 01:46:26,635 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 01:46:32,453 - __main__ - INFO - Starting optimization...
2025-07-01 01:46:32,464 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 01:46:32,473 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 01:46:32,474 - gui.strategy_manager - INFO - Creating walk-forward windows: train=12 days, test=3 days
2025-07-01 01:46:32,475 - gui.strategy_manager - INFO - Minimum bars: train=827, test=330
2025-07-01 01:46:32,475 - gui.strategy_manager - INFO - Window 1: Train 827 bars (1999-03-10 00:00:00 to 2002-06-24 00:00:00), Test 330 bars (2002-06-25 00:00:00 to 2003-10-14 00:00:00)
2025-07-01 01:46:32,477 - gui.strategy_manager - INFO - Window 2: Train 827 bars (2003-10-15 00:00:00 to 2007-01-29 00:00:00), Test 330 bars (2007-01-30 00:00:00 to 2008-05-20 00:00:00)
2025-07-01 01:46:32,478 - gui.strategy_manager - INFO - Window 3: Train 827 bars (2008-05-21 00:00:00 to 2011-08-30 00:00:00), Test 330 bars (2011-08-31 00:00:00 to 2012-12-21 00:00:00)
2025-07-01 01:46:32,482 - gui.strategy_manager - INFO - Window 4: Train 827 bars (2012-12-24 00:00:00 to 2016-04-07 00:00:00), Test 330 bars (2016-04-08 00:00:00 to 2017-07-28 00:00:00)
2025-07-01 01:46:32,483 - gui.strategy_manager - INFO - Window 5: Train 827 bars (2017-07-31 00:00:00 to 2020-11-09 00:00:00), Test 330 bars (2020-11-10 00:00:00 to 2022-03-03 00:00:00)
2025-07-01 01:46:32,485 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-07-01 01:47:18,476 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 01:47:18,718 - gui.theme_manager - INFO - Theme refreshed for all widgets
2025-07-01 01:47:18,750 - gui.theme_manager - INFO - Theme reset to default
2025-07-01 01:47:25,911 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 01:47:30,475 - __main__ - INFO - Starting optimization...
2025-07-01 01:47:30,497 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 01:47:30,501 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 01:47:30,512 - gui.strategy_manager - INFO - Creating walk-forward windows: train=12 days, test=3 days
2025-07-01 01:47:30,512 - gui.strategy_manager - INFO - Minimum bars: train=827, test=330
2025-07-01 01:47:30,513 - gui.strategy_manager - INFO - Window 1: Train 827 bars (1999-03-10 00:00:00 to 2002-06-24 00:00:00), Test 330 bars (2002-06-25 00:00:00 to 2003-10-14 00:00:00)
2025-07-01 01:47:30,520 - gui.strategy_manager - INFO - Window 2: Train 827 bars (2003-10-15 00:00:00 to 2007-01-29 00:00:00), Test 330 bars (2007-01-30 00:00:00 to 2008-05-20 00:00:00)
2025-07-01 01:47:30,522 - gui.strategy_manager - INFO - Window 3: Train 827 bars (2008-05-21 00:00:00 to 2011-08-30 00:00:00), Test 330 bars (2011-08-31 00:00:00 to 2012-12-21 00:00:00)
2025-07-01 01:47:30,523 - gui.strategy_manager - INFO - Window 4: Train 827 bars (2012-12-24 00:00:00 to 2016-04-07 00:00:00), Test 330 bars (2016-04-08 00:00:00 to 2017-07-28 00:00:00)
2025-07-01 01:47:30,523 - gui.strategy_manager - INFO - Window 5: Train 827 bars (2017-07-31 00:00:00 to 2020-11-09 00:00:00), Test 330 bars (2020-11-10 00:00:00 to 2022-03-03 00:00:00)
2025-07-01 01:47:30,525 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-07-01 01:49:04,136 - __main__ - INFO - Timeframe changed to: Last 5 Years
2025-07-01 01:49:04,257 - __main__ - INFO - Applied timeframe 'Last 5 Years': 2020-06-28 to 2025-06-27
2025-07-01 01:49:56,689 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-07-01 01:50:48,125 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 01:50:48,136 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 01:50:48,142 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 01:50:48,151 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 01:50:48,261 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 01:50:48,270 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 01:50:48,273 - __main__ - INFO - Found 2 strategies
2025-07-01 01:50:48,279 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 01:50:55,921 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 01:50:56,347 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 01:50:56,363 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 01:51:05,571 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 01:51:05,578 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 01:51:20,513 - __main__ - INFO - Starting optimization...
2025-07-01 01:51:20,530 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:51:20,539 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 01:51:20,542 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 01:51:20,543 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 01:51:20,544 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 01:51:20,545 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 01:51:20,545 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 01:51:20,546 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 01:51:34,587 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -4.4421
2025-07-01 01:51:34,588 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -4.4421
2025-07-01 01:51:34,600 - __main__ - INFO - Optimization completed. Best performance: -4.4421
2025-07-01 01:51:34,609 - __main__ - INFO - Optimized parameters: {'LE': 7, 'SE': 17, 'LX': 6, 'SX': 2, 'ADXPeriod': 13}
2025-07-01 01:51:34,617 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 01:51:34,722 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 01:51:34,732 - __main__ - INFO - Updated parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 01:51:34,748 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:51:35,633 - gui.metrics_calculator - INFO - Calculated metrics for 67 trades
2025-07-01 01:51:35,649 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:51:35,671 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:51:35,681 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 01:51:35,696 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 01:51:54,042 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:51:54,054 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:51:54,062 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 01:52:32,364 - __main__ - INFO - Starting optimization...
2025-07-01 01:52:32,379 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:52:32,386 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 01:52:32,387 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 01:52:32,387 - gui.strategy_manager - INFO - Creating walk-forward windows: train=60 days, test=20 days
2025-07-01 01:52:32,388 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 01:52:32,388 - gui.strategy_manager - INFO - Window 1: Train 100 bars (2023-06-28 00:00:00 to 2023-11-16 00:00:00), Test 25 bars (2023-11-17 00:00:00 to 2023-12-22 00:00:00)
2025-07-01 01:52:32,389 - gui.strategy_manager - INFO - Window 2: Train 100 bars (2023-12-26 00:00:00 to 2024-05-17 00:00:00), Test 25 bars (2024-05-20 00:00:00 to 2024-06-25 00:00:00)
2025-07-01 01:52:32,391 - gui.strategy_manager - INFO - Window 3: Train 100 bars (2024-06-26 00:00:00 to 2024-11-14 00:00:00), Test 25 bars (2024-11-15 00:00:00 to 2024-12-20 00:00:00)
2025-07-01 01:52:32,391 - gui.strategy_manager - INFO - Window 4: Train 100 bars (2024-12-23 00:00:00 to 2025-05-19 00:00:00), Test 25 bars (2025-05-20 00:00:00 to 2025-06-25 00:00:00)
2025-07-01 01:52:32,392 - gui.strategy_manager - INFO - Walk-forward optimization: Created 4 windows
2025-07-01 01:52:48,917 - gui.strategy_manager - INFO - Walk-forward completed: 4 windows, avg performance: -499993.6800
2025-07-01 01:52:48,917 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -499993.6800
2025-07-01 01:52:48,928 - __main__ - INFO - Optimization completed. Best performance: -499993.6800
2025-07-01 01:52:48,935 - __main__ - INFO - Optimized parameters: {'LE': 2, 'SE': 4, 'LX': 18, 'SX': 12, 'ADXPeriod': 19}
2025-07-01 01:52:48,940 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 01:52:49,045 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 2.0, 'SE': 4.0, 'LX': 18.0, 'SX': 12.0, 'ADXPeriod': 19.0}
2025-07-01 01:52:49,053 - __main__ - INFO - Updated parameters: {'LE': 2.0, 'SE': 4.0, 'LX': 18.0, 'SX': 12.0, 'ADXPeriod': 19.0}
2025-07-01 01:52:49,062 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:52:49,584 - gui.metrics_calculator - INFO - Calculated metrics for 15 trades
2025-07-01 01:52:49,595 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:52:49,606 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 01:52:49,612 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 01:52:49,622 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 01:53:00,925 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame6.!frame2.!text"
2025-07-01 02:03:32,882 - gui.strategy_results_logger - INFO - Created strategy log directory: strategy_logs
2025-07-01 02:03:33,148 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:03:33,189 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:03:33,268 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:03:33,327 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:03:33,378 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:03:33,447 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:03:33,489 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:03:33,535 - __main__ - INFO - Found 2 strategies
2025-07-01 02:03:33,555 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:07:06,630 - gui.strategy_results_viewer - ERROR - Error refreshing results: Invalid column index data
2025-07-01 02:07:13,414 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:07:13,424 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:07:13,429 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:07:13,434 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:07:13,478 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:07:13,484 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:07:13,488 - __main__ - INFO - Found 2 strategies
2025-07-01 02:07:13,494 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:07:22,117 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:07:22,632 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:07:22,646 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 02:07:35,509 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 02:07:35,517 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 02:07:39,372 - __main__ - INFO - Starting optimization...
2025-07-01 02:07:39,390 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:07:39,426 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:07:39,427 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:07:39,428 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:07:39,431 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 02:07:39,432 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 02:07:39,433 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 02:07:39,437 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 02:08:02,740 - gui.strategy_results_viewer - ERROR - Error refreshing results: Column #11 out of range
2025-07-01 02:08:08,649 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:08:08,658 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:08:08,662 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:08:08,666 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:08:08,708 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:08:08,715 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:08:08,717 - __main__ - INFO - Found 2 strategies
2025-07-01 02:08:08,723 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:08:14,842 - gui.strategy_results_viewer - ERROR - Error handling result selection: Column #11 out of range
2025-07-01 02:08:18,515 - gui.strategy_results_viewer - ERROR - Error handling result selection: Column #11 out of range
2025-07-01 02:08:20,018 - gui.strategy_results_viewer - ERROR - Error handling result selection: Column #11 out of range
2025-07-01 02:08:47,442 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:08:47,912 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:08:47,922 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 02:08:53,748 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 02:08:53,749 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 02:08:57,555 - __main__ - INFO - Starting optimization...
2025-07-01 02:08:57,561 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:08:57,562 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:08:57,562 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:08:57,563 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:08:57,564 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 02:08:57,565 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 02:08:57,566 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 02:08:57,567 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 02:09:19,539 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -4.4421
2025-07-01 02:09:19,539 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -4.4421
2025-07-01 02:09:19,541 - __main__ - INFO - Optimization completed. Best performance: -4.4421
2025-07-01 02:09:19,541 - __main__ - INFO - Optimized parameters: {'LE': 7, 'SE': 17, 'LX': 6, 'SX': 2, 'ADXPeriod': 13}
2025-07-01 02:09:19,541 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:09:19,647 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 02:09:19,648 - __main__ - INFO - Updated parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 02:09:19,656 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:09:20,607 - gui.metrics_calculator - INFO - Calculated metrics for 67 trades
2025-07-01 02:09:20,617 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:09:20,675 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:09:20,704 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 02:09:20,732 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:09:20,734 - __main__ - ERROR - Error logging strategy results: 'DoubleVar' object is not subscriptable
2025-07-01 02:09:20,735 - __main__ - INFO - Error logging results: 'DoubleVar' object is not subscriptable
2025-07-01 02:09:40,844 - gui.strategy_results_viewer - INFO - Refreshed results table with 2 entries
2025-07-01 02:09:40,864 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:09:40,886 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:09:40,898 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:09:40,904 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:09:40,951 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:09:40,957 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:09:40,962 - __main__ - INFO - Found 2 strategies
2025-07-01 02:09:40,969 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:09:46,110 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame7.!frame2.!text"
2025-07-01 02:10:15,106 - gui.strategy_results_viewer - INFO - Refreshed results table with 2 entries
2025-07-01 02:10:15,113 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:10:15,121 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:10:15,125 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:10:15,132 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:10:15,177 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:10:15,183 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:10:15,186 - __main__ - INFO - Found 2 strategies
2025-07-01 02:10:15,192 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:10:34,599 - gui.strategy_results_viewer - INFO - Refreshed results table with 2 entries
2025-07-01 02:11:59,945 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:12:06,595 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:12:07,022 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:12:07,035 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 02:12:17,566 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 02:12:17,574 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 02:12:27,140 - __main__ - INFO - Starting optimization...
2025-07-01 02:12:27,155 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:12:27,163 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:12:27,164 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:12:28,583 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 502 bars
2025-07-01 02:13:18,884 - gui.strategy_manager - INFO - Bayesian optimization completed: 50 evaluations
2025-07-01 02:13:18,885 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 176.0555
2025-07-01 02:13:18,897 - __main__ - INFO - Optimization completed. Best performance: 176.0555
2025-07-01 02:13:18,905 - __main__ - INFO - Optimized parameters: {'LE': 18, 'SE': 4, 'LX': 1, 'SX': 13, 'ADXPeriod': 10}
2025-07-01 02:13:18,911 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:13:19,013 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 18.0, 'SE': 4.0, 'LX': 1.0, 'SX': 13.0, 'ADXPeriod': 10.0}
2025-07-01 02:13:19,023 - __main__ - INFO - Updated parameters: {'LE': 18.0, 'SE': 4.0, 'LX': 1.0, 'SX': 13.0, 'ADXPeriod': 10.0}
2025-07-01 02:13:19,033 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:13:19,749 - gui.metrics_calculator - INFO - Calculated metrics for 82 trades
2025-07-01 02:13:19,759 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:13:19,775 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:13:19,782 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 02:13:19,795 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:13:19,795 - __main__ - ERROR - Error logging strategy results: 'DoubleVar' object is not subscriptable
2025-07-01 02:13:19,803 - __main__ - INFO - Error logging results: 'DoubleVar' object is not subscriptable
2025-07-01 02:14:10,997 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:14:11,010 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:14:11,016 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 02:14:22,917 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:14:24,189 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:15:19,187 - __main__ - INFO - Timeframe changed to: Last 5 Years
2025-07-01 02:15:19,196 - __main__ - INFO - Applied timeframe 'Last 5 Years': 2020-06-28 to 2025-06-27
2025-07-01 02:15:26,522 - __main__ - INFO - Starting optimization...
2025-07-01 02:15:26,539 - __main__ - INFO - Using date range 2020-06-28 to 2025-06-27: 1256 bars
2025-07-01 02:15:26,546 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:15:26,546 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:15:26,551 - gui.strategy_manager - INFO - Whole period Bayesian optimization: 50 evaluations on 1256 bars
2025-07-01 02:17:51,505 - __main__ - INFO - Starting optimization...
2025-07-01 02:17:51,605 - __main__ - INFO - Using date range 2020-06-28 to 2025-06-27: 1256 bars
2025-07-01 02:17:51,700 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:17:51,705 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:17:51,735 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:17:51,742 - gui.strategy_manager - INFO - Minimum bars: train=157, test=62
2025-07-01 02:17:51,750 - gui.strategy_manager - INFO - Window 1: Train 250 bars (2020-06-29 00:00:00 to 2021-06-24 00:00:00), Test 62 bars (2021-06-25 00:00:00 to 2021-09-22 00:00:00)
2025-07-01 02:17:51,756 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2021-09-23 00:00:00 to 2022-09-16 00:00:00), Test 64 bars (2022-09-19 00:00:00 to 2022-12-16 00:00:00)
2025-07-01 02:17:51,764 - gui.strategy_manager - INFO - Window 3: Train 249 bars (2022-12-19 00:00:00 to 2023-12-14 00:00:00), Test 62 bars (2023-12-15 00:00:00 to 2024-03-15 00:00:00)
2025-07-01 02:17:51,779 - gui.strategy_manager - INFO - Window 4: Train 248 bars (2024-03-18 00:00:00 to 2025-03-13 00:00:00), Test 62 bars (2025-03-14 00:00:00 to 2025-06-11 00:00:00)
2025-07-01 02:17:51,781 - gui.strategy_manager - INFO - Walk-forward optimization: Created 4 windows
2025-07-01 02:19:18,332 - gui.strategy_manager - INFO - Walk-forward completed: 4 windows, avg performance: 16.5966
2025-07-01 02:19:18,335 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 16.5966
2025-07-01 02:19:18,344 - __main__ - INFO - Optimization completed. Best performance: 16.5966
2025-07-01 02:19:18,350 - __main__ - INFO - Optimized parameters: {'LE': 11, 'SE': 7, 'LX': 17, 'SX': 5, 'ADXPeriod': 13}
2025-07-01 02:19:18,352 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:19:18,464 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 11.0, 'SE': 7.0, 'LX': 17.0, 'SX': 5.0, 'ADXPeriod': 13.0}
2025-07-01 02:19:18,468 - __main__ - INFO - Updated parameters: {'LE': 11.0, 'SE': 7.0, 'LX': 17.0, 'SX': 5.0, 'ADXPeriod': 13.0}
2025-07-01 02:19:18,477 - __main__ - INFO - Using date range 2020-06-28 to 2025-06-27: 1256 bars
2025-07-01 02:19:25,441 - gui.metrics_calculator - INFO - Calculated metrics for 298 trades
2025-07-01 02:19:25,833 - __main__ - INFO - Using date range 2020-06-28 to 2025-06-27: 1256 bars
2025-07-01 02:19:26,283 - __main__ - INFO - Using date range 2020-06-28 to 2025-06-27: 1256 bars
2025-07-01 02:19:26,316 - __main__ - INFO - Calculated returns for 61 months from 2020-06 to 2025-06
2025-07-01 02:19:26,534 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:19:26,535 - __main__ - ERROR - Error logging strategy results: 'DoubleVar' object is not subscriptable
2025-07-01 02:19:26,538 - __main__ - INFO - Error logging results: 'DoubleVar' object is not subscriptable
2025-07-01 02:19:37,406 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:19:49,921 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame7.!frame2.!text"
2025-07-01 02:22:40,574 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:22:40,590 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:22:40,601 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:22:40,606 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:22:40,612 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:22:40,658 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:22:40,665 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:22:40,669 - __main__ - INFO - Found 2 strategies
2025-07-01 02:22:40,675 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:22:47,082 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:22:47,521 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:22:47,561 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 02:22:53,047 - __main__ - INFO - Timeframe changed to: Last 5 Years
2025-07-01 02:22:53,055 - __main__ - INFO - Applied timeframe 'Last 5 Years': 2020-06-28 to 2025-06-27
2025-07-01 02:23:01,030 - __main__ - INFO - Starting optimization...
2025-07-01 02:23:01,049 - __main__ - INFO - Using date range 2020-06-28 to 2025-06-27: 1256 bars
2025-07-01 02:23:01,056 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:23:01,057 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:23:01,058 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:23:01,061 - gui.strategy_manager - INFO - Minimum bars: train=157, test=62
2025-07-01 02:23:01,061 - gui.strategy_manager - INFO - Window 1: Train 250 bars (2020-06-29 00:00:00 to 2021-06-24 00:00:00), Test 62 bars (2021-06-25 00:00:00 to 2021-09-22 00:00:00)
2025-07-01 02:23:01,062 - gui.strategy_manager - INFO - Window 2: Train 248 bars (2021-09-23 00:00:00 to 2022-09-16 00:00:00), Test 64 bars (2022-09-19 00:00:00 to 2022-12-16 00:00:00)
2025-07-01 02:23:01,062 - gui.strategy_manager - INFO - Window 3: Train 249 bars (2022-12-19 00:00:00 to 2023-12-14 00:00:00), Test 62 bars (2023-12-15 00:00:00 to 2024-03-15 00:00:00)
2025-07-01 02:23:01,064 - gui.strategy_manager - INFO - Window 4: Train 248 bars (2024-03-18 00:00:00 to 2025-03-13 00:00:00), Test 62 bars (2025-03-14 00:00:00 to 2025-06-11 00:00:00)
2025-07-01 02:23:01,065 - gui.strategy_manager - INFO - Walk-forward optimization: Created 4 windows
2025-07-01 02:23:38,399 - gui.strategy_manager - INFO - Walk-forward completed: 4 windows, avg performance: 11.4765
2025-07-01 02:23:38,399 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 11.4765
2025-07-01 02:23:38,413 - __main__ - INFO - Optimization completed. Best performance: 11.4765
2025-07-01 02:23:38,426 - __main__ - INFO - Optimized parameters: {'LE': 16, 'SE': 10, 'LX': 17, 'SX': 18, 'ADXPeriod': 30}
2025-07-01 02:23:38,434 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:23:38,548 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 16.0, 'SE': 10.0, 'LX': 17.0, 'SX': 18.0, 'ADXPeriod': 30.0}
2025-07-01 02:23:38,584 - __main__ - INFO - Updated parameters: {'LE': 16.0, 'SE': 10.0, 'LX': 17.0, 'SX': 18.0, 'ADXPeriod': 30.0}
2025-07-01 02:23:38,610 - __main__ - INFO - Using date range 2020-06-28 to 2025-06-27: 1256 bars
2025-07-01 02:23:53,558 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:23:53,562 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:23:53,572 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:23:53,576 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:23:53,581 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:23:53,630 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:23:53,639 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:23:53,641 - __main__ - INFO - Found 2 strategies
2025-07-01 02:23:53,646 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:24:07,612 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:24:08,404 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:24:08,454 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 02:24:14,313 - __main__ - INFO - Timeframe changed to: Last 5 Years
2025-07-01 02:24:14,319 - __main__ - INFO - Applied timeframe 'Last 5 Years': 2020-06-28 to 2025-06-27
2025-07-01 02:24:19,673 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 02:24:19,682 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 02:24:24,849 - __main__ - INFO - Starting optimization...
2025-07-01 02:24:24,865 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:24:24,872 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:24:24,873 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:24:24,873 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:24:24,874 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 02:24:24,874 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 02:24:24,880 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 02:24:24,881 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 02:24:41,099 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -4.4421
2025-07-01 02:24:41,100 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -4.4421
2025-07-01 02:24:41,109 - __main__ - INFO - Optimization completed. Best performance: -4.4421
2025-07-01 02:24:41,119 - __main__ - INFO - Optimized parameters: {'LE': 7, 'SE': 17, 'LX': 6, 'SX': 2, 'ADXPeriod': 13}
2025-07-01 02:24:41,125 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:24:41,231 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 02:24:41,257 - __main__ - INFO - Updated parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 02:24:41,271 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:24:41,849 - gui.metrics_calculator - INFO - Calculated metrics for 67 trades
2025-07-01 02:24:41,860 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:24:41,874 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:24:41,881 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 02:24:41,894 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:24:41,900 - __main__ - INFO - Starting strategy results logging...
2025-07-01 02:24:41,906 - __main__ - INFO - Strategy: 4BarCompare, Market: Unknown, File: Unknown
2025-07-01 02:24:41,907 - __main__ - ERROR - Error logging strategy results: 'DoubleVar' object is not subscriptable
2025-07-01 02:24:41,912 - __main__ - INFO - Error logging results: 'DoubleVar' object is not subscriptable
2025-07-01 02:25:00,190 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:25:10,545 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame7.!frame2.!text"
2025-07-01 02:25:23,526 - gui.strategy_results_viewer - INFO - Refreshed results table with 0 entries
2025-07-01 02:25:23,532 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:25:23,538 - PyTS_GUI_Refactored - INFO - PyTS GUI initialized successfully
2025-07-01 02:25:23,542 - PyTS_GUI_Refactored - INFO - Loading strategy: 4BarCompare
2025-07-01 02:25:23,549 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:25:23,580 - PyTS_GUI_Refactored - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:25:23,585 - PyTS_GUI_Refactored - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:25:23,589 - PyTS_GUI_Refactored - INFO - Found 2 strategies
2025-07-01 02:25:23,596 - PyTS_GUI_Refactored - INFO - Starting strategy results logging...
2025-07-01 02:25:23,602 - PyTS_GUI_Refactored - INFO - Strategy: TestStrategy, Market: TEST, File: test_data.csv
2025-07-01 02:25:23,607 - PyTS_GUI_Refactored - INFO - Calling results logger with 4 parameters...
2025-07-01 02:25:23,618 - gui.strategy_results_logger - INFO - Logged strategy run: TestStrategy_20250701_022523
2025-07-01 02:25:23,624 - PyTS_GUI_Refactored - INFO - Results logger returned run_id: TestStrategy_20250701_022523
2025-07-01 02:25:23,632 - PyTS_GUI_Refactored - INFO - Strategy results logged: TestStrategy_20250701_022523
2025-07-01 02:25:23,637 - gui.strategy_results_viewer - INFO - Refreshed results table with 1 entries
2025-07-01 02:25:23,640 - gui.strategy_results_viewer - INFO - Refreshed results table with 1 entries
2025-07-01 02:28:33,578 - gui.strategy_results_viewer - INFO - Refreshed results table with 1 entries
2025-07-01 02:28:33,623 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:28:33,674 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:28:33,679 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:28:33,685 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:28:33,719 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:28:33,723 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:28:33,725 - __main__ - INFO - Found 2 strategies
2025-07-01 02:28:33,730 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:28:39,422 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:28:39,859 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:28:39,885 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 02:28:44,481 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 02:28:44,490 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 02:28:48,758 - __main__ - INFO - Starting optimization...
2025-07-01 02:28:48,776 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:28:48,784 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:28:48,784 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:28:48,787 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:28:48,789 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 02:28:48,789 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 02:28:48,790 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 02:28:48,791 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 02:29:03,395 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -4.4421
2025-07-01 02:29:03,396 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -4.4421
2025-07-01 02:29:03,402 - __main__ - INFO - Optimization completed. Best performance: -4.4421
2025-07-01 02:29:03,411 - __main__ - INFO - Optimized parameters: {'LE': 7, 'SE': 17, 'LX': 6, 'SX': 2, 'ADXPeriod': 13}
2025-07-01 02:29:03,417 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:29:03,525 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 02:29:03,546 - __main__ - INFO - Updated parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 02:29:03,558 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:29:04,182 - gui.metrics_calculator - INFO - Calculated metrics for 67 trades
2025-07-01 02:29:04,193 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:29:04,204 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:29:04,211 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 02:29:04,222 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:29:04,229 - __main__ - INFO - Starting strategy results logging...
2025-07-01 02:29:04,237 - __main__ - INFO - Strategy: 4BarCompare, Market: QQQ, File: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:29:04,247 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 02:29:04,254 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 02:29:04,263 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 02:29:04,271 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 02:29:04,281 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 02:29:04,286 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 02:29:04,301 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_022904
2025-07-01 02:29:04,307 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_022904
2025-07-01 02:29:04,314 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_022904
2025-07-01 02:29:04,319 - gui.strategy_results_viewer - INFO - Refreshed results table with 2 entries
2025-07-01 02:36:17,697 - gui.strategy_results_viewer - INFO - Refreshed results table with 2 entries
2025-07-01 02:36:17,707 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:36:17,756 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:36:17,771 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:36:17,776 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:36:17,835 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:36:17,842 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:36:17,846 - __main__ - INFO - Found 2 strategies
2025-07-01 02:36:17,853 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:36:22,839 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:36:23,219 - gui.data_manager - INFO - Successfully loaded 6617 bars from C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:36:23,237 - __main__ - INFO - Successfully loaded 6617 bars
2025-07-01 02:36:27,092 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 02:36:27,100 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 02:36:31,066 - __main__ - INFO - Starting optimization...
2025-07-01 02:36:31,080 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:36:31,092 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:36:31,094 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:36:31,096 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:36:31,096 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 02:36:31,097 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 02:36:31,098 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 02:36:31,098 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 02:36:45,711 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -4.4421
2025-07-01 02:36:45,711 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -4.4421
2025-07-01 02:36:45,717 - __main__ - INFO - Optimization completed. Best performance: -4.4421
2025-07-01 02:36:45,726 - __main__ - INFO - Optimized parameters: {'LE': 7, 'SE': 17, 'LX': 6, 'SX': 2, 'ADXPeriod': 13}
2025-07-01 02:36:45,733 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:36:45,846 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 02:36:45,864 - __main__ - INFO - Updated parameters: {'LE': 7.0, 'SE': 17.0, 'LX': 6.0, 'SX': 2.0, 'ADXPeriod': 13.0}
2025-07-01 02:36:45,876 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:36:46,515 - gui.metrics_calculator - INFO - Calculated metrics for 67 trades
2025-07-01 02:36:46,525 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:36:46,535 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 02:36:46,542 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 02:36:46,552 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:36:46,557 - __main__ - INFO - Starting strategy results logging...
2025-07-01 02:36:46,569 - __main__ - INFO - Strategy: 4BarCompare, Market: QQQ, File: C:/Users/<USER>/Desktop/Data1/ETF/QQQ.csv
2025-07-01 02:36:46,578 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 02:36:46,585 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 02:36:46,594 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 02:36:46,601 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 02:36:46,612 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 02:36:46,617 - __main__ - INFO - Found 67 trades for logging
2025-07-01 02:36:46,620 - __main__ - INFO - Include trades setting: False
2025-07-01 02:36:46,627 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 02:36:46,637 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_023646
2025-07-01 02:36:46,646 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_023646
2025-07-01 02:36:46,651 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_023646
2025-07-01 02:36:46,658 - gui.strategy_results_viewer - INFO - Refreshed results table with 3 entries
2025-07-01 02:37:48,842 - gui.strategy_results_viewer - INFO - Refreshed results table with 3 entries
2025-07-01 02:37:48,850 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 02:37:48,905 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 02:37:48,948 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 02:37:48,953 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 02:37:49,003 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 02:37:49,009 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 02:37:49,012 - __main__ - INFO - Found 2 strategies
2025-07-01 02:37:49,017 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 02:37:53,868 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-07-01 02:37:54,192 - gui.data_manager - INFO - Successfully loaded 5184 bars from C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-07-01 02:37:54,217 - __main__ - INFO - Successfully loaded 5184 bars
2025-07-01 02:37:59,221 - __main__ - INFO - Timeframe changed to: Last 3 Years
2025-07-01 02:37:59,230 - __main__ - INFO - Applied timeframe 'Last 3 Years': 2022-06-28 to 2025-06-27
2025-07-01 02:38:04,540 - __main__ - INFO - Starting optimization...
2025-07-01 02:38:04,559 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:38:04,569 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:38:04,570 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:38:04,570 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:38:04,571 - gui.strategy_manager - INFO - Minimum bars: train=100, test=37
2025-07-01 02:38:04,572 - gui.strategy_manager - INFO - Window 1: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 62 bars (2023-06-26 00:00:00 to 2023-09-21 00:00:00)
2025-07-01 02:38:04,572 - gui.strategy_manager - INFO - Window 2: Train 247 bars (2023-09-22 00:00:00 to 2024-09-16 00:00:00), Test 63 bars (2024-09-17 00:00:00 to 2024-12-13 00:00:00)
2025-07-01 02:38:04,573 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 02:38:20,305 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -0.9300
2025-07-01 02:38:20,305 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -0.9300
2025-07-01 02:38:20,311 - __main__ - INFO - Optimization completed. Best performance: -0.9300
2025-07-01 02:38:20,320 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 17, 'LX': 1, 'SX': 4, 'ADXPeriod': 28}
2025-07-01 02:38:20,325 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:38:20,435 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 17.0, 'LX': 1.0, 'SX': 4.0, 'ADXPeriod': 28.0}
2025-07-01 02:38:20,443 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 17.0, 'LX': 1.0, 'SX': 4.0, 'ADXPeriod': 28.0}
2025-07-01 02:38:20,455 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:38:22,500 - gui.metrics_calculator - INFO - Calculated metrics for 53 trades
2025-07-01 02:38:22,608 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:38:22,626 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:38:22,635 - __main__ - INFO - Calculated returns for 37 months from 2022-06 to 2025-06
2025-07-01 02:38:22,672 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:38:22,680 - __main__ - INFO - Starting strategy results logging...
2025-07-01 02:38:22,692 - __main__ - INFO - Strategy: 4BarCompare, Market: GLD, File: C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-07-01 02:38:22,702 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 02:38:22,714 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 02:38:22,725 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 02:38:22,735 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 02:38:22,746 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 02:38:22,750 - __main__ - INFO - Found 53 trades for logging
2025-07-01 02:38:22,756 - __main__ - INFO - Include trades setting: True
2025-07-01 02:38:22,763 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 02:38:22,786 - gui.strategy_results_logger - INFO - Saved 53 trades to CSV for run 4BarCompare_20250701_023822
2025-07-01 02:38:22,791 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_023822
2025-07-01 02:38:22,799 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_023822
2025-07-01 02:38:22,813 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_023822
2025-07-01 02:38:22,826 - gui.strategy_results_viewer - INFO - Refreshed results table with 4 entries
2025-07-01 02:38:44,007 - gui.strategy_results_logger - INFO - Exported 53 trades to: C:/Users/<USER>/Desktop/trade.csv
2025-07-01 02:40:58,831 - __main__ - INFO - Starting optimization...
2025-07-01 02:40:58,841 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:40:58,847 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:40:58,847 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:40:58,848 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:40:58,848 - gui.strategy_manager - INFO - Minimum bars: train=100, test=37
2025-07-01 02:40:58,849 - gui.strategy_manager - INFO - Window 1: Train 249 bars (2022-06-28 00:00:00 to 2023-06-23 00:00:00), Test 62 bars (2023-06-26 00:00:00 to 2023-09-21 00:00:00)
2025-07-01 02:40:58,850 - gui.strategy_manager - INFO - Window 2: Train 247 bars (2023-09-22 00:00:00 to 2024-09-16 00:00:00), Test 63 bars (2024-09-17 00:00:00 to 2024-12-13 00:00:00)
2025-07-01 02:40:58,850 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 02:41:03,028 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:41:03,068 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:41:03,074 - __main__ - INFO - Calculated returns for 37 months from 2022-06 to 2025-06
2025-07-01 02:41:11,617 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -0.9300
2025-07-01 02:41:11,618 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -0.9300
2025-07-01 02:41:11,627 - __main__ - INFO - Optimization completed. Best performance: -0.9300
2025-07-01 02:41:11,634 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 17, 'LX': 1, 'SX': 4, 'ADXPeriod': 28}
2025-07-01 02:41:11,639 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:41:11,747 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 17.0, 'LX': 1.0, 'SX': 4.0, 'ADXPeriod': 28.0}
2025-07-01 02:41:11,768 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 17.0, 'LX': 1.0, 'SX': 4.0, 'ADXPeriod': 28.0}
2025-07-01 02:41:11,783 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:41:12,880 - gui.metrics_calculator - INFO - Calculated metrics for 53 trades
2025-07-01 02:41:12,889 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:41:12,899 - __main__ - INFO - Using date range 2022-06-28 to 2025-06-27: 753 bars
2025-07-01 02:41:12,905 - __main__ - INFO - Calculated returns for 37 months from 2022-06 to 2025-06
2025-07-01 02:41:12,915 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:41:12,920 - __main__ - INFO - Starting strategy results logging...
2025-07-01 02:41:12,928 - __main__ - INFO - Strategy: 4BarCompare, Market: GLD, File: C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-07-01 02:41:12,934 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 02:41:12,942 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 02:41:12,948 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 02:41:12,955 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 02:41:12,963 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 02:41:12,966 - __main__ - INFO - Found 53 trades for logging
2025-07-01 02:41:12,969 - __main__ - INFO - Include trades setting: True
2025-07-01 02:41:12,974 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 02:41:12,991 - gui.strategy_results_logger - INFO - Saved 53 trades to CSV for run 4BarCompare_20250701_024112
2025-07-01 02:41:12,992 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_024112
2025-07-01 02:41:12,997 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_024112
2025-07-01 02:41:13,003 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_024112
2025-07-01 02:41:13,009 - gui.strategy_results_viewer - INFO - Refreshed results table with 5 entries
2025-07-01 02:41:27,864 - __main__ - INFO - Starting optimization...
2025-07-01 02:41:27,874 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 02:41:27,875 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 02:41:27,875 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 02:41:27,876 - gui.strategy_manager - INFO - Minimum bars: train=648, test=259
2025-07-01 02:41:27,876 - gui.strategy_manager - INFO - Window 1: Train 648 bars (2004-11-18 00:00:00 to 2007-06-18 00:00:00), Test 259 bars (2007-06-19 00:00:00 to 2008-06-26 00:00:00)
2025-07-01 02:41:27,877 - gui.strategy_manager - INFO - Window 2: Train 648 bars (2008-06-27 00:00:00 to 2011-01-21 00:00:00), Test 259 bars (2011-01-24 00:00:00 to 2012-02-01 00:00:00)
2025-07-01 02:41:27,877 - gui.strategy_manager - INFO - Window 3: Train 648 bars (2012-02-02 00:00:00 to 2014-08-29 00:00:00), Test 259 bars (2014-09-02 00:00:00 to 2015-09-10 00:00:00)
2025-07-01 02:41:27,878 - gui.strategy_manager - INFO - Window 4: Train 648 bars (2015-09-11 00:00:00 to 2018-04-09 00:00:00), Test 259 bars (2018-04-10 00:00:00 to 2019-04-18 00:00:00)
2025-07-01 02:41:27,879 - gui.strategy_manager - INFO - Window 5: Train 648 bars (2019-04-22 00:00:00 to 2021-11-11 00:00:00), Test 259 bars (2021-11-12 00:00:00 to 2022-11-22 00:00:00)
2025-07-01 02:41:27,879 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-07-01 02:43:15,125 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: -6.9340
2025-07-01 02:43:15,125 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -6.9340
2025-07-01 02:43:15,134 - __main__ - INFO - Optimization completed. Best performance: -6.9340
2025-07-01 02:43:15,141 - __main__ - INFO - Optimized parameters: {'LE': 16, 'SE': 3, 'LX': 18, 'SX': 12, 'ADXPeriod': 14}
2025-07-01 02:43:15,146 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 02:43:15,251 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 16.0, 'SE': 3.0, 'LX': 18.0, 'SX': 12.0, 'ADXPeriod': 14.0}
2025-07-01 02:43:15,272 - __main__ - INFO - Updated parameters: {'LE': 16.0, 'SE': 3.0, 'LX': 18.0, 'SX': 12.0, 'ADXPeriod': 14.0}
2025-07-01 02:44:09,248 - gui.metrics_calculator - INFO - Calculated metrics for 28 trades
2025-07-01 02:44:09,260 - __main__ - INFO - Calculated returns for 248 months from 2004-11 to 2025-06
2025-07-01 02:44:09,275 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 02:44:09,279 - __main__ - INFO - Starting strategy results logging...
2025-07-01 02:44:09,287 - __main__ - INFO - Strategy: 4BarCompare, Market: GLD, File: C:/Users/<USER>/Desktop/Data1/ETF/GLD.csv
2025-07-01 02:44:09,294 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 02:44:09,300 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 02:44:09,307 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 02:44:09,314 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 02:44:09,321 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 02:44:09,325 - __main__ - INFO - Found 28 trades for logging
2025-07-01 02:44:09,328 - __main__ - INFO - Include trades setting: True
2025-07-01 02:44:09,332 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 02:44:09,356 - gui.strategy_results_logger - INFO - Saved 28 trades to CSV for run 4BarCompare_20250701_024409
2025-07-01 02:44:09,356 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_024409
2025-07-01 02:44:09,362 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_024409
2025-07-01 02:44:09,367 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_024409
2025-07-01 02:44:09,373 - gui.strategy_results_viewer - INFO - Refreshed results table with 6 entries
2025-07-01 02:56:17,733 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame7.!frame2.!text"
2025-07-01 03:00:32,774 - gui.strategy_results_viewer - INFO - Refreshed results table with 6 entries
2025-07-01 03:00:32,788 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 03:00:32,795 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 03:00:32,800 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 03:00:32,805 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 03:00:32,851 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 03:00:32,857 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 03:00:32,860 - __main__ - INFO - Found 2 strategies
2025-07-01 03:00:32,865 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 03:00:54,037 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-07-01 03:00:54,537 - gui.data_manager - INFO - Successfully loaded 9499 bars from C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-07-01 03:00:54,552 - __main__ - INFO - Successfully loaded 9499 bars
2025-07-01 03:01:03,811 - __main__ - INFO - Starting optimization...
2025-07-01 03:01:03,822 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 03:01:03,822 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 03:01:03,824 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 03:01:03,828 - gui.strategy_manager - INFO - Minimum bars: train=1187, test=474
2025-07-01 03:01:03,829 - gui.strategy_manager - INFO - Window 1: Train 1187 bars (1986-12-31 00:00:00 to 1991-10-18 00:00:00), Test 474 bars (1991-10-21 00:00:00 to 1993-09-14 00:00:00)
2025-07-01 03:01:03,834 - gui.strategy_manager - INFO - Window 2: Train 1187 bars (1993-09-15 00:00:00 to 1998-07-02 00:00:00), Test 474 bars (1998-07-03 00:00:00 to 2000-06-01 00:00:00)
2025-07-01 03:01:03,837 - gui.strategy_manager - INFO - Window 3: Train 1187 bars (2000-06-02 00:00:00 to 2005-03-23 00:00:00), Test 474 bars (2005-03-24 00:00:00 to 2007-02-21 00:00:00)
2025-07-01 03:01:03,838 - gui.strategy_manager - INFO - Window 4: Train 1187 bars (2007-02-22 00:00:00 to 2011-12-09 00:00:00), Test 474 bars (2011-12-12 00:00:00 to 2013-11-18 00:00:00)
2025-07-01 03:01:03,839 - gui.strategy_manager - INFO - Window 5: Train 1187 bars (2013-11-19 00:00:00 to 2018-09-12 00:00:00), Test 474 bars (2018-09-13 00:00:00 to 2020-08-17 00:00:00)
2025-07-01 03:01:03,839 - gui.strategy_manager - INFO - Walk-forward optimization: Created 5 windows
2025-07-01 03:06:20,129 - gui.strategy_manager - INFO - Walk-forward completed: 5 windows, avg performance: 405.7450
2025-07-01 03:06:20,130 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: 405.7450
2025-07-01 03:06:20,139 - __main__ - INFO - Optimization completed. Best performance: 405.7450
2025-07-01 03:06:20,147 - __main__ - INFO - Optimized parameters: {'LE': 1, 'SE': 20, 'LX': 7, 'SX': 10, 'ADXPeriod': 14}
2025-07-01 03:06:20,152 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 03:06:20,258 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 1.0, 'SE': 20.0, 'LX': 7.0, 'SX': 10.0, 'ADXPeriod': 14.0}
2025-07-01 03:06:20,269 - __main__ - INFO - Updated parameters: {'LE': 1.0, 'SE': 20.0, 'LX': 7.0, 'SX': 10.0, 'ADXPeriod': 14.0}
2025-07-01 03:09:44,574 - gui.metrics_calculator - INFO - Calculated metrics for 1082 trades
2025-07-01 03:09:44,639 - __main__ - INFO - Calculated returns for 463 months from 1986-12 to 2025-06
2025-07-01 03:09:44,680 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 03:09:44,685 - __main__ - INFO - Starting strategy results logging...
2025-07-01 03:09:44,694 - __main__ - INFO - Strategy: 4BarCompare, Market: ^HSI, File: C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-07-01 03:09:44,703 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 03:09:44,709 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 03:09:44,717 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 03:09:44,727 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 03:09:44,737 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 03:09:44,743 - __main__ - INFO - Found 1082 trades for logging
2025-07-01 03:09:44,747 - __main__ - INFO - Include trades setting: True
2025-07-01 03:09:44,752 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 03:09:44,847 - gui.strategy_results_logger - INFO - Saved 1082 trades to CSV for run 4BarCompare_20250701_030944
2025-07-01 03:09:44,847 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_030944
2025-07-01 03:09:44,853 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_030944
2025-07-01 03:09:44,860 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_030944
2025-07-01 03:09:44,877 - gui.strategy_results_viewer - INFO - Refreshed results table with 7 entries
2025-07-01 03:10:37,833 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:42,442 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:45,681 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:46,504 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:47,163 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:47,852 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:51,442 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:53,945 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:54,899 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:56,768 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:57,304 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:10:58,913 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:11:20,897 - gui.strategy_results_logger - INFO - Exported results to: strategy_logs\trade.xlsx
2025-07-01 03:11:45,209 - gui.strategy_results_logger - INFO - Exported results to: strategy_logs\123.xlsx
2025-07-01 03:13:21,201 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:13:21,788 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:13:25,440 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:13:26,396 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:13:27,252 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:13:29,891 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:13:30,979 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:13:32,807 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame7.!frame2.!text"
2025-07-01 03:15:07,268 - gui.strategy_results_viewer - INFO - Refreshed results table with 8 entries
2025-07-01 03:15:07,310 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 03:15:07,352 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 03:15:07,358 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 03:15:07,364 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 03:15:07,408 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 03:15:07,414 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 03:15:07,417 - __main__ - INFO - Found 2 strategies
2025-07-01 03:15:07,424 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 03:15:33,922 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/SPY.csv
2025-07-01 03:15:34,372 - gui.data_manager - INFO - Successfully loaded 8159 bars from C:/Users/<USER>/Desktop/Data1/ETF/SPY.csv
2025-07-01 03:15:34,391 - __main__ - INFO - Successfully loaded 8159 bars
2025-07-01 03:15:39,489 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 03:15:39,495 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 03:15:46,279 - __main__ - INFO - Starting optimization...
2025-07-01 03:15:46,294 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 03:15:46,302 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 03:15:46,303 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 03:15:46,304 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 03:15:46,306 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 03:15:46,306 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 03:15:46,308 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 03:15:46,309 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 03:15:59,059 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -499992.9774
2025-07-01 03:15:59,060 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -499992.9774
2025-07-01 03:15:59,069 - __main__ - INFO - Optimization completed. Best performance: -499992.9774
2025-07-01 03:15:59,076 - __main__ - INFO - Optimized parameters: {'LE': 3, 'SE': 1, 'LX': 19, 'SX': 1, 'ADXPeriod': 29}
2025-07-01 03:15:59,081 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 03:15:59,182 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 3.0, 'SE': 1.0, 'LX': 19.0, 'SX': 1.0, 'ADXPeriod': 29.0}
2025-07-01 03:15:59,192 - __main__ - INFO - Updated parameters: {'LE': 3.0, 'SE': 1.0, 'LX': 19.0, 'SX': 1.0, 'ADXPeriod': 29.0}
2025-07-01 03:15:59,203 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 03:15:59,703 - gui.metrics_calculator - INFO - Calculated metrics for 20 trades
2025-07-01 03:15:59,712 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 03:15:59,726 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 03:15:59,731 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 03:15:59,744 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 03:15:59,748 - __main__ - INFO - Starting strategy results logging...
2025-07-01 03:15:59,757 - __main__ - INFO - Strategy: 4BarCompare, Market: SPY, File: C:/Users/<USER>/Desktop/Data1/ETF/SPY.csv
2025-07-01 03:15:59,764 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 03:15:59,771 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 03:15:59,778 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 03:15:59,786 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 03:15:59,794 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 03:15:59,797 - __main__ - INFO - Found 20 trades for logging
2025-07-01 03:15:59,800 - __main__ - INFO - Include trades setting: True
2025-07-01 03:15:59,806 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 03:15:59,877 - gui.strategy_results_logger - INFO - Saved 20 trades to CSV for run 4BarCompare_20250701_031559
2025-07-01 03:15:59,877 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_031559
2025-07-01 03:15:59,883 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_031559
2025-07-01 03:15:59,889 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_031559
2025-07-01 03:15:59,909 - gui.strategy_results_viewer - INFO - Refreshed results table with 9 entries
2025-07-01 03:16:05,649 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:06,181 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:06,728 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:07,934 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:09,233 - gui.strategy_results_viewer - INFO - Refreshed results table with 9 entries
2025-07-01 03:16:10,293 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:11,350 - gui.strategy_results_viewer - INFO - Refreshed results table with 9 entries
2025-07-01 03:16:12,390 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:15,566 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:20,260 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:20,557 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:28,766 - gui.strategy_results_viewer - ERROR - Error displaying result details: 'StrategyResultsViewer' object has no attribute 'trades_text'
2025-07-01 03:16:30,504 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame7.!frame2.!text"
2025-07-01 03:25:05,357 - gui.strategy_results_viewer - INFO - Refreshed results table with 9 entries
2025-07-01 03:25:05,431 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 03:25:05,476 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 03:25:05,486 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 03:25:05,512 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 03:25:05,559 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 03:25:05,565 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 03:25:05,568 - __main__ - INFO - Found 2 strategies
2025-07-01 03:25:05,575 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 03:25:10,929 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-07-01 03:25:11,467 - gui.data_manager - INFO - Successfully loaded 9499 bars from C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-07-01 03:25:11,478 - __main__ - INFO - Successfully loaded 9499 bars
2025-07-01 03:25:17,504 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 03:25:17,513 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 03:25:21,511 - __main__ - INFO - Starting optimization...
2025-07-01 03:25:21,528 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 491 bars
2025-07-01 03:25:21,536 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 03:25:21,536 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 03:25:21,537 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 03:25:21,537 - gui.strategy_manager - INFO - Minimum bars: train=100, test=24
2025-07-01 03:25:21,538 - gui.strategy_manager - INFO - Window 1: Train 242 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 62 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 03:25:21,538 - gui.strategy_manager - INFO - Window 2: Train 163 bars (2024-09-23 00:00:00 to 2025-05-26 00:00:00), Test 24 bars (2025-05-27 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 03:25:21,539 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 03:25:36,662 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -50.2451
2025-07-01 03:25:36,664 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -50.2451
2025-07-01 03:25:36,672 - __main__ - INFO - Optimization completed. Best performance: -50.2451
2025-07-01 03:25:36,681 - __main__ - INFO - Optimized parameters: {'LE': 20, 'SE': 1, 'LX': 20, 'SX': 20, 'ADXPeriod': 2}
2025-07-01 03:25:36,687 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 03:25:36,791 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 20.0, 'SE': 1.0, 'LX': 20.0, 'SX': 20.0, 'ADXPeriod': 2.0}
2025-07-01 03:25:36,800 - __main__ - INFO - Updated parameters: {'LE': 20.0, 'SE': 1.0, 'LX': 20.0, 'SX': 20.0, 'ADXPeriod': 2.0}
2025-07-01 03:25:36,813 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 491 bars
2025-07-01 03:25:37,454 - gui.metrics_calculator - INFO - Calculated metrics for 37 trades
2025-07-01 03:25:37,468 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 491 bars
2025-07-01 03:25:37,481 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 491 bars
2025-07-01 03:25:37,487 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 03:25:37,497 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 03:25:37,502 - __main__ - INFO - Starting strategy results logging...
2025-07-01 03:25:37,510 - __main__ - INFO - Strategy: 4BarCompare, Market: ^HSI, File: C:/Users/<USER>/Desktop/Data1/ETF/^HSI.csv
2025-07-01 03:25:37,519 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 03:25:37,527 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 03:25:37,537 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 03:25:37,544 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 03:25:37,555 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 03:25:37,558 - __main__ - INFO - Found 37 trades for logging
2025-07-01 03:25:37,561 - __main__ - INFO - Include trades setting: True
2025-07-01 03:25:37,570 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 03:25:37,656 - gui.strategy_results_logger - INFO - Saved 37 trades to CSV for run 4BarCompare_20250701_032537
2025-07-01 03:25:37,657 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_032537
2025-07-01 03:25:37,664 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_032537
2025-07-01 03:25:37,673 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_032537
2025-07-01 03:25:37,707 - gui.strategy_results_viewer - INFO - Refreshed results table with 10 entries
2025-07-01 03:26:21,489 - __main__ - INFO - Added 6 data files to bulk backtest
2025-07-01 03:26:37,336 - __main__ - INFO - Added 1 strategy files to bulk backtest
2025-07-01 03:26:40,952 - __main__ - INFO - Bulk backtest completed with 6 results
2025-07-01 03:29:39,595 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame8.!frame2.!text"
2025-07-01 03:36:38,935 - gui.theme_manager - WARNING - Could not apply theme to widget .!notebook.!frame6.!frame.!labelframe.!frame.!treeview: Layout Themed.Treeview.2208886213520 not found
2025-07-01 03:36:38,945 - gui.theme_manager - WARNING - Could not apply theme to widget .!notebook.!frame6.!frame.!labelframe2.!notebook.!frame4.!frame.!treeview: Layout Themed.Treeview.2208886214160 not found
2025-07-01 03:36:38,959 - gui.strategy_results_viewer - INFO - Refreshed results table with 10 entries
2025-07-01 03:36:38,983 - gui.theme_manager - WARNING - Could not apply theme to widget .!notebook.!frame7.!panedwindow.!labelframe2.!frame.!treeview: Layout Themed.Treeview.2208838873648 not found
2025-07-01 03:36:39,024 - gui.theme_manager - INFO - Dark theme configured successfully
2025-07-01 03:36:39,039 - gui.theme_manager - WARNING - Could not apply theme to widget .!notebook.!frame4.!panedwindow.!labelframe.!treeview: Layout Themed.Treeview.2208886227360 not found
2025-07-01 03:36:39,040 - gui.theme_manager - WARNING - Could not apply theme to widget .!notebook.!frame6.!frame.!labelframe.!frame.!treeview: Layout Themed.Treeview.2208886213520 not found
2025-07-01 03:36:39,042 - gui.theme_manager - WARNING - Could not apply theme to widget .!notebook.!frame6.!frame.!labelframe2.!notebook.!frame4.!frame.!treeview: Layout Themed.Treeview.2208886214160 not found
2025-07-01 03:36:39,059 - gui.theme_manager - WARNING - Could not apply theme to widget .!notebook.!frame7.!panedwindow.!labelframe2.!frame.!treeview: Layout Themed.Treeview.2208838873648 not found
2025-07-01 03:36:39,072 - __main__ - INFO - PyTS GUI initialized successfully
2025-07-01 03:36:39,089 - __main__ - INFO - Loading strategy: 4BarCompare
2025-07-01 03:36:39,095 - gui.strategy_manager - INFO - Successfully loaded strategy: 4BarCompare
2025-07-01 03:36:39,147 - __main__ - INFO - Loaded periods: 12 months train, 3 months test
2025-07-01 03:36:39,152 - __main__ - INFO - Strategy '4BarCompare' loaded successfully
2025-07-01 03:36:39,155 - __main__ - INFO - Found 2 strategies
2025-07-01 03:36:39,160 - __main__ - INFO - Starting PyTS - Professional Trading System v2.0.0
2025-07-01 03:36:54,791 - __main__ - INFO - Loading data from: C:/Users/<USER>/Desktop/Data1/ETF/^DJI.csv
2025-07-01 03:36:55,503 - gui.data_manager - INFO - Successfully loaded 8432 bars from C:/Users/<USER>/Desktop/Data1/ETF/^DJI.csv
2025-07-01 03:36:55,522 - __main__ - INFO - Successfully loaded 8432 bars
2025-07-01 03:37:00,272 - __main__ - INFO - Timeframe changed to: Last 2 Years
2025-07-01 03:37:00,278 - __main__ - INFO - Applied timeframe 'Last 2 Years': 2023-06-28 to 2025-06-27
2025-07-01 03:37:05,798 - __main__ - INFO - Starting optimization...
2025-07-01 03:37:05,815 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 03:37:05,824 - __main__ - INFO - Starting optimization with metric: total_profit
2025-07-01 03:37:05,828 - gui.strategy_manager - INFO - Optimization will use metric: total_profit
2025-07-01 03:37:05,829 - gui.strategy_manager - INFO - Creating walk-forward windows: train=360 days, test=90 days
2025-07-01 03:37:05,830 - gui.strategy_manager - INFO - Minimum bars: train=100, test=25
2025-07-01 03:37:05,830 - gui.strategy_manager - INFO - Window 1: Train 248 bars (2023-06-28 00:00:00 to 2024-06-21 00:00:00), Test 63 bars (2024-06-24 00:00:00 to 2024-09-20 00:00:00)
2025-07-01 03:37:05,832 - gui.strategy_manager - INFO - Window 2: Train 166 bars (2024-09-23 00:00:00 to 2025-05-21 00:00:00), Test 25 bars (2025-05-22 00:00:00 to 2025-06-27 00:00:00)
2025-07-01 03:37:05,833 - gui.strategy_manager - INFO - Walk-forward optimization: Created 2 windows
2025-07-01 03:37:22,952 - gui.strategy_manager - INFO - Walk-forward completed: 2 windows, avg performance: -498796.0449
2025-07-01 03:37:22,952 - gui.strategy_manager - INFO - Bayesian optimization completed. Best performance: -498796.0449
2025-07-01 03:37:22,959 - __main__ - INFO - Optimization completed. Best performance: -498796.0449
2025-07-01 03:37:22,968 - __main__ - INFO - Optimized parameters: {'LE': 2, 'SE': 15, 'LX': 13, 'SX': 1, 'ADXPeriod': 26}
2025-07-01 03:37:22,973 - __main__ - INFO - Running backtest with optimized parameters...
2025-07-01 03:37:23,075 - gui.strategy_manager - INFO - Updated strategy parameters: {'LE': 2.0, 'SE': 15.0, 'LX': 13.0, 'SX': 1.0, 'ADXPeriod': 26.0}
2025-07-01 03:37:23,099 - __main__ - INFO - Updated parameters: {'LE': 2.0, 'SE': 15.0, 'LX': 13.0, 'SX': 1.0, 'ADXPeriod': 26.0}
2025-07-01 03:37:23,112 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 03:37:23,691 - gui.metrics_calculator - INFO - Calculated metrics for 67 trades
2025-07-01 03:37:23,715 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 03:37:23,727 - __main__ - INFO - Using date range 2023-06-28 to 2025-06-27: 502 bars
2025-07-01 03:37:23,734 - __main__ - INFO - Calculated returns for 25 months from 2023-06 to 2025-06
2025-07-01 03:37:23,744 - __main__ - INFO - Backtest completed after optimization - metrics updated
2025-07-01 03:37:23,750 - __main__ - INFO - Starting strategy results logging...
2025-07-01 03:37:23,760 - __main__ - INFO - Strategy: 4BarCompare, Market: ^DJI, File: C:/Users/<USER>/Desktop/Data1/ETF/^DJI.csv
2025-07-01 03:37:23,770 - __main__ - INFO - Warning: Could not get parameter LE: 'DoubleVar' object is not subscriptable
2025-07-01 03:37:23,779 - __main__ - INFO - Warning: Could not get parameter SE: 'DoubleVar' object is not subscriptable
2025-07-01 03:37:23,788 - __main__ - INFO - Warning: Could not get parameter LX: 'DoubleVar' object is not subscriptable
2025-07-01 03:37:23,796 - __main__ - INFO - Warning: Could not get parameter SX: 'DoubleVar' object is not subscriptable
2025-07-01 03:37:23,805 - __main__ - INFO - Warning: Could not get parameter ADXPeriod: 'DoubleVar' object is not subscriptable
2025-07-01 03:37:23,808 - __main__ - INFO - Found 67 trades for logging
2025-07-01 03:37:23,813 - __main__ - INFO - Include trades setting: True
2025-07-01 03:37:23,817 - __main__ - INFO - Calling results logger with 5 parameters...
2025-07-01 03:37:23,900 - gui.strategy_results_logger - INFO - Saved 67 trades to CSV for run 4BarCompare_20250701_033723
2025-07-01 03:37:23,901 - gui.strategy_results_logger - INFO - Logged strategy run: 4BarCompare_20250701_033723
2025-07-01 03:37:23,907 - __main__ - INFO - Results logger returned run_id: 4BarCompare_20250701_033723
2025-07-01 03:37:23,913 - __main__ - INFO - Strategy results logged: 4BarCompare_20250701_033723
2025-07-01 03:37:24,029 - gui.strategy_results_viewer - INFO - Refreshed results table with 11 entries
2025-07-01 03:37:32,383 - __main__ - INFO - Added 4 data files to bulk backtest
2025-07-01 03:37:45,782 - __main__ - INFO - Added 1 strategy files to bulk backtest
2025-07-01 03:37:54,625 - __main__ - INFO - Added 1 strategy files to bulk backtest
2025-07-01 03:38:00,652 - __main__ - INFO - Added 0 strategy files to bulk backtest
2025-07-01 03:38:06,718 - __main__ - INFO - Bulk backtest completed with 4 results
2025-07-01 03:38:20,921 - __main__ - ERROR - Cleanup error: invalid command name ".!notebook.!frame8.!frame2.!text"
