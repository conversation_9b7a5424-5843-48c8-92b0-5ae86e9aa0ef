#!/usr/bin/env python3
"""
Test script to verify strategy results viewer functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from gui.strategy_results_viewer import StrategyResultsViewer
from gui.theme_manager import <PERSON><PERSON>anager

def test_results_viewer():
    """Test the strategy results viewer"""
    print("Testing Strategy Results Viewer...")
    
    # Create root window
    root = tk.Tk()
    root.title("Test Strategy Results Viewer")
    root.geometry("1200x800")
    
    # Create theme manager
    theme_manager = ThemeManager()
    
    # Create strategy results viewer
    viewer = StrategyResultsViewer(root, theme_manager)
    
    print("Strategy Results Viewer created successfully!")
    print("You can now test the viewer functionality in the GUI.")
    print("Close the window to exit the test.")
    
    # Run the GUI
    root.mainloop()

if __name__ == "__main__":
    test_results_viewer()
