import pandas as pd
from datetime import datetime, timedelta, time
from typing import List, Optional
from TradingSystem import Bar

class Tools:
    @staticmethod
    def read_csv(filename: str):
        print('Reading Bars...')
        # Read CSV file using pandas
        df = pd.read_csv(filename, parse_dates=['Date'])
        df.set_index('Date', inplace=True)
        bar = Bar(
                Date=df.index,
                Open=df['Open'],
                High=df['High'],
                Low=df['Low'],
                Close=df['Close'],
                Volume=df['Volume']
            )
        return bar

    @staticmethod
    def resample_bars(bars, interval_minutes: int):
        """Optimized bar resampling using vectorized operations."""
        if not bars:
            return []

        # More efficient data extraction using list comprehensions
        dates = [bar.Date for bar in bars]
        opens = [bar.Open for bar in bars]
        highs = [bar.High for bar in bars]
        lows = [bar.Low for bar in bars]
        closes = [bar.Close for bar in bars]
        volumes = [bar.Volume for bar in bars]

        # Create DataFrame directly with pre-extracted data
        df = pd.DataFrame({
            'Date': pd.to_datetime(dates),
            'Open': opens,
            'High': highs,
            'Low': lows,
            'Close': closes,
            'Volume': volumes
        })

        # Set datetime as index
        df.set_index('Date', inplace=True)

        # Resample with optimized aggregation
        resampled = df.resample(f'{interval_minutes}min').agg({
            'Open': 'first',
            'High': 'max',
            'Low': 'min',
            'Close': 'last',
            'Volume': 'sum'
        }).dropna()  # Remove any NaN rows

        # Convert back to list of Bar objects more efficiently
        resampled_bars = [
            Bar(date, row['Open'], row['High'], row['Low'], row['Close'], row['Volume'])
            for date, row in resampled.iterrows()
        ]

        return resampled_bars

    @staticmethod
    def map_bars(bar: int, original_bars: List[Bar], resampled_bars: List[Bar], interval_minutes: int) -> Optional[int]:
        original_time = original_bars[bar].Date

        # Perform binary search to find the corresponding resampled bar
        left = 0
        right = len(resampled_bars) - 1
        result = -1

        while left <= right:
            mid = left + (right - left) // 2
            resampled_time = resampled_bars[mid].Date

            if (original_time - resampled_time) >= timedelta(seconds=0) and (original_time - resampled_time) < timedelta(minutes=interval_minutes):
                result = mid  # Found a matching resampled bar
                break
            elif (original_time - resampled_time) < timedelta(seconds=0):
                right = mid - 1  # Search in the left half
            else:
                left = mid + 1  # Search in the right half

        return result if result != -1 else None

    class Datetime:
        def __init__(self, year: int, month: int, day: int, hour: int, minute: int, second: int):
            self.year = year
            self.month = month
            self.day = day
            self.hour = hour
            self.minute = minute
            self.second = second

    @staticmethod
    def check_time(bar_time: time, operator: str, target_time: str) -> bool:
        # Parse the target time string into a datetime object
        target_dt = datetime.strptime(target_time, "%H:%M:%S").time()

        if operator == "<":
            return bar_time < target_dt
        elif operator == ">":
            return bar_time > target_dt
        elif operator == "<=":
            return bar_time <= target_dt
        elif operator == ">=":
            return bar_time >= target_dt
        elif operator == "<":
            return bar_time < target_dt
        elif operator == ">":
            return bar_time > target_dt
        elif operator == "==":
            return bar_time == target_dt
        else:
            raise ValueError("Invalid operator. Supported operators are '<=', '>=', '<', '>', '=='.")